fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {get_environment_group}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {get_environment_group}: Handler<_, _>
