fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, Query<LogsQuery>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<LogsResponse>>, ControllerError>> {get_application_logs}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, Query<LogsQuery>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<LogsResponse>>, ControllerError>> {get_application_logs}: Handler<_, _>
