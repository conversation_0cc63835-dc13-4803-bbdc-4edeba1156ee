fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<DeploymentResponse>>, ControllerError>> {rollback_deployment}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<DeploymentResponse>>, ControllerError>> {rollback_deployment}: Handler<_, _>
