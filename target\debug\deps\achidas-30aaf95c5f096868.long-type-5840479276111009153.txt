fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::<PERSON><PERSON><SetSecretRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {set_secret}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<SetSecretRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {set_secret}: Handler<_, _>
