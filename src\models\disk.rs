use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Disk {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub size_gb: i32,
    pub mount_path: String,
    pub service_id: ObjectId,
    pub user_id: ObjectId,
    pub infrastructure_id: String, // Vultr block storage ID
    pub status: DiskStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum DiskStatus {
    Creating,
    Available,
    InUse,
    Resizing,
    Restoring,
    Error,
    Deleting,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DiskSnapshot {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub disk_id: ObjectId,
    pub snapshot_key: String,
    pub size_gb: i32,
    pub created_at: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub expires_at: Option<DateTime<Utc>>,
}

// Pagination support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

impl Default for Pagination {
    fn default() -> Self {
        Self {
            page: Some(1),
            limit: Some(20),
        }
    }
}
