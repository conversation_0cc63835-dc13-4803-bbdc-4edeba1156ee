fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, Query<models::PaginationQuery>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<DeploymentResponse>>>, ControllerError>> {list_deployments}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, Query<models::PaginationQuery>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<DeploymentResponse>>>, ControllerError>> {list_deployments}: Handler<_, _>
