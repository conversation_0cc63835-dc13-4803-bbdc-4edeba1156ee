use crate::{
    config::Config,
    database::Database,
    infrastructure::{
        CircuitBreakerService, CircuitBreakerAware, MetricsService, RateLimiterService
    },
    models::{DomainStatus},
    services::{ServiceError, ServiceResult},
    vultr::VultrClient,
    with_circuit_breaker,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::Collection;
use serde::{Deserialize, Serialize};
use tracing::{error, info, instrument, warn};
use uuid::Uuid;

// Domain models that should be in models/domain.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomDomain {
    pub id: Option<ObjectId>,
    pub name: String,
    pub service_id: ObjectId,
    pub user_id: ObjectId,
    pub status: DomainStatus,
    pub verification: DomainVerification,
    pub ssl_certificate: Option<SSLCertificate>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainVerification {
    pub dns_record: String,
    pub dns_value: String,
    pub http_path: String,
    pub http_content: String,
    pub verified: bool,
    pub verified_at: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SSLCertificate {
    pub certificate: String,
    pub private_key: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateDomainRequest {
    pub name: String,
    pub service_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainResponse {
    pub id: String,
    pub name: String,
    pub service_id: String,
    pub status: DomainStatus,
    pub verification: DomainVerification,
    pub ssl_certificate: Option<SSLCertificate>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerifyDomainRequest {
    pub verification_method: String, // "dns" or "http"
}

pub struct DomainService<'a> {
    domains: Collection<CustomDomain>,
    vultr_client: &'a VultrClient,
    config: &'a Config,
    circuit_breaker: CircuitBreakerService,
    metrics: MetricsService,
    rate_limiter: RateLimiterService,
}

impl<'a> DomainService<'a> {
    pub fn new(database: &Database, vultr_client: &'a VultrClient, config: &'a Config) -> Self {
        let circuit_breaker = CircuitBreakerService::new();
        let metrics = MetricsService::new();
        let rate_limiter = RateLimiterService::new();

        Self {
            domains: database.collection("custom_domains"),
            vultr_client,
            config,
            circuit_breaker,
            metrics,
            rate_limiter,
        }
    }

    /// Add a custom domain to a service
    #[instrument(skip(self))]
    pub async fn create_domain(&self, user_id: &str, request: CreateDomainRequest) -> ServiceResult<DomainResponse> {
        // Rate limit domain creation
        self.rate_limiter.check_api_rate_limit(user_id).await?;

        // Validate domain name
        self.validate_domain_name(&request.name)?;

        // Validate service exists and user has access
        self.validate_service_access(user_id, &request.service_id).await?;

        // Check if domain already exists
        let existing = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.domains
                    .find_one(doc! { "name": &request.name }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))
            }
        )?;

        if existing.is_some() {
            return Err(ServiceError::Conflict("Domain already exists".to_string()));
        }

        // Generate verification tokens
        let verification = DomainVerification {
            dns_record: format!("_render-challenge.{}", request.name),
            dns_value: Uuid::new_v4().to_string(),
            http_path: format!("/.well-known/render-challenge/{}", Uuid::new_v4()),
            http_content: Uuid::new_v4().to_string(),
            verified: false,
            verified_at: None,
        };

        let domain = CustomDomain {
            id: None,
            name: request.name.clone(),
            service_id: ObjectId::parse_str(&request.service_id)
                .map_err(|_| ServiceError::Validation("Invalid service ID".to_string()))?,
            user_id: ObjectId::parse_str(user_id)
                .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?,
            status: DomainStatus::Pending,
            verification: verification.clone(),
            ssl_certificate: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.domains.insert_one(&domain, None).await
                    .map_err(|e| ServiceError::Database(e))
            }
        )?;

        let domain_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get domain ID".to_string()))?;

        self.metrics.record_custom_metric("domains_created", 1.0, vec![]);

        Ok(DomainResponse {
            id: domain_id.to_hex(),
            name: domain.name,
            service_id: request.service_id,
            status: domain.status,
            verification,
            ssl_certificate: domain.ssl_certificate,
            created_at: domain.created_at,
            updated_at: domain.updated_at,
        })
    }

    /// Verify domain ownership
    #[instrument(skip(self))]
    pub async fn verify_domain(
        &self,
        user_id: &str,
        domain_id: &str,
        request: VerifyDomainRequest,
    ) -> ServiceResult<DomainResponse> {
        let domain_object_id = ObjectId::parse_str(domain_id)
            .map_err(|_| ServiceError::Validation("Invalid domain ID".to_string()))?;

        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Get domain
        let domain = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.domains
                    .find_one(doc! { "_id": domain_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Domain not found".to_string()))
            }
        )?;

        // Perform verification based on method
        let verification_result = match request.verification_method.as_str() {
            "dns" => self.verify_dns_record(&domain).await,
            "http" => self.verify_http_challenge(&domain).await,
            _ => Err(ServiceError::Validation("Invalid verification method".to_string())),
        }?;

        if verification_result {
            // Update domain as verified
            let mut updated_verification = domain.verification.clone();
            updated_verification.verified = true;
            updated_verification.verified_at = Some(Utc::now());

            with_circuit_breaker!(
                self.circuit_breaker,
                "database",
                {
                    self.domains
                        .update_one(
                            doc! { "_id": domain_object_id },
                            doc! { 
                                "$set": { 
                                    "status": "verified",
                                    "verification.verified": true,
                                    "verification.verified_at": Utc::now(),
                                    "updated_at": Utc::now()
                                }
                            },
                            None,
                        )
                        .await
                        .map_err(|e| ServiceError::Database(e))?;
                    Ok::<(), ServiceError>(())
                }
            )?;

            // Start SSL certificate provisioning
            self.provision_ssl_certificate(domain_object_id).await?;

            self.metrics.record_custom_metric("domains_verified", 1.0, vec![]);
            info!("Domain {} verified successfully", domain.name);
        } else {
            return Err(ServiceError::Validation("Domain verification failed".to_string()));
        }

        self.get_domain(user_id, domain_id).await
    }

    /// Get domain by ID
    #[instrument(skip(self))]
    pub async fn get_domain(&self, user_id: &str, domain_id: &str) -> ServiceResult<DomainResponse> {
        let domain_object_id = ObjectId::parse_str(domain_id)
            .map_err(|_| ServiceError::Validation("Invalid domain ID".to_string()))?;

        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let domain = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.domains
                    .find_one(doc! { "_id": domain_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Domain not found".to_string()))
            }
        )?;

        Ok(DomainResponse {
            id: domain.id.unwrap().to_hex(),
            name: domain.name,
            service_id: domain.service_id.to_hex(),
            status: domain.status,
            verification: domain.verification,
            ssl_certificate: domain.ssl_certificate,
            created_at: domain.created_at,
            updated_at: domain.updated_at,
        })
    }

    /// Delete custom domain
    #[instrument(skip(self))]
    pub async fn delete_domain(&self, user_id: &str, domain_id: &str) -> ServiceResult<()> {
        let domain_object_id = ObjectId::parse_str(domain_id)
            .map_err(|_| ServiceError::Validation("Invalid domain ID".to_string()))?;

        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Get domain
        let domain = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.domains
                    .find_one(doc! { "_id": domain_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Domain not found".to_string()))
            }
        )?;

        // Remove SSL certificate if exists
        if domain.ssl_certificate.is_some() {
            self.revoke_ssl_certificate(&domain.name).await?;
        }

        // Remove from load balancer/CDN
        self.remove_domain_from_infrastructure(&domain.name).await?;

        // Delete from database
        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.domains
                    .delete_one(doc! { "_id": domain_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok::<(), ServiceError>(())
            }
        )?;

        self.metrics.record_custom_metric("domains_deleted", 1.0, vec![]);
        info!("Domain {} deleted successfully", domain.name);
        Ok(())
    }

    // Private helper methods
    fn validate_domain_name(&self, domain: &str) -> ServiceResult<()> {
        // Basic domain validation
        if domain.is_empty() || domain.len() > 253 {
            return Err(ServiceError::Validation("Invalid domain name length".to_string()));
        }

        if !domain.contains('.') {
            return Err(ServiceError::Validation("Domain must contain at least one dot".to_string()));
        }

        // Check for valid characters
        if !domain.chars().all(|c| c.is_alphanumeric() || c == '.' || c == '-') {
            return Err(ServiceError::Validation("Domain contains invalid characters".to_string()));
        }

        Ok(())
    }

    async fn validate_service_access(&self, user_id: &str, service_id: &str) -> ServiceResult<()> {
        // In production, would verify the service exists and user has access
        info!("Validating service access for user {} to service {}", user_id, service_id);
        Ok(())
    }

    async fn verify_dns_record(&self, domain: &CustomDomain) -> ServiceResult<bool> {
        // In production, would perform actual DNS lookup
        info!("Verifying DNS record for domain: {}", domain.name);
        info!("Expected DNS record: {} TXT {}", domain.verification.dns_record, domain.verification.dns_value);
        
        // Simulate DNS verification
        Ok(true)
    }

    async fn verify_http_challenge(&self, domain: &CustomDomain) -> ServiceResult<bool> {
        // In production, would perform HTTP request to verify challenge
        info!("Verifying HTTP challenge for domain: {}", domain.name);
        info!("Expected path: {} with content: {}", domain.verification.http_path, domain.verification.http_content);
        
        // Simulate HTTP verification
        Ok(true)
    }

    async fn provision_ssl_certificate(&self, domain_id: ObjectId) -> ServiceResult<()> {
        // Start async SSL certificate provisioning
        tokio::spawn(async move {
            // Simulate certificate provisioning time
            tokio::time::sleep(std::time::Duration::from_secs(60)).await;
            info!("SSL certificate provisioned for domain: {}", domain_id);
        });

        Ok(())
    }

    async fn revoke_ssl_certificate(&self, domain_name: &str) -> ServiceResult<()> {
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                info!("Revoking SSL certificate for domain: {}", domain_name);
                // In production, would revoke actual SSL certificate
                Ok(())
            }
        )
    }

    async fn remove_domain_from_infrastructure(&self, domain_name: &str) -> ServiceResult<()> {
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                info!("Removing domain from infrastructure: {}", domain_name);
                // In production, would remove from load balancer/CDN
                Ok(())
            }
        )
    }
}

impl<'a> CircuitBreakerAware for DomainService<'a> {
    fn circuit_breaker(&self) -> &CircuitBreakerService {
        &self.circuit_breaker
    }
}
