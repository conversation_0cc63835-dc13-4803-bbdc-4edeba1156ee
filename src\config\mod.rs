use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub server_address: String,
    pub database_url: String,
    pub vultr_api_key: String,
    pub jwt_secret: String,
    pub jaeger_endpoint: Option<String>,
    pub prometheus_endpoint: String,
    pub log_level: String,
    pub environment: Option<String>,
    pub retry_config: RetryConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RetryConfig {
    pub max_retries: u32,
    pub initial_delay_ms: u64,
    pub max_delay_ms: u64,
    pub multiplier: f64,
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            initial_delay_ms: 100,
            max_delay_ms: 30000,
            multiplier: 2.0,
            jitter: true,
        }
    }
}

impl Config {
    pub fn load() -> Result<Self> {
        dotenvy::dotenv().ok();
        
        let config = Self {
            server_address: env::var("SERVER_ADDRESS")
                .unwrap_or_else(|_| "0.0.0.0:3000".to_string()),
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "mongodb://localhost:27017/achidas".to_string()),
            vultr_api_key: env::var("VULTR_API_KEY")
                .expect("VULTR_API_KEY environment variable is required"),
            jwt_secret: env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-secret-key".to_string()),
            jaeger_endpoint: env::var("JAEGER_ENDPOINT").ok(),
            prometheus_endpoint: env::var("PROMETHEUS_ENDPOINT")
                .unwrap_or_else(|_| "0.0.0.0:9090".to_string()),
            log_level: env::var("LOG_LEVEL")
                .unwrap_or_else(|_| "info".to_string()),
            environment: env::var("ENVIRONMENT").ok(),
            retry_config: RetryConfig::default(),
        };
        
        Ok(config)
    }
}
