use crate::{
    controllers::{success_response, ControllerResult},
    middleware::auth::get_current_user,
    models::{UserProfile, ApiResponse},
    services::{user::UserService, billing::BillingService},
    AppState,
};
use axum::{extract::{Request, State}, Json, body::Body};
use std::sync::Arc;
use tracing::instrument;
use serde::{Deserialize, Serialize};

#[instrument(skip(state, req))]
pub async fn get_profile(
    State(state): State<Arc<AppState>>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    let claims = get_current_user(&req)?;

    let user_service = UserService::new(&state.database);
    let user_profile = user_service.get_user_profile(&claims.sub).await?;

    Ok(success_response(user_profile))
}

#[derive(Debug, Serialize)]
pub struct AccountInfo {
    pub name: String,
    pub email: String,
    pub balance: f64,
    pub pending_charges: f64,
    pub last_payment_date: Option<chrono::DateTime<chrono::Utc>>,
    pub last_payment_amount: Option<f64>,
    pub servers_count: u32,
    pub applications_count: u32,
    pub hosting_tier: String,
}

#[derive(Debug, Serialize)]
pub struct AccountUsage {
    pub bandwidth: BandwidthUsage,
    pub current_month_usage: f64,
    pub previous_month_usage: f64,
    pub date_range: DateRange,
}

#[derive(Debug, Serialize)]
pub struct BandwidthUsage {
    pub total_gb: f64,
    pub incoming_gb: f64,
    pub outgoing_gb: f64,
}

#[derive(Debug, Serialize)]
pub struct DateRange {
    pub start_date: chrono::DateTime<chrono::Utc>,
    pub end_date: chrono::DateTime<chrono::Utc>,
}

#[instrument(skip(state, req))]
pub async fn get_account_info(
    State(state): State<Arc<AppState>>,
    req: Request,
) -> ControllerResult<Json<ApiResponse<AccountInfo>>> {
    let claims = get_current_user(&req)?;

    let user_service = UserService::new(&state.database);
    let billing_service = BillingService::new(&state.database);

    // Get user profile
    let user_profile = user_service.get_user_profile(&claims.sub).await?;

    // Get billing info
    let billing_info = billing_service.get_billing_info(&claims.sub).await?;

    // Get user's servers and applications count (you'll need to implement these)
    let servers_count = user_service.get_user_servers_count(&claims.sub).await.unwrap_or(0);
    let applications_count = user_service.get_user_applications_count(&claims.sub).await.unwrap_or(0);

    let account_info = AccountInfo {
        name: format!("{} {}", user_profile.first_name, user_profile.last_name),
        email: user_profile.email,
        balance: billing_info.account.balance,
        pending_charges: billing_info.account.pending_charges,
        last_payment_date: billing_info.account.last_payment_date,
        last_payment_amount: None, // You can add this to billing service
        servers_count,
        applications_count,
        hosting_tier: "Shared".to_string(), // You can make this dynamic based on user's plan
    };

    Ok(success_response(account_info))
}

#[instrument(skip(state, req))]
pub async fn get_account_usage(
    State(state): State<Arc<AppState>>,
    req: Request,
) -> ControllerResult<Json<ApiResponse<AccountUsage>>> {
    let claims = get_current_user(&req)?;

    let billing_service = BillingService::new(&state.database);

    // Get current month usage
    let now = chrono::Utc::now();
    let start_of_month = chrono::DateTime::from_utc(
        chrono::NaiveDate::from_ymd_opt(now.year(), now.month(), 1)
            .unwrap()
            .and_hms_opt(0, 0, 0)
            .unwrap(),
        chrono::Utc,
    );

    let usage_params = serde_json::json!({
        "start_date": start_of_month.format("%Y-%m-%d").to_string(),
        "end_date": now.format("%Y-%m-%d").to_string()
    });

    let usage_data = billing_service.get_usage_data(&claims.sub, usage_params).await?;

    // Extract bandwidth data (you'll need to implement proper bandwidth tracking)
    let bandwidth_usage = BandwidthUsage {
        total_gb: usage_data.get("total_bandwidth_gb").and_then(|v| v.as_f64()).unwrap_or(0.0),
        incoming_gb: usage_data.get("incoming_gb").and_then(|v| v.as_f64()).unwrap_or(0.0),
        outgoing_gb: usage_data.get("outgoing_gb").and_then(|v| v.as_f64()).unwrap_or(0.0),
    };

    let account_usage = AccountUsage {
        bandwidth: bandwidth_usage,
        current_month_usage: usage_data.get("current_month_cost").and_then(|v| v.as_f64()).unwrap_or(0.0),
        previous_month_usage: usage_data.get("previous_month_cost").and_then(|v| v.as_f64()).unwrap_or(0.0),
        date_range: DateRange {
            start_date: start_of_month,
            end_date: now,
        },
    };

    Ok(success_response(account_usage))
}

#[instrument(skip(state, req, update_data))]
pub async fn update_profile(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Json(update_data): Json<serde_json::Value>,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    let claims = get_current_user(&req)?;

    let user_service = UserService::new(&state.database);
    let user_profile = user_service
        .update_user_profile(&claims.sub, update_data)
        .await?;

    Ok(success_response(user_profile))
}
