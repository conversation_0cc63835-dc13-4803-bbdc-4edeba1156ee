openapi: 3.0.3
info:
  title: Achidas Cloud Platform API
  description: |
    Comprehensive API for the Achidas Cloud Platform - A Render-like cloud service leveraging Vultr infrastructure.
    
    ## Authentication
    Most endpoints require JWT authentication. Include the token in the Authorization header:
    `Authorization: Bearer <your_jwt_token>`
    
    ## Response Format
    All responses follow a consistent format:
    ```json
    {
      "success": true,
      "data": {...},
      "message": "Optional message",
      "error": "Optional error message",
      "meta": {
        "timestamp": "2024-01-01T00:00:00Z",
        "request_id": "uuid",
        "version": "v1"
      }
    }
    ```
  version: 1.0.0
  contact:
    name: Achidas API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000
    description: Local development server
  - url: https://api.achidas.com
    description: Production server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
        message:
          type: string
        error:
          type: string
        meta:
          $ref: '#/components/schemas/ApiResponseMeta'
      required:
        - success
        - meta

    ApiResponseMeta:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        request_id:
          type: string
          format: uuid
        version:
          type: string
          example: "v1"

    PaginationQuery:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
        per_page:
          type: integer
          minimum: 1
          maximum: 100
          default: 20

    PaginationMeta:
      type: object
      properties:
        page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        total_pages:
          type: integer

    # User Models
    CreateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          minLength: 8
          example: "securepassword123"
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"
        company:
          type: string
          example: "Acme Corp"
      required:
        - email
        - password
        - first_name
        - last_name

    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          example: "securepassword123"
      required:
        - email
        - password

    LoginResponse:
      type: object
      properties:
        token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        user:
          $ref: '#/components/schemas/UserProfile'
        expires_at:
          type: string
          format: date-time

    UserProfile:
      type: object
      properties:
        id:
          type: string
          example: "507f1f77bcf86cd799439011"
        email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
        company:
          type: string
        role:
          type: string
          enum: [Admin, User, Billing]
        status:
          type: string
          enum: [Active, Inactive, Suspended, PendingVerification]
        created_at:
          type: string
          format: date-time
        last_login:
          type: string
          format: date-time

    # Application Models
    CreateApplicationRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          example: "my-web-app"
        description:
          type: string
          example: "My awesome web application"
        repository:
          $ref: '#/components/schemas/CreateRepositoryRequest'
        environment:
          $ref: '#/components/schemas/CreateEnvironmentRequest'
        runtime_config:
          $ref: '#/components/schemas/CreateRuntimeConfigRequest'
      required:
        - name
        - repository
        - environment
        - runtime_config

    CreateRepositoryRequest:
      type: object
      properties:
        provider:
          type: string
          enum: [GitHub, GitLab, Bitbucket]
        url:
          type: string
          format: uri
          example: "https://github.com/user/repo.git"
        branch:
          type: string
          example: "main"
        auto_deploy:
          type: boolean
          default: true
        access_token:
          type: string
          description: "Personal access token for private repositories"
      required:
        - provider
        - url
        - branch

    CreateEnvironmentRequest:
      type: object
      properties:
        variables:
          type: object
          additionalProperties:
            type: string
          example:
            NODE_ENV: "production"
            PORT: "3000"
        secrets:
          type: object
          additionalProperties:
            type: string
          example:
            DATABASE_URL: "mongodb://..."
            API_KEY: "secret-key"

    CreateRuntimeConfigRequest:
      type: object
      properties:
        runtime:
          type: string
          enum: [Node, Python, Go, Rust, PHP, Ruby, Java, Docker]
        version:
          type: string
          example: "18"
        build_command:
          type: string
          example: "npm run build"
        start_command:
          type: string
          example: "npm start"
        install_command:
          type: string
          example: "npm install"
        health_check_path:
          type: string
          example: "/health"
        port:
          type: integer
          default: 3000

    ApplicationResponse:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [Active, Inactive, Building, Deploying, Failed]
        repository:
          type: object
        environment:
          type: object
        runtime_config:
          type: object
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # Instance Models
    CreateInstanceRequest:
      type: object
      properties:
        name:
          type: string
          example: "my-server"
        region:
          type: string
          example: "ewr"
        plan:
          type: string
          example: "vc2-1c-1gb"
        os:
          type: string
          example: "ubuntu-20.04"
        ssh_key_ids:
          type: array
          items:
            type: string
        startup_script_id:
          type: string
        tags:
          type: array
          items:
            type: string
      required:
        - region
        - plan

    InstanceResponse:
      type: object
      properties:
        id:
          type: string
        user_id:
          type: string
        vultr_instance_id:
          type: string
        name:
          type: string
        region:
          type: string
        plan:
          type: string
        os:
          type: string
        status:
          type: string
          enum: [Pending, Installing, Running, Stopped, Suspended, Destroyed, Error]
        ip_address:
          type: string
        internal_ip:
          type: string
        vcpu_count:
          type: integer
        ram:
          type: integer
        disk:
          type: integer
        bandwidth:
          type: integer
        monthly_cost:
          type: number
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        tags:
          type: array
          items:
            type: string

paths:
  # Health Check
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check if the API is running
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/health:
    get:
      tags:
        - Health
      summary: Versioned health check
      description: Check if the API is running (versioned endpoint)
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # Authentication
  /api/v1/auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserProfile'
        '400':
          description: Validation error
        '409':
          description: User already exists

  /api/v1/auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
        '400':
          description: Validation error

  /api/v1/auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh JWT token
      description: Refresh an expired JWT token
      security: []
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid refresh token
        '501':
          description: Not implemented yet

  # Applications
  /api/v1/applications:
    get:
      tags:
        - Applications
      summary: List applications
      description: Get a paginated list of user applications
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Applications retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              $ref: '#/components/schemas/ApplicationResponse'
                          meta:
                            $ref: '#/components/schemas/PaginationMeta'

    post:
      tags:
        - Applications
      summary: Create application
      description: Create a new application
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApplicationRequest'
      responses:
        '201':
          description: Application created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ApplicationResponse'
        '400':
          description: Validation error

  /api/v1/applications/{app_id}:
    get:
      tags:
        - Applications
      summary: Get application
      description: Get application details by ID
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Application retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ApplicationResponse'
        '404':
          description: Application not found

    put:
      tags:
        - Applications
      summary: Update application
      description: Update application configuration
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApplicationRequest'
      responses:
        '200':
          description: Application updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ApplicationResponse'
        '404':
          description: Application not found

    delete:
      tags:
        - Applications
      summary: Delete application
      description: Delete an application and all its resources
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Application deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Application not found

  /api/v1/applications/{app_id}/deploy:
    post:
      tags:
        - Applications
      summary: Trigger deployment
      description: Trigger a new deployment for the application
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                branch:
                  type: string
                  example: "main"
                commit_sha:
                  type: string
                  example: "abc123def456"
                force_rebuild:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Deployment triggered successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          deployment_id:
                            type: string
                          status:
                            type: string
        '404':
          description: Application not found

  # Instances
  /api/v1/instances:
    get:
      tags:
        - Instances
      summary: List instances
      description: Get a paginated list of user instances
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Instances retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              $ref: '#/components/schemas/InstanceResponse'
                          meta:
                            $ref: '#/components/schemas/PaginationMeta'

    post:
      tags:
        - Instances
      summary: Create instance
      description: Create a new cloud instance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInstanceRequest'
      responses:
        '201':
          description: Instance created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/InstanceResponse'
        '400':
          description: Validation error

  /api/v1/instances/{instance_id}:
    get:
      tags:
        - Instances
      summary: Get instance
      description: Get instance details by ID
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/InstanceResponse'
        '404':
          description: Instance not found

    put:
      tags:
        - Instances
      summary: Update instance
      description: Update instance configuration
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                label:
                  type: string
                tag:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Instance updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/InstanceResponse'
        '404':
          description: Instance not found

    delete:
      tags:
        - Instances
      summary: Delete instance
      description: Delete an instance permanently
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Instance not found

  /api/v1/instances/{instance_id}/start:
    post:
      tags:
        - Instances
      summary: Start instance
      description: Start a stopped instance
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance start initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Instance not found

  /api/v1/instances/{instance_id}/stop:
    post:
      tags:
        - Instances
      summary: Stop instance
      description: Stop a running instance
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance stop initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Instance not found

  /api/v1/instances/{instance_id}/restart:
    post:
      tags:
        - Instances
      summary: Restart instance
      description: Restart an instance
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance restart initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Instance not found

  # Billing
  /api/v1/billing:
    get:
      tags:
        - Billing
      summary: Get billing information
      description: Get current billing information for the user
      responses:
        '200':
          description: Billing information retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          current_balance:
                            type: number
                          pending_charges:
                            type: number
                          last_payment_date:
                            type: string
                            format: date-time
                          next_billing_date:
                            type: string
                            format: date-time

  /api/v1/billing/invoices:
    get:
      tags:
        - Billing
      summary: List invoices
      description: Get a paginated list of user invoices
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Invoices retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: string
                                amount:
                                  type: number
                                status:
                                  type: string
                                date:
                                  type: string
                                  format: date-time
                          meta:
                            $ref: '#/components/schemas/PaginationMeta'

  /api/v1/billing/usage:
    get:
      tags:
        - Billing
      summary: Get usage data
      description: Get current usage statistics
      parameters:
        - name: start_date
          in: query
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Usage data retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          compute_hours:
                            type: number
                          bandwidth_gb:
                            type: number
                          storage_gb:
                            type: number
                          estimated_cost:
                            type: number

  # Hosting Platform
  /api/v1/hosting/plans:
    get:
      tags:
        - Hosting
      summary: Get hosting plans
      description: Get all available hosting plans
      security: []
      responses:
        '200':
          description: Hosting plans retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            name:
                              type: string
                              example: "nano"
                            display_name:
                              type: string
                              example: "Nano Plan"
                            vcpu:
                              type: integer
                            ram_mb:
                              type: integer
                            disk_gb:
                              type: integer
                            bandwidth_gb:
                              type: integer
                            price_monthly:
                              type: number
                            price_hourly:
                              type: number

  /api/v1/hosting/plans/{plan_name}:
    get:
      tags:
        - Hosting
      summary: Get hosting plan details
      description: Get details for a specific hosting plan
      security: []
      parameters:
        - name: plan_name
          in: path
          required: true
          schema:
            type: string
            example: "nano"
      responses:
        '200':
          description: Hosting plan details retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          name:
                            type: string
                          display_name:
                            type: string
                          vcpu:
                            type: integer
                          ram_mb:
                            type: integer
                          disk_gb:
                            type: integer
                          bandwidth_gb:
                            type: integer
                          price_monthly:
                            type: number
                          price_hourly:
                            type: number
                          features:
                            type: array
                            items:
                              type: string
        '404':
          description: Plan not found

  /api/v1/hosting/deploy/user/{user_id}:
    post:
      tags:
        - Hosting
      summary: Deploy user to hosting
      description: Deploy a user to the intelligent hosting platform
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                plan:
                  type: string
                  example: "nano"
                region:
                  type: string
                  example: "ewr"
                config:
                  type: object
                  properties:
                    auto_scale:
                      type: boolean
                    max_instances:
                      type: integer
              required:
                - plan
                - region
      responses:
        '201':
          description: User deployment initiated
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          deployment_id:
                            type: string
                          status:
                            type: string
                          estimated_time:
                            type: string

  /api/v1/hosting/infrastructure/status:
    get:
      tags:
        - Hosting
      summary: Get infrastructure status
      description: Get current infrastructure status and metrics
      responses:
        '200':
          description: Infrastructure status retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          total_servers:
                            type: integer
                          active_servers:
                            type: integer
                          total_users:
                            type: integer
                          resource_utilization:
                            type: object
                            properties:
                              cpu_percent:
                                type: number
                              memory_percent:
                                type: number
                              disk_percent:
                                type: number

  # Webhooks
  /api/v1/webhooks/github:
    post:
      tags:
        - Webhooks
      summary: GitHub webhook
      description: Handle GitHub webhook events
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: GitHub webhook payload
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/webhooks/gitlab:
    post:
      tags:
        - Webhooks
      summary: GitLab webhook
      description: Handle GitLab webhook events
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: GitLab webhook payload
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/webhooks/git/{app_id}:
    post:
      tags:
        - Webhooks
      summary: Application-specific webhook
      description: Handle webhook events for a specific application
      security: []
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: Git webhook payload
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # Infrastructure
  /api/v1/account:
    get:
      tags:
        - Infrastructure
      summary: Get account information
      description: Get Vultr account information
      responses:
        '200':
          description: Account information retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          name:
                            type: string
                          email:
                            type: string
                          balance:
                            type: number
                          pending_charges:
                            type: number

  /api/v1/regions:
    get:
      tags:
        - Infrastructure
      summary: List regions
      description: Get all available regions
      security: []
      responses:
        '200':
          description: Regions retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            city:
                              type: string
                            country:
                              type: string
                            continent:
                              type: string
                            options:
                              type: array
                              items:
                                type: string

  /api/v1/plans:
    get:
      tags:
        - Infrastructure
      summary: List plans
      description: Get all available server plans
      security: []
      responses:
        '200':
          description: Plans retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            vcpu_count:
                              type: integer
                            ram:
                              type: integer
                            disk:
                              type: integer
                            bandwidth:
                              type: integer
                            monthly_cost:
                              type: number
                            type:
                              type: string

  /api/v1/ssh-keys:
    get:
      tags:
        - Infrastructure
      summary: List SSH keys
      description: Get all SSH keys
      responses:
        '200':
          description: SSH keys retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            name:
                              type: string
                            ssh_key:
                              type: string
                            date_created:
                              type: string
                              format: date-time

    post:
      tags:
        - Infrastructure
      summary: Create SSH key
      description: Add a new SSH key
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: "my-ssh-key"
                ssh_key:
                  type: string
                  example: "ssh-rsa AAAAB3NzaC1yc2E..."
              required:
                - name
                - ssh_key
      responses:
        '201':
          description: SSH key created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string

  # Admin
  /api/v1/admin/users:
    get:
      tags:
        - Admin
      summary: List all users (Admin)
      description: Get a paginated list of all users (admin only)
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              $ref: '#/components/schemas/UserProfile'
                          meta:
                            $ref: '#/components/schemas/PaginationMeta'
        '403':
          description: Forbidden - Admin access required

  /api/v1/admin/users/{user_id}:
    get:
      tags:
        - Admin
      summary: Get user details (Admin)
      description: Get detailed user information (admin only)
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User details retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserProfile'
        '403':
          description: Forbidden - Admin access required
        '404':
          description: User not found

  /api/v1/admin/users/{user_id}/suspend:
    post:
      tags:
        - Admin
      summary: Suspend user (Admin)
      description: Suspend a user account (admin only)
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  example: "Terms of service violation"
              required:
                - reason
      responses:
        '200':
          description: User suspended successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Forbidden - Admin access required
        '404':
          description: User not found

  /api/v1/admin/metrics:
    get:
      tags:
        - Admin
      summary: Get system metrics (Admin)
      description: Get system-wide metrics and statistics (admin only)
      responses:
        '200':
          description: Metrics retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          total_users:
                            type: integer
                          active_instances:
                            type: integer
                          total_deployments:
                            type: integer
                          system_health:
                            type: object
                            properties:
                              cpu_usage:
                                type: number
                              memory_usage:
                                type: number
                              disk_usage:
                                type: number
        '403':
          description: Forbidden - Admin access required

tags:
  - name: Health
    description: Health check endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Applications
    description: Application management
  - name: Instances
    description: Cloud instance management
  - name: Billing
    description: Billing and usage information
  - name: Hosting
    description: Intelligent hosting platform
  - name: Infrastructure
    description: Cloud infrastructure management
  - name: Webhooks
    description: Webhook endpoints
  - name: Admin
    description: Administrative endpoints
