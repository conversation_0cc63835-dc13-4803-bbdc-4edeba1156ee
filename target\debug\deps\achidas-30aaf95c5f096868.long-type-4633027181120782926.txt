fn(axum::extract::State<Arc<AppState>>, {type error}, axum::J<PERSON><CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<ApplicationResponse>>, ControllerError>> {create_application}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::Json<CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<ApplicationResponse>>, ControllerError>> {create_application}: Handler<_, _>
