fn(axum::extract::State<Arc<AppState>>, {type error}, Query<models::PaginationQuery>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<ApplicationResponse>>>, ControllerError>> {list_applications}
fn(axum::extract::State<Arc<AppState>>, {type error}, Query<models::PaginationQuery>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<ApplicationResponse>>>, ControllerError>> {list_applications}: Handler<_, _>
