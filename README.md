# Achidas Cloud Platform

## 🌍 Africa's Premier Cloud Hosting Platform

Achidas is a Render-like cloud platform service specifically designed for the African market, leveraging Vultr infrastructure to provide affordable, scalable, and reliable hosting solutions.

## 🚀 What is Achidas?

Achidas bridges the gap between expensive cloud services and the African market by providing:

- **Affordable Pay-as-you-go Pricing** - Optimized for African economies
- **African-Focused Regions** - Servers in major African cities
- **Render-like Experience** - Familiar interface for developers
- **Vultr Infrastructure** - Enterprise-grade reliability
- **Local Currency Support** - Pricing in local African currencies

## 🎯 Target Market

- **African Developers** - Building applications for local markets
- **Startups** - Need affordable, scalable infrastructure
- **SMEs** - Require reliable hosting without breaking the bank
- **Students & Learners** - Affordable platform for learning cloud development

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Public APIs   │    │   User APIs     │    │   Admin APIs    │
│                 │    │                 │    │                 │
│ • Authentication│    │ • Applications  │    │ • User Mgmt     │
│ • Regions Info  │    │ • Billing       │    │ • Vultr Direct  │
│ • Webhooks      │    │ • Infrastructure│    │ • System Mgmt   │
│ • Health Checks │    │ • Hosting       │    │ • Analytics     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Achidas Backend │
                    │                 │
                    │ • Route Logic   │
                    │ • Auth System   │
                    │ • Rate Limiting │
                    │ • Monitoring    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Vultr API       │
                    │                 │
                    │ • Servers       │
                    │ • Storage       │
                    │ • Networking    │
                    │ • Databases     │
                    └─────────────────┘
```

## 🌐 API Organization

### 🌍 Public APIs (`/api/v1/public/`)
**No Authentication Required**
- Authentication endpoints (register, login, refresh)
- Public information (regions, pricing, plans)
- Webhook endpoints for external services
- Health checks

### 👤 User APIs (`/api/v1/users/`)
**Authentication Required**
- Application management (create, deploy, monitor)
- Environment variables and secrets
- Billing and account management
- Infrastructure management (instances, SSH keys)
- Hosting services (import servers, scaling)

### 🔧 Admin APIs (`/api/v1/admin/`)
**Admin Authentication Required**
- User management (list, suspend, activate)
- System administration (metrics, billing overview)
- Direct Vultr API access (servers, storage, networking)
- Infrastructure provisioning and management

## 🚀 Getting Started

### For End Users

#### 1. Register an Account
```bash
curl -X POST https://api.achidas.com/api/v1/public/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

#### 2. Login and Get Token
```bash
curl -X POST https://api.achidas.com/api/v1/public/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

#### 3. Choose an African Region
```bash
curl https://api.achidas.com/api/v1/public/african-regions
```

#### 4. Create Your First Application
```bash
curl -X POST https://api.achidas.com/api/v1/users/applications \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-web-app",
    "description": "My awesome web application",
    "repository": {
      "url": "https://github.com/user/repo.git",
      "branch": "main"
    },
    "service_type": "web_service",
    "region": "ng-lag"
  }'
```

#### 5. Deploy Your Application
```bash
curl -X POST https://api.achidas.com/api/v1/users/applications/{app_id}/deploy \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### For Users with Existing Vultr Servers

#### 1. List Your Existing Servers
```bash
curl https://api.achidas.com/api/v1/users/hosting/servers/vultr \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. Import an Existing Server
```bash
curl -X POST https://api.achidas.com/api/v1/users/hosting/servers/import/{instance_id} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3. Get Setup Instructions
```bash
curl https://api.achidas.com/api/v1/users/hosting/servers/setup/{instance_id} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### For Administrators

#### 1. Access Admin Dashboard
```bash
curl https://api.achidas.com/api/v1/admin/metrics \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### 2. Manage Vultr Infrastructure
```bash
# List all Vultr servers
curl https://api.achidas.com/api/v1/admin/vultr/servers \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Create new server
curl -X POST https://api.achidas.com/api/v1/admin/vultr/servers \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{...server_config...}'
```

## 🌍 Available African Regions

| Region | Country | City | Status | Pricing |
|--------|---------|------|--------|---------|
| `za-jhb` | South Africa | Johannesburg | ✅ Available | Standard |
| `za-cpt` | South Africa | Cape Town | ✅ Available | Standard |
| `ng-lag` | Nigeria | Lagos | ✅ Available | Economy |
| `ng-abj` | Nigeria | Abuja | ✅ Available | Economy |
| `ke-nai` | Kenya | Nairobi | ✅ Available | Economy |
| `eg-cai` | Egypt | Cairo | ✅ Available | Standard |
| `gh-acc` | Ghana | Accra | ✅ Available | Economy |
| `ma-cas` | Morocco | Casablanca | ✅ Available | Standard |

## 💰 Pricing Strategy

### Economy Tier (West/East Africa)
- **Target**: $2-5 USD per month for basic plans
- **Local Pricing**: ₦3,000-7,500 (Nigeria), KSh 300-750 (Kenya)
- **Focus**: Maximum affordability

### Standard Tier (Southern/North Africa)
- **Target**: $5-15 USD per month for basic plans
- **Local Pricing**: R75-225 (South Africa), EGP 150-450 (Egypt)
- **Focus**: Balance of performance and cost

### Premium Tier (Enterprise)
- **Target**: $15+ USD per month
- **Features**: Dedicated resources, SLA guarantees
- **Focus**: Business-grade reliability

## 🔧 Service Types

| Service | Description | Use Case |
|---------|-------------|----------|
| **Web Services** | Full-stack web applications | React, Vue, Angular apps |
| **Static Sites** | Static website hosting | Documentation, portfolios |
| **Background Workers** | Async job processing | Email sending, data processing |
| **Cron Jobs** | Scheduled tasks | Backups, reports, cleanup |
| **Private Services** | Internal microservices | APIs, databases |

## 🛠️ Tech Stack

- **Framework**: Axum (high-performance async web framework)
- **Runtime**: Tokio (async runtime)
- **Database**: MongoDB with official Rust driver
- **HTTP Client**: reqwest with custom retry logic
- **Authentication**: JWT with jsonwebtoken
- **Observability**: OpenTelemetry + Jaeger + Prometheus
- **Validation**: validator crate
- **Serialization**: serde with JSON support

## Project Structure

```
src/
├── main.rs                 # Application entry point
├── lib.rs                  # Library exports
├── config/                 # Configuration management
├── controllers/            # HTTP request handlers
│   ├── auth.rs            # Authentication endpoints
│   ├── instances.rs       # Instance management
│   ├── billing.rs         # Billing endpoints
│   ├── users.rs           # User management
│   ├── vultr.rs           # Vultr resource endpoints
│   └── admin.rs           # Admin endpoints
├── models/                 # Data models and DTOs
│   ├── user.rs            # User models
│   ├── instance.rs        # Instance models
│   └── billing.rs         # Billing models
├── services/               # Business logic layer
│   ├── auth.rs            # Authentication service
│   ├── instance.rs        # Instance management service
│   ├── billing.rs         # Billing service
│   ├── user.rs            # User service
│   └── admin.rs           # Admin service
├── routes/                 # Route definitions
├── middleware/             # Custom middleware
│   └── auth.rs            # Authentication middleware
├── vultr/                  # Vultr API client
│   ├── client.rs          # API client implementation
│   └── models.rs          # Vultr API models
├── database/               # Database connection and utilities
├── observability/          # Tracing and metrics setup
└── utils/                  # Utility functions
    └── retry.rs           # Retry logic with exponential backoff
```

## 🛠️ Development Setup

### Prerequisites
- Rust 1.70+
- MongoDB
- Vultr API Key

### Environment Variables
```bash
# Database
DATABASE_URL=mongodb://localhost:27017/achidas

# Vultr API
VULTR_API_KEY=your_vultr_api_key

# JWT
JWT_SECRET=your_jwt_secret

# Server
PORT=8080
RUST_LOG=info
```

### Running the Server
```bash
# Clone the repository
git clone https://github.com/achidas/achidas-backend.git
cd achidas-backend

# Install dependencies
cargo build

# Run the server
cargo run
```

### Building for Production
```bash
cargo build --release
```

## 📋 API Endpoints Summary

### 🌍 Public APIs (No Auth)
- Authentication: `/api/v1/public/auth/*`
- Regions: `/api/v1/public/african-regions`
- Webhooks: `/api/v1/public/webhooks/*`
- Health: `/api/v1/public/health`

### 👤 User APIs (Auth Required)
- Applications: `/api/v1/users/applications/*`
- Environment: `/api/v1/users/environment-groups/*`
- Billing: `/api/v1/users/billing/*`
- Infrastructure: `/api/v1/users/instances/*`
- SSH Keys: `/api/v1/users/ssh-keys/*`
- Hosting: `/api/v1/users/hosting/*`

### 🔧 Admin APIs (Admin Auth)
- User Management: `/api/v1/admin/users/*`
- System: `/api/v1/admin/metrics`
- Vultr Direct: `/api/v1/admin/vultr/*`

*For complete API documentation, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)*

## 🧪 Development

### Running Tests
```bash
cargo test
```

### Running with Debug Logging
```bash
RUST_LOG=debug cargo run
```

### Database Setup
The application will automatically create necessary indexes on startup. Make sure MongoDB is running and accessible.

## 📊 Monitoring and Observability

### Metrics
Prometheus metrics are available at `http://localhost:9090/metrics`

Key metrics include:
- HTTP request counts and durations
- Vultr API call counts and errors
- Active instance counts
- User registration and login counts

### Tracing
Distributed tracing via OpenTelemetry and Jaeger. Configure `JAEGER_ENDPOINT` to enable.

### Logging
Structured logging with configurable levels. Set `LOG_LEVEL` environment variable.

## 🚀 Production Deployment

### Docker Support
```dockerfile
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/achidas /usr/local/bin/achidas
EXPOSE 8080
CMD ["achidas"]
```

### Kubernetes Deployment
The application is stateless and Kubernetes-ready with proper configuration management.

## 📚 Documentation

- **API Documentation**: [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)
- **Deployment Guide**: Coming soon
- **User Guide**: Coming soon

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for more information.

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: https://docs.achidas.com
- **Community**: https://community.achidas.com

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ for Africa by Achidas Team**
