fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, Query<utils::Pagination>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<DeploymentResponse>>>, ControllerError>> {list_deployments}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, Query<utils::Pagination>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<DeploymentResponse>>>, ControllerError>> {list_deployments}: Handler<_, _>
