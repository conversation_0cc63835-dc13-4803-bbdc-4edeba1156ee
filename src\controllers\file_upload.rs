use crate::{
    controllers::{<PERSON><PERSON><PERSON><PERSON>, ControllerR<PERSON>ult},
    models::{ApiResponse, Claims, deployment::{CreateSimpleApplicationRequest, HostingTier, SimpleServiceType}},
    services::{
        file_upload::{FileUploadService, FileUploadConfig},
        deployment::DeploymentService,
        hosting_plans::HostingPlansService,
    },
    AppState,
};
use axum::{
    extract::{Multipart, State},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::instrument;

// Helper function to parse file size strings like "100MB", "1GB", etc.
fn parse_file_size(size_str: &str) -> Option<u64> {
    let size_str = size_str.to_uppercase();
    if let Some(pos) = size_str.find(|c: char| c.is_alphabetic()) {
        let (number_part, unit_part) = size_str.split_at(pos);
        if let Ok(number) = number_part.parse::<u64>() {
            let multiplier = match unit_part {
                "B" => 1,
                "KB" => 1024,
                "MB" => 1024 * 1024,
                "GB" => 1024 * 1024 * 1024,
                _ => return None,
            };
            return Some(number * multiplier);
        }
    }
    None
}

#[derive(Debug, Serialize)]
pub struct FileUploadResponse {
    pub file_id: String,
    pub original_name: String,
    pub size: u64,
    pub extracted: bool,
    pub upload_url: String,
}

#[derive(Debug, Deserialize)]
pub struct DeployFromUploadRequest {
    pub file_id: String,
    pub name: String,
    pub description: Option<String>,
    pub service_type: SimpleServiceType,
    pub hosting_tier: HostingTier,
    pub custom_plan: Option<String>,
    pub environment_variables: Option<std::collections::HashMap<String, String>>,
    pub build_command: Option<String>,
    pub start_command: Option<String>,
    pub region: Option<String>,
    pub vultr_plan: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct DeployFromUploadResponse {
    pub application_id: String,
    pub deployment_id: String,
    pub status: String,
    pub message: String,
}

// Upload file for deployment
#[instrument(skip(state, multipart))]
pub async fn upload_file(
    State(state): State<Arc<AppState>>,
    multipart: Multipart,
) -> ControllerResult<Json<ApiResponse<FileUploadResponse>>> {
    let config = FileUploadConfig {
        upload_path: std::env::var("UPLOAD_PATH").unwrap_or_else(|_| "./uploads".to_string()),
        max_file_size: parse_file_size(&std::env::var("MAX_FILE_SIZE").unwrap_or_else(|_| "100MB".to_string()))
            .unwrap_or(100 * 1024 * 1024),
        allowed_extensions: std::env::var("ALLOWED_FILE_TYPES")
            .unwrap_or_else(|_| "zip,tar,tar.gz,tgz,js,html,css,json,md,txt".to_string())
            .split(',')
            .map(|s| s.trim().to_string())
            .collect(),
    };

    let upload_service = FileUploadService::new(config);
    let uploaded_file = upload_service.handle_upload(multipart).await?;

    let response = FileUploadResponse {
        file_id: uploaded_file.id.clone(),
        original_name: uploaded_file.original_name,
        size: uploaded_file.size,
        extracted: uploaded_file.extracted_path.is_some(),
        upload_url: format!("/api/v1/users/uploads/{}", uploaded_file.id),
    };

    Ok(Json(ApiResponse::success(response)))
}

// Deploy application from uploaded file
#[instrument(skip(state, claims))]
pub async fn deploy_from_upload(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Json(request): Json<DeployFromUploadRequest>,
) -> ControllerResult<Json<ApiResponse<DeployFromUploadResponse>>> {
    // Validate file exists
    let config = FileUploadConfig::default();
    let upload_service = FileUploadService::new(config);
    
    let file_path = upload_service.get_file_path(&request.file_id);
    if !file_path.exists() {
        return Err(ControllerError::NotFound("Uploaded file not found".to_string()));
    }

    // Get the source path (extracted if compressed, original if not)
    let source_path = if upload_service.get_extracted_path(&request.file_id).exists() {
        upload_service.get_extracted_path(&request.file_id)
    } else {
        file_path
    };

    // Convert to simplified application request
    let app_request = CreateSimpleApplicationRequest {
        name: request.name,
        description: request.description,
        repository_url: format!("file://{}", source_path.display()), // Special file:// URL
        branch: Some("main".to_string()),
        service_type: request.service_type,
        hosting_tier: request.hosting_tier,
        custom_plan: request.custom_plan,
        environment_variables: request.environment_variables,
        build_command: request.build_command,
        start_command: request.start_command,
        region: request.region,
        vultr_plan: request.vultr_plan,
    };

    // Deploy the application
    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    let application = deployment_service.create_simple_application(&claims.sub, app_request).await?;

    let response = DeployFromUploadResponse {
        application_id: application.id,
        deployment_id: application.latest_deployment.unwrap_or_else(|| "pending".to_string()),
        status: "deploying".to_string(),
        message: "Application deployment started from uploaded files".to_string(),
    };

    Ok(Json(ApiResponse::success(response)))
}

// List uploaded files for user
#[instrument(skip(state, claims))]
pub async fn list_uploads(
    State(_state): State<Arc<AppState>>,
    claims: Claims,
) -> ControllerResult<Json<ApiResponse<Vec<FileUploadResponse>>>> {
    // This is a simplified implementation
    // In production, you'd store upload metadata in database with user association
    let uploads = vec![]; // Placeholder - implement proper file listing

    Ok(Json(ApiResponse::success(uploads)))
}

// Delete uploaded file
#[instrument(skip(state, claims, file_id))]
pub async fn delete_upload(
    State(_state): State<Arc<AppState>>,
    claims: Claims,
    axum::extract::Path(file_id): axum::extract::Path<String>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    let config = FileUploadConfig::default();
    let upload_service = FileUploadService::new(config);
    
    upload_service.cleanup_file(&file_id).await?;

    Ok(Json(ApiResponse::success(())))
}

// Get upload info
#[instrument(skip(state, claims, file_id))]
pub async fn get_upload_info(
    State(_state): State<Arc<AppState>>,
    claims: Claims,
    axum::extract::Path(file_id): axum::extract::Path<String>,
) -> ControllerResult<Json<ApiResponse<FileUploadResponse>>> {
    let config = FileUploadConfig::default();
    let upload_service = FileUploadService::new(config);
    
    let file_path = upload_service.get_file_path(&file_id);
    if !file_path.exists() {
        return Err(ControllerError::NotFound("File not found".to_string()));
    }

    let metadata = tokio::fs::metadata(&file_path).await
        .map_err(|e| ControllerError::Internal(format!("Failed to get file metadata: {}", e)))?;

    let response = FileUploadResponse {
        file_id: file_id.clone(),
        original_name: file_path.file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("unknown")
            .to_string(),
        size: metadata.len(),
        extracted: upload_service.get_extracted_path(&file_id).exists(),
        upload_url: format!("/api/v1/users/uploads/{}", file_id),
    };

    Ok(Json(ApiResponse::success(response)))
}

// Deployment methods for different service types
pub fn get_deployment_suggestions(service_type: &SimpleServiceType) -> DeploymentSuggestions {
    match service_type {
        SimpleServiceType::StaticWebsite => DeploymentSuggestions {
            build_command: Some("npm run build".to_string()),
            start_command: Some("serve -s build".to_string()),
            recommended_tier: HostingTier::Shared,
            recommended_plan: "starter".to_string(),
            description: "Perfect for HTML/CSS/JS websites. Files will be served directly.".to_string(),
        },
        SimpleServiceType::ReactApp => DeploymentSuggestions {
            build_command: Some("npm run build".to_string()),
            start_command: Some("serve -s build".to_string()),
            recommended_tier: HostingTier::Shared,
            recommended_plan: "standard".to_string(),
            description: "React applications with build process. Optimized for SPA deployment.".to_string(),
        },
        SimpleServiceType::VueApp => DeploymentSuggestions {
            build_command: Some("npm run build".to_string()),
            start_command: Some("serve -s dist".to_string()),
            recommended_tier: HostingTier::Shared,
            recommended_plan: "standard".to_string(),
            description: "Vue.js applications with build process.".to_string(),
        },
        SimpleServiceType::NextJsApp => DeploymentSuggestions {
            build_command: Some("npm run build".to_string()),
            start_command: Some("npm start".to_string()),
            recommended_tier: HostingTier::Dedicated,
            recommended_plan: "dedicated-small".to_string(),
            description: "Next.js applications with SSR capabilities.".to_string(),
        },
        SimpleServiceType::NodeJsApp => DeploymentSuggestions {
            build_command: Some("npm install".to_string()),
            start_command: Some("npm start".to_string()),
            recommended_tier: HostingTier::Shared,
            recommended_plan: "pro".to_string(),
            description: "Node.js backend applications and APIs.".to_string(),
        },
        SimpleServiceType::PythonApp => DeploymentSuggestions {
            build_command: Some("pip install -r requirements.txt".to_string()),
            start_command: Some("python app.py".to_string()),
            recommended_tier: HostingTier::Shared,
            recommended_plan: "pro".to_string(),
            description: "Python web applications (Flask, Django, FastAPI).".to_string(),
        },
        SimpleServiceType::PhpApp => DeploymentSuggestions {
            build_command: Some("composer install".to_string()),
            start_command: Some("php -S 0.0.0.0:8000".to_string()),
            recommended_tier: HostingTier::Shared,
            recommended_plan: "basic".to_string(),
            description: "PHP applications and websites.".to_string(),
        },
        SimpleServiceType::DockerApp => DeploymentSuggestions {
            build_command: Some("docker build -t app .".to_string()),
            start_command: Some("docker run -p 8000:8000 app".to_string()),
            recommended_tier: HostingTier::Dedicated,
            recommended_plan: "dedicated-medium".to_string(),
            description: "Containerized applications with Docker.".to_string(),
        },
    }
}

#[derive(Debug, Serialize)]
pub struct DeploymentSuggestions {
    pub build_command: Option<String>,
    pub start_command: Option<String>,
    pub recommended_tier: HostingTier,
    pub recommended_plan: String,
    pub description: String,
}

// Get deployment suggestions for service type
#[instrument(skip(state))]
pub async fn get_deployment_suggestions_endpoint(
    State(_state): State<Arc<AppState>>,
    axum::extract::Path(service_type): axum::extract::Path<String>,
) -> ControllerResult<Json<ApiResponse<DeploymentSuggestions>>> {
    let service_type = match service_type.as_str() {
        "static" => SimpleServiceType::StaticWebsite,
        "react" => SimpleServiceType::ReactApp,
        "vue" => SimpleServiceType::VueApp,
        "nextjs" => SimpleServiceType::NextJsApp,
        "nodejs" => SimpleServiceType::NodeJsApp,
        "python" => SimpleServiceType::PythonApp,
        "php" => SimpleServiceType::PhpApp,
        "docker" => SimpleServiceType::DockerApp,
        _ => return Err(ControllerError::Validation("Invalid service type".to_string())),
    };

    let suggestions = get_deployment_suggestions(&service_type);
    Ok(Json(ApiResponse::success(suggestions)))
}
