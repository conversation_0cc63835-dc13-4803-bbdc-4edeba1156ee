use crate::{
    controllers::{success_response, ControllerResult},
    models::{regions::*, ApiResponse},
    AppState,
};
use crate::models::regions::get_pricing_tier;
use axum::{
    extract::{Path, Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::instrument;

#[derive(Debug, Deserialize)]
pub struct RegionQuery {
    pub available_only: Option<bool>,
    pub country: Option<String>,
}

#[instrument(skip(state))]
pub async fn list_regions(
    State(state): State<Arc<AppState>>,
    Query(query): Query<RegionQuery>,
) -> ControllerResult<Json<ApiResponse<Vec<RegionResponse>>>> {
    let regions = if query.available_only.unwrap_or(false) {
        get_available_regions()
    } else {
        get_african_regions()
    };

    let filtered_regions: Vec<RegionResponse> = regions
        .into_iter()
        .filter(|region| {
            if let Some(ref country_code) = query.country {
                region.country_code.to_lowercase() == country_code.to_lowercase()
            } else {
                true
            }
        })
        .map(|region| region.into())
        .collect();

    Ok(success_response(filtered_regions))
}

#[instrument(skip(state))]
pub async fn get_region(
    State(state): State<Arc<AppState>>,
    Path(region_code): Path<String>,
) -> ControllerResult<Json<ApiResponse<RegionResponse>>> {
    // Inline validation checks
    if region_code.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("region_code is required".to_string()));
    }

    let region = get_region_by_code(&region_code)
        .ok_or_else(|| crate::controllers::ControllerError::NotFound("Region not found".to_string()))?;

    Ok(success_response(region.into()))
}

#[derive(Debug, Serialize)]
pub struct CountryResponse {
    pub code: String,
    pub name: String,
    pub regions_count: usize,
    pub available_regions_count: usize,
}

#[instrument(skip(state))]
pub async fn list_countries(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<Vec<CountryResponse>>>> {
    let all_regions = get_african_regions();
    
    // Group regions by country
    let mut countries: std::collections::HashMap<String, (String, Vec<AfricanRegion>)> = std::collections::HashMap::new();
    
    for region in all_regions {
        countries
            .entry(region.country_code.clone())
            .or_insert((region.country.clone(), Vec::new()))
            .1
            .push(region);
    }

    let country_responses: Vec<CountryResponse> = countries
        .into_iter()
        .map(|(code, (name, regions))| {
            let available_count = regions.iter().filter(|r| r.available).count();
            CountryResponse {
                code,
                name,
                regions_count: regions.len(),
                available_regions_count: available_count,
            }
        })
        .collect();

    Ok(success_response(country_responses))
}

#[instrument(skip(state))]
pub async fn get_country_regions(
    State(state): State<Arc<AppState>>,
    Path(country_code): Path<String>,
) -> ControllerResult<Json<ApiResponse<Vec<RegionResponse>>>> {
    // Inline validation checks
    if country_code.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("country_code is required".to_string()));
    }

    let regions = get_regions_by_country(&country_code.to_uppercase());
    
    if regions.is_empty() {
        return Err(crate::controllers::ControllerError::NotFound("Country not found or has no regions".to_string()));
    }

    let region_responses: Vec<RegionResponse> = regions
        .into_iter()
        .map(|region| region.into())
        .collect();

    Ok(success_response(region_responses))
}

#[derive(Debug, Serialize)]
pub struct RegionPricingResponse {
    pub region_code: String,
    pub region_name: String,
    pub country: String,
    pub pricing_tier: String,
    pub base_price_multiplier: f64,
    pub currency: String,
    pub estimated_monthly_cost: EstimatedCosts,
}

#[derive(Debug, Serialize)]
pub struct EstimatedCosts {
    pub micro: f64,    // 1 vCPU, 1GB RAM
    pub small: f64,    // 1 vCPU, 2GB RAM
    pub medium: f64,   // 2 vCPU, 4GB RAM
    pub large: f64,    // 4 vCPU, 8GB RAM
    pub xlarge: f64,   // 8 vCPU, 16GB RAM
}

#[instrument(skip(state))]
pub async fn get_region_pricing(
    State(state): State<Arc<AppState>>,
    Path(region_code): Path<String>,
) -> ControllerResult<Json<ApiResponse<RegionPricingResponse>>> {
    // Inline validation checks
    if region_code.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("region_code is required".to_string()));
    }

    let region = get_region_by_code(&region_code)
        .ok_or_else(|| crate::controllers::ControllerError::NotFound("Region not found".to_string()))?;

    let pricing_tier = get_pricing_tier(&region.code);
    let base_multiplier = match pricing_tier.as_str() {
        "economy" => 0.8,
        "standard" => 1.0,
        "premium" => 1.2,
        _ => 1.0,
    };

    // Base prices in USD (these would typically come from a pricing service)
    let base_costs = EstimatedCosts {
        micro: 5.0,
        small: 10.0,
        medium: 20.0,
        large: 40.0,
        xlarge: 80.0,
    };

    let estimated_costs = EstimatedCosts {
        micro: base_costs.micro * base_multiplier,
        small: base_costs.small * base_multiplier,
        medium: base_costs.medium * base_multiplier,
        large: base_costs.large * base_multiplier,
        xlarge: base_costs.xlarge * base_multiplier,
    };

    let pricing_response = RegionPricingResponse {
        region_code: region.code.clone(),
        region_name: region.name.clone(),
        country: region.country.clone(),
        pricing_tier,
        base_price_multiplier: base_multiplier,
        currency: "USD".to_string(),
        estimated_monthly_cost: estimated_costs,
    };

    Ok(success_response(pricing_response))
}
