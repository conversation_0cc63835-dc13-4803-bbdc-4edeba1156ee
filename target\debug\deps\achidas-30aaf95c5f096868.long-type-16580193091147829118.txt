fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<ApplicationResponse>>, ControllerError>> {get_application}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<ApplicationResponse>>, ControllerError>> {get_application}: Handler<_, _>
