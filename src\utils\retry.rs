use crate::config::RetryConfig;
use backoff::{future::retry, ExponentialBackoff};
use reqwest::{Client, Request, Response};
use std::convert::AsRef;
use std::sync::Arc;
use std::time::{Duration, Instant};
use thiserror::Error;
use tokio::sync::Mutex;
use tracing::{error, warn, instrument, debug};

#[derive(Error, Debug)]
pub enum RetryError {
    #[error("HTTP request failed: {0}")]
    Http(#[from] reqwest::Error),

    #[error("Max retries exceeded")]
    MaxRetriesExceeded,

    #[error("Request timeout")]
    Timeout,

    #[error("Permanent error: {0}")]
    Permanent(String),

    #[error("Transient error: {0}")]
    Transient(String),

    #[error("Rate limited: {0}")]
    RateLimited(String),
}

#[derive(Debug, Clone)]
pub struct RateLimiter {
    requests: Arc<Mutex<Vec<Instant>>>,
    max_requests: usize,
    window_duration: Duration,
}

impl RateLimiter {
    pub fn new(max_requests: usize, window_duration: Duration) -> Self {
        Self {
            requests: Arc::new(Mutex::new(Vec::new())),
            max_requests,
            window_duration,
        }
    }

    pub fn wait_if_needed(&self) -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send + '_>> {
        Box::pin(async move {
            self.wait_if_needed_impl().await
        })
    }

    fn wait_if_needed_impl(&self) -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send + '_>> {
        Box::pin(async move {
        let mut requests = self.requests.lock().await;
        let now = Instant::now();

        // Remove old requests outside the window
        requests.retain(|&request_time| now.duration_since(request_time) < self.window_duration);

        // If we're at the limit, wait until we can make another request
        if requests.len() >= self.max_requests {
            if let Some(&oldest_request) = requests.first() {
                let wait_time = self.window_duration - now.duration_since(oldest_request);
                if wait_time > Duration::from_millis(0) {
                    debug!("Rate limiting: waiting {:?} before next request", wait_time);
                    drop(requests); // Release the lock before sleeping
                    tokio::time::sleep(wait_time + Duration::from_millis(100)).await; // Add small buffer
                    return self.wait_if_needed_impl().await; // Recursive call to check again
                }
            }
        }

        // Record this request
        requests.push(now);
        })
    }
}

#[derive(Debug, Clone)]
pub struct RetryClient {
    client: Client,
    config: RetryConfig,
    rate_limiter: Option<RateLimiter>,
}

impl RetryClient {
    pub fn new(client: Client, config: RetryConfig) -> Self {
        Self {
            client,
            config,
            rate_limiter: None,
        }
    }

    pub fn with_rate_limiter(client: Client, config: RetryConfig, rate_limiter: RateLimiter) -> Self {
        Self {
            client,
            config,
            rate_limiter: Some(rate_limiter),
        }
    }

    #[instrument(skip(self, url))]
    pub async fn get(&self, url: &str) -> Result<Response, RetryError> {
        let request = self.client.get(url).build()?;
        self.execute_with_retry(request).await
    }

    #[instrument(skip(self, url))]
    pub fn post(&self, url: &str) -> RequestBuilder {
        RequestBuilder {
            client: self.clone(),
            request_builder: self.client.post(url),
        }
    }

    #[instrument(skip(self, url))]
    pub fn put(&self, url: &str) -> RequestBuilder {
        RequestBuilder {
            client: self.clone(),
            request_builder: self.client.put(url),
        }
    }

    #[instrument(skip(self, url))]
    pub fn patch(&self, url: &str) -> RequestBuilder {
        RequestBuilder {
            client: self.clone(),
            request_builder: self.client.patch(url),
        }
    }

    #[instrument(skip(self, url))]
    pub async fn delete(&self, url: &str) -> Result<Response, RetryError> {
        let request = self.client.delete(url).build()?;
        self.execute_with_retry(request).await
    }

    #[instrument(skip(self, request))]
    async fn execute_with_retry(&self, request: Request) -> Result<Response, RetryError> {
        // Apply rate limiting if configured
        if let Some(rate_limiter) = &self.rate_limiter {
            rate_limiter.wait_if_needed().await;
        }
        let backoff = ExponentialBackoff {
            initial_interval: Duration::from_millis(self.config.initial_delay_ms),
            max_interval: Duration::from_millis(self.config.max_delay_ms),
            multiplier: self.config.multiplier,
            max_elapsed_time: Some(Duration::from_secs(300)), // 5 minutes total
            ..Default::default()
        };

        let operation = || async {
            let cloned_request = request.try_clone()
                .ok_or_else(|| backoff::Error::permanent(RetryError::MaxRetriesExceeded))?;

            match self.client.execute(cloned_request).await {
                Ok(response) => {
                    if response.status() == 429 {
                        // Rate limited by Vultr API
                        warn!("Rate limited by API (429), retrying after delay...");
                        Err(backoff::Error::transient(RetryError::RateLimited("API rate limit exceeded".to_string())))
                    } else if response.status().is_server_error() {
                        warn!("Server error {}, retrying...", response.status());
                        Err(backoff::Error::transient(RetryError::Timeout))
                    } else if response.status().is_client_error() {
                        // Don't retry other client errors
                        Ok(response)
                    } else {
                        Ok(response)
                    }
                }
                Err(e) => {
                    if e.is_timeout() || e.is_connect() {
                        warn!("Network error, retrying: {}", e);
                        Err(backoff::Error::transient(RetryError::Http(e)))
                    } else {
                        error!("Permanent error: {}", e);
                        Err(backoff::Error::permanent(RetryError::Http(e)))
                    }
                }
            }
        };

        retry(backoff, operation).await
            .map_err(|_| RetryError::MaxRetriesExceeded)
    }
}

pub struct RequestBuilder {
    client: RetryClient,
    request_builder: reqwest::RequestBuilder,
}

impl RequestBuilder {
    pub fn json<T: serde::Serialize + ?Sized>(self, json: &T) -> Self {
        Self {
            client: self.client,
            request_builder: self.request_builder.json(json),
        }
    }

    pub fn header<K, V>(self, key: K, value: V) -> Self
    where
        K: AsRef<str>,
        V: AsRef<str>,
    {
        Self {
            client: self.client,
            request_builder: self.request_builder.header(key.as_ref(), value.as_ref()),
        }
    }

    pub async fn send(self) -> Result<Response, RetryError> {
        let request = self.request_builder.build()?;
        self.client.execute_with_retry(request).await
    }
}