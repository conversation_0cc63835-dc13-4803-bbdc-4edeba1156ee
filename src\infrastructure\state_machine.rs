use serde::{Deserialize, Serialize};
use std::fmt;
use tracing::{info, warn, error};
use chrono::{DateTime, Utc};

// Deployment State Machine
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum DeploymentState {
    Created,
    Queued,
    Building,
    Testing,
    Deploying,
    Running,
    Failed,
    Cancelled,
    RolledBack,
}

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum DeploymentEvent {
    Queue,
    StartBuild,
    BuildComplete,
    BuildFailed,
    StartTest,
    TestComplete,
    TestFailed,
    StartDeploy,
    DeployComplete,
    DeployFailed,
    Cancel,
    Rollback,
    Retry,
}

// Define the state machine using simple struct
#[derive(Clone, Debug)]
pub struct DeploymentStateMachine {
    pub state: DeploymentState,
}

impl DeploymentStateMachine {
    pub fn new() -> Self {
        Self {
            state: DeploymentState::Created,
        }
    }

    pub fn from_state(state: DeploymentState) -> Self {
        Self { state }
    }

    pub fn current_state(&self) -> &DeploymentState {
        &self.state
    }

    pub fn transition(&mut self, event: DeploymentEvent) -> Result<DeploymentState, String> {
        let new_state = match (&self.state, &event) {
            (DeploymentState::Created, DeploymentEvent::Queue) => DeploymentState::Queued,
            (DeploymentState::Created, DeploymentEvent::Cancel) => DeploymentState::Cancelled,
            
            (DeploymentState::Queued, DeploymentEvent::StartBuild) => DeploymentState::Building,
            (DeploymentState::Queued, DeploymentEvent::Cancel) => DeploymentState::Cancelled,
            
            (DeploymentState::Building, DeploymentEvent::BuildComplete) => DeploymentState::Testing,
            (DeploymentState::Building, DeploymentEvent::BuildFailed) => DeploymentState::Failed,
            (DeploymentState::Building, DeploymentEvent::Cancel) => DeploymentState::Cancelled,
            
            (DeploymentState::Testing, DeploymentEvent::TestComplete) => DeploymentState::Deploying,
            (DeploymentState::Testing, DeploymentEvent::TestFailed) => DeploymentState::Failed,
            (DeploymentState::Testing, DeploymentEvent::Cancel) => DeploymentState::Cancelled,
            
            (DeploymentState::Deploying, DeploymentEvent::DeployComplete) => DeploymentState::Running,
            (DeploymentState::Deploying, DeploymentEvent::DeployFailed) => DeploymentState::Failed,
            (DeploymentState::Deploying, DeploymentEvent::Cancel) => DeploymentState::Cancelled,
            
            (DeploymentState::Running, DeploymentEvent::Rollback) => DeploymentState::RolledBack,
            (DeploymentState::Running, DeploymentEvent::StartBuild) => DeploymentState::Building,
            
            (DeploymentState::Failed, DeploymentEvent::Retry) => DeploymentState::Queued,
            (DeploymentState::Failed, DeploymentEvent::Cancel) => DeploymentState::Cancelled,
            
            (DeploymentState::Cancelled, DeploymentEvent::Retry) => DeploymentState::Queued,
            
            (DeploymentState::RolledBack, DeploymentEvent::StartBuild) => DeploymentState::Building,
            
            _ => return Err(format!("Invalid transition from {:?} with event {:?}", self.state, event)),
        };

        self.state = new_state.clone();
        Ok(new_state)
    }

    pub fn can_transition(&self, event: &DeploymentEvent) -> bool {
        match (&self.state, event) {
            (DeploymentState::Created, DeploymentEvent::Queue) => true,
            (DeploymentState::Created, DeploymentEvent::Cancel) => true,
            
            (DeploymentState::Queued, DeploymentEvent::StartBuild) => true,
            (DeploymentState::Queued, DeploymentEvent::Cancel) => true,
            
            (DeploymentState::Building, DeploymentEvent::BuildComplete) => true,
            (DeploymentState::Building, DeploymentEvent::BuildFailed) => true,
            (DeploymentState::Building, DeploymentEvent::Cancel) => true,
            
            (DeploymentState::Testing, DeploymentEvent::TestComplete) => true,
            (DeploymentState::Testing, DeploymentEvent::TestFailed) => true,
            (DeploymentState::Testing, DeploymentEvent::Cancel) => true,
            
            (DeploymentState::Deploying, DeploymentEvent::DeployComplete) => true,
            (DeploymentState::Deploying, DeploymentEvent::DeployFailed) => true,
            (DeploymentState::Deploying, DeploymentEvent::Cancel) => true,
            
            (DeploymentState::Running, DeploymentEvent::Rollback) => true,
            (DeploymentState::Running, DeploymentEvent::StartBuild) => true,
            
            (DeploymentState::Failed, DeploymentEvent::Retry) => true,
            (DeploymentState::Failed, DeploymentEvent::Cancel) => true,
            
            (DeploymentState::Cancelled, DeploymentEvent::Retry) => true,
            
            (DeploymentState::RolledBack, DeploymentEvent::StartBuild) => true,
            
            _ => false,
        }
    }
}

// Application State Machine
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum ApplicationState {
    Creating,
    Active,
    Suspended,
    Deleting,
    Deleted,
    Error,
}

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum ApplicationEvent {
    Created,
    Suspend,
    Resume,
    Delete,
    Error,
    Recover,
}

#[derive(Clone, Debug)]
pub struct ApplicationStateMachine {
    pub state: ApplicationState,
}

impl ApplicationStateMachine {
    pub fn new() -> Self {
        Self {
            state: ApplicationState::Creating,
        }
    }

    pub fn from_state(state: ApplicationState) -> Self {
        Self { state }
    }

    pub fn current_state(&self) -> &ApplicationState {
        &self.state
    }

    pub fn transition(&mut self, event: ApplicationEvent) -> Result<ApplicationState, String> {
        let new_state = match (&self.state, &event) {
            (ApplicationState::Creating, ApplicationEvent::Created) => ApplicationState::Active,
            (ApplicationState::Creating, ApplicationEvent::Error) => ApplicationState::Error,
            
            (ApplicationState::Active, ApplicationEvent::Suspend) => ApplicationState::Suspended,
            (ApplicationState::Active, ApplicationEvent::Delete) => ApplicationState::Deleting,
            (ApplicationState::Active, ApplicationEvent::Error) => ApplicationState::Error,
            
            (ApplicationState::Suspended, ApplicationEvent::Resume) => ApplicationState::Active,
            (ApplicationState::Suspended, ApplicationEvent::Delete) => ApplicationState::Deleting,
            (ApplicationState::Suspended, ApplicationEvent::Error) => ApplicationState::Error,
            
            (ApplicationState::Deleting, ApplicationEvent::Delete) => ApplicationState::Deleted,
            (ApplicationState::Deleting, ApplicationEvent::Error) => ApplicationState::Error,
            
            (ApplicationState::Error, ApplicationEvent::Recover) => ApplicationState::Active,
            (ApplicationState::Error, ApplicationEvent::Delete) => ApplicationState::Deleting,
            
            (ApplicationState::Deleted, ApplicationEvent::Recover) => ApplicationState::Creating,
            
            _ => return Err(format!("Invalid transition from {:?} with event {:?}", self.state, event)),
        };

        self.state = new_state.clone();
        Ok(new_state)
    }

    pub fn can_transition(&self, event: &ApplicationEvent) -> bool {
        match (&self.state, event) {
            (ApplicationState::Creating, ApplicationEvent::Created) => true,
            (ApplicationState::Creating, ApplicationEvent::Error) => true,
            
            (ApplicationState::Active, ApplicationEvent::Suspend) => true,
            (ApplicationState::Active, ApplicationEvent::Delete) => true,
            (ApplicationState::Active, ApplicationEvent::Error) => true,
            
            (ApplicationState::Suspended, ApplicationEvent::Resume) => true,
            (ApplicationState::Suspended, ApplicationEvent::Delete) => true,
            (ApplicationState::Suspended, ApplicationEvent::Error) => true,
            
            (ApplicationState::Deleting, ApplicationEvent::Delete) => true,
            (ApplicationState::Deleting, ApplicationEvent::Error) => true,
            
            (ApplicationState::Error, ApplicationEvent::Recover) => true,
            (ApplicationState::Error, ApplicationEvent::Delete) => true,
            
            (ApplicationState::Deleted, ApplicationEvent::Recover) => true,
            
            _ => false,
        }
    }
}

// Build State Machine
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum BuildState {
    Queued,
    Running,
    Success,
    Failed,
    Cancelled,
    Timeout,
}

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum BuildEvent {
    Start,
    Complete,
    Fail,
    Cancel,
    Timeout,
    Retry,
}

#[derive(Clone, Debug)]
pub struct BuildStateMachine {
    pub state: BuildState,
}

impl BuildStateMachine {
    pub fn new() -> Self {
        Self {
            state: BuildState::Queued,
        }
    }

    pub fn from_state(state: BuildState) -> Self {
        Self { state }
    }

    pub fn current_state(&self) -> &BuildState {
        &self.state
    }

    pub fn transition(&mut self, event: BuildEvent) -> Result<BuildState, String> {
        let new_state = match (&self.state, &event) {
            (BuildState::Queued, BuildEvent::Start) => BuildState::Running,
            (BuildState::Queued, BuildEvent::Cancel) => BuildState::Cancelled,
            (BuildState::Queued, BuildEvent::Timeout) => BuildState::Timeout,
            
            (BuildState::Running, BuildEvent::Complete) => BuildState::Success,
            (BuildState::Running, BuildEvent::Fail) => BuildState::Failed,
            (BuildState::Running, BuildEvent::Cancel) => BuildState::Cancelled,
            (BuildState::Running, BuildEvent::Timeout) => BuildState::Timeout,
            
            (BuildState::Success, BuildEvent::Retry) => BuildState::Queued,
            (BuildState::Failed, BuildEvent::Retry) => BuildState::Queued,
            (BuildState::Timeout, BuildEvent::Retry) => BuildState::Queued,
            
            _ => return Err(format!("Invalid transition from {:?} with event {:?}", self.state, event)),
        };

        self.state = new_state.clone();
        Ok(new_state)
    }

    pub fn can_transition(&self, event: &BuildEvent) -> bool {
        match (&self.state, event) {
            (BuildState::Queued, BuildEvent::Start) => true,
            (BuildState::Queued, BuildEvent::Cancel) => true,
            (BuildState::Queued, BuildEvent::Timeout) => true,
            
            (BuildState::Running, BuildEvent::Complete) => true,
            (BuildState::Running, BuildEvent::Fail) => true,
            (BuildState::Running, BuildEvent::Cancel) => true,
            (BuildState::Running, BuildEvent::Timeout) => true,
            
            (BuildState::Success, BuildEvent::Retry) => true,
            (BuildState::Failed, BuildEvent::Retry) => true,
            (BuildState::Timeout, BuildEvent::Retry) => true,
            
            _ => false,
        }
    }
}

// State transition tracking
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct StateTransition<S, E> {
    pub from_state: S,
    pub to_state: S,
    pub event: E,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

pub trait StateMachineManager<S, E>
where
    S: Clone + fmt::Debug + PartialEq + Serialize,
    E: Clone + fmt::Debug + PartialEq,
{
    fn current_state(&self) -> &S;
    fn transition(&mut self, event: E) -> Result<StateTransition<S, E>, StateMachineError>;
    fn can_transition(&self, event: &E) -> bool;
    fn get_history(&self) -> &[StateTransition<S, E>];
}

#[derive(Debug, Clone)]
pub struct StateMachineError {
    pub message: String,
    pub current_state: String,
    pub attempted_event: String,
}

impl fmt::Display for StateMachineError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "State machine error: {} (current state: {}, attempted event: {})",
            self.message, self.current_state, self.attempted_event
        )
    }
}

impl std::error::Error for StateMachineError {}

// Deployment State Manager
pub struct DeploymentStateManager {
    state_machine: DeploymentStateMachine,
    history: Vec<StateTransition<DeploymentState, DeploymentEvent>>,
}

impl DeploymentStateManager {
    pub fn new() -> Self {
        Self {
            state_machine: DeploymentStateMachine::new(),
            history: Vec::new(),
        }
    }

    pub fn from_state(state: DeploymentState) -> Self {
        Self {
            state_machine: DeploymentStateMachine::from_state(state),
            history: Vec::new(),
        }
    }
}

impl StateMachineManager<DeploymentState, DeploymentEvent> for DeploymentStateManager {
    fn current_state(&self) -> &DeploymentState {
        self.state_machine.current_state()
    }

    fn transition(&mut self, event: DeploymentEvent) -> Result<StateTransition<DeploymentState, DeploymentEvent>, StateMachineError> {
        let from_state = self.state_machine.current_state().clone();

        match self.state_machine.transition(event.clone()) {
            Ok(to_state) => {
                let transition = StateTransition {
                    from_state: from_state.clone(),
                    to_state: to_state.clone(),
                    event: event.clone(),
                    timestamp: Utc::now(),
                    metadata: None,
                };

                self.history.push(transition.clone());

                info!(
                    "Deployment state transition: {:?} -> {:?} (event: {:?})",
                    from_state, to_state, event
                );

                Ok(transition)
            }
            Err(error_msg) => {
                let error = StateMachineError {
                    message: error_msg,
                    current_state: format!("{:?}", from_state),
                    attempted_event: format!("{:?}", event),
                };

                warn!(
                    "Invalid deployment state transition: {:?} -> {:?}",
                    from_state, event
                );

                Err(error)
            }
        }
    }

    fn can_transition(&self, event: &DeploymentEvent) -> bool {
        self.state_machine.can_transition(event)
    }

    fn get_history(&self) -> &[StateTransition<DeploymentState, DeploymentEvent>] {
        &self.history
    }
}

// Application State Manager
pub struct ApplicationStateManager {
    state_machine: ApplicationStateMachine,
    history: Vec<StateTransition<ApplicationState, ApplicationEvent>>,
}

impl ApplicationStateManager {
    pub fn new() -> Self {
        Self {
            state_machine: ApplicationStateMachine::new(),
            history: Vec::new(),
        }
    }

    pub fn from_state(state: ApplicationState) -> Self {
        Self {
            state_machine: ApplicationStateMachine::from_state(state),
            history: Vec::new(),
        }
    }
}

impl StateMachineManager<ApplicationState, ApplicationEvent> for ApplicationStateManager {
    fn current_state(&self) -> &ApplicationState {
        self.state_machine.current_state()
    }

    fn transition(&mut self, event: ApplicationEvent) -> Result<StateTransition<ApplicationState, ApplicationEvent>, StateMachineError> {
        let from_state = self.state_machine.current_state().clone();

        match self.state_machine.transition(event.clone()) {
            Ok(to_state) => {
                let transition = StateTransition {
                    from_state: from_state.clone(),
                    to_state: to_state.clone(),
                    event: event.clone(),
                    timestamp: Utc::now(),
                    metadata: None,
                };

                self.history.push(transition.clone());

                info!(
                    "Application state transition: {:?} -> {:?} (event: {:?})",
                    from_state, to_state, event
                );

                Ok(transition)
            }
            Err(error_msg) => {
                let error = StateMachineError {
                    message: error_msg,
                    current_state: format!("{:?}", from_state),
                    attempted_event: format!("{:?}", event),
                };

                warn!(
                    "Invalid application state transition: {:?} -> {:?}",
                    from_state, event
                );

                Err(error)
            }
        }
    }

    fn can_transition(&self, event: &ApplicationEvent) -> bool {
        self.state_machine.can_transition(event)
    }

    fn get_history(&self) -> &[StateTransition<ApplicationState, ApplicationEvent>] {
        &self.history
    }
}
