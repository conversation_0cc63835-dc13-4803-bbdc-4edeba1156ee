use serde::{Deserialize, Serialize};
use validator::Validate;

// Date serialization helpers for MongoDB compatibility
pub mod date_serialization {
    use chrono::{DateTime, Utc};
    use serde::{Deserialize, Deserializer, Serialize, Serializer};

    pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Serialize as native BSON DateTime for MongoDB
        date.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        use serde_json::Value;

        // Try to deserialize as a generic value first
        let value = Value::deserialize(deserializer)?;

        match value {
            // Handle string format (RFC 3339) - legacy data
            Value::String(s) => {
                // Try RFC 3339 first
                if let Ok(dt) = DateTime::parse_from_rfc3339(&s) {
                    return Ok(dt.with_timezone(&Utc));
                }

                // Try ISO 8601 without timezone
                if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S") {
                    return Ok(DateTime::from_naive_utc_and_offset(dt, Utc));
                }

                // Try ISO 8601 with milliseconds
                if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.3f") {
                    return Ok(DateTime::from_naive_utc_and_offset(dt, Utc));
                }

                Err(serde::de::Error::custom(format!("Unable to parse date string: {}", s)))
            }
            // Handle BSON DateTime objects (they come as maps with $date field)
            Value::Object(map) if map.contains_key("$date") => {
                if let Some(date_value) = map.get("$date") {
                    match date_value {
                        Value::String(s) => {
                            if let Ok(dt) = DateTime::parse_from_rfc3339(s) {
                                Ok(dt.with_timezone(&Utc))
                            } else if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(s, "%Y-%m-%dT%H:%M:%S") {
                                Ok(DateTime::from_naive_utc_and_offset(dt, Utc))
                            } else {
                                Err(serde::de::Error::custom(format!("Unable to parse BSON date string: {}", s)))
                            }
                        }
                        Value::Number(n) => {
                            if let Some(timestamp) = n.as_i64() {
                                DateTime::from_timestamp_millis(timestamp)
                                    .ok_or_else(|| serde::de::Error::custom("Invalid timestamp"))
                            } else {
                                Err(serde::de::Error::custom("Invalid timestamp format"))
                            }
                        }
                        _ => Err(serde::de::Error::custom("Invalid BSON date format"))
                    }
                } else {
                    Err(serde::de::Error::custom("Missing $date field"))
                }
            }
            // Handle number format (timestamp)
            Value::Number(n) => {
                if let Some(timestamp) = n.as_i64() {
                    DateTime::from_timestamp_millis(timestamp)
                        .ok_or_else(|| serde::de::Error::custom("Invalid timestamp"))
                } else {
                    Err(serde::de::Error::custom("Invalid timestamp format"))
                }
            }
            // Handle null values by returning current time as fallback
            Value::Null => {
                Ok(Utc::now())
            }
            // Try to deserialize as native DateTime for other cases
            _ => {
                DateTime::<Utc>::deserialize(value)
                    .map_err(|e| serde::de::Error::custom(format!("Failed to deserialize DateTime: {}", e)))
            }
        }
    }
}

pub mod optional_date_serialization {
    use chrono::{DateTime, Utc};
    use serde::{Deserialize, Deserializer, Serialize, Serializer};

    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(dt) => dt.serialize(serializer),
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        use serde_json::Value;

        let opt = Option::<Value>::deserialize(deserializer)?;
        match opt {
            Some(value) => {
                match value {
                    // Handle string format (RFC 3339) - legacy data
                    Value::String(s) => {
                        // Try RFC 3339 first
                        if let Ok(dt) = DateTime::parse_from_rfc3339(&s) {
                            return Ok(Some(dt.with_timezone(&Utc)));
                        }

                        // Try ISO 8601 without timezone
                        if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S") {
                            return Ok(Some(DateTime::from_naive_utc_and_offset(dt, Utc)));
                        }

                        // Try ISO 8601 with milliseconds
                        if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.3f") {
                            return Ok(Some(DateTime::from_naive_utc_and_offset(dt, Utc)));
                        }

                        Err(serde::de::Error::custom(format!("Unable to parse optional date string: {}", s)))
                    }
                    // Handle BSON DateTime objects (they come as maps with $date field)
                    Value::Object(map) if map.contains_key("$date") => {
                        if let Some(date_value) = map.get("$date") {
                            match date_value {
                                Value::String(s) => {
                                    if let Ok(dt) = DateTime::parse_from_rfc3339(s) {
                                        Ok(Some(dt.with_timezone(&Utc)))
                                    } else if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(s, "%Y-%m-%dT%H:%M:%S") {
                                        Ok(Some(DateTime::from_naive_utc_and_offset(dt, Utc)))
                                    } else {
                                        Err(serde::de::Error::custom(format!("Unable to parse optional BSON date string: {}", s)))
                                    }
                                }
                                Value::Number(n) => {
                                    if let Some(timestamp) = n.as_i64() {
                                        DateTime::from_timestamp_millis(timestamp)
                                            .map(Some)
                                            .ok_or_else(|| serde::de::Error::custom("Invalid timestamp"))
                                    } else {
                                        Err(serde::de::Error::custom("Invalid timestamp format"))
                                    }
                                }
                                _ => Err(serde::de::Error::custom("Invalid BSON date format"))
                            }
                        } else {
                            Err(serde::de::Error::custom("Missing $date field"))
                        }
                    }
                    // Handle number format (timestamp)
                    Value::Number(n) => {
                        if let Some(timestamp) = n.as_i64() {
                            DateTime::from_timestamp_millis(timestamp)
                                .map(Some)
                                .ok_or_else(|| serde::de::Error::custom("Invalid timestamp"))
                        } else {
                            Err(serde::de::Error::custom("Invalid timestamp format"))
                        }
                    }
                    // Try to deserialize as native DateTime for other cases
                    _ => {
                        DateTime::<Utc>::deserialize(value)
                            .map(Some)
                            .map_err(serde::de::Error::custom)
                    }
                }
            }
            None => Ok(None),
        }
    }
}

pub mod user;
pub mod instance;
pub mod billing;
pub mod deployment;
pub mod build;
pub mod blueprint;
pub mod disk;
pub mod domain;
pub mod database;
pub mod common;
pub mod regions;

// User models
pub use user::{User, UserRole, UserStatus, UserProfile, Claims, CreateUserRequest, LoginRequest, LoginResponse};

// Instance models
pub use instance::{Instance, InstanceStatus, CreateInstanceRequest, InstanceResponse};

// Billing models - prefix ServiceType to avoid conflicts
pub use billing::{
    BillingAccount, BillingAccountResponse, BillingResponse,
    Invoice, InvoiceStatus, UsageRecord,
    ServiceType as BillingServiceType, InvoiceResponse
};

// Deployment models
pub use deployment::{
    Application, ApplicationStatus, ApplicationResponse, CreateApplicationRequest,
    Deployment, DeploymentStatus, DeploymentTrigger, DeploymentResponse, TriggerDeploymentRequest,
    Environment, EnvironmentResponse, RuntimeConfig, AutoScalingConfig, HealthCheckConfig, NetworkingConfig,
    BuildStep as DeploymentBuildStep, LogLevel as DeploymentLogLevel, Repository, DeploymentMetrics,
    GitProvider, GitWebhookPayload, CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
};

// Build models
pub use build::{
    BuildJob, BuildStatus, BuildSource, BuildConfig, BuildMetrics, BuildWorker,
    WorkerStatus, BuildQueue, BuildPriority, CreateBuildJobRequest, BuildJobResponse,
    BuildLogsResponse, BuildStep as BuildBuildStep, LogLevel as BuildLogLevel,
    LogStream, ArtifactType, BuildRuntime, BuildArtifact, BuildLogEntry
};

// Blueprint models - prefix ServiceType to avoid conflicts
pub use blueprint::{
    Blueprint, BlueprintStatus, BlueprintResource, ServiceConfig, BlueprintSync, SyncState, AchidasYaml,
    ServiceType as BlueprintServiceType, AutoScalingConfig as BlueprintAutoScalingConfig,
    HealthCheckConfig as BlueprintHealthCheckConfig, NetworkingConfig as BlueprintNetworkingConfig
};

// Disk models
pub use disk::{Disk, DiskStatus, DiskSnapshot, Pagination as DiskPagination};

// Domain models
pub use domain::{DomainStatus};

// Database models
pub use database::{Database, DatabaseStatus, CreateDatabaseRequest, UpdateDatabaseRequest, DatabaseResponse};

// Common models
pub use common::{ErrorResponse, HealthStatus};

// Regions models
pub use regions::{AfricanRegion, RegionResponse};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponseMeta {
    pub timestamp: String,
    pub request_id: String,
    pub trace_id: String,
    pub version: String,
    pub status_code: u16,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub method: Option<String>,
}

impl Default for ApiResponseMeta {
    fn default() -> Self {
        Self {
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: uuid::Uuid::new_v4().to_string(),
            trace_id: uuid::Uuid::new_v4().to_string(),
            version: "v1".to_string(),
            status_code: 200,
            path: None,
            method: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    pub meta: ApiResponseMeta,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn success_with_meta(data: T, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta,
        }
    }

    pub fn error(error: String) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = 400;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_status(error: String, status_code: u16) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = status_code;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_meta(error: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn message_with_meta(message: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationMeta {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PaginationQuery {
    #[validate(range(min = 1, max = 100))]
    pub page: Option<u32>,
    #[validate(range(min = 1, max = 100))]
    pub per_page: Option<u32>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
        }
    }
}
