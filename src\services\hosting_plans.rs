use crate::{
    models::{CustomPlan, HostingTier, LoadBalancingAlgorithm},
    services::{ServiceError, ServiceResult},
};
use std::collections::HashMap;

pub struct HostingPlansService;

impl HostingPlansService {
    pub fn new() -> Self {
        Self
    }

    // Get all available hosting plans
    pub fn get_all_plans(&self) -> ServiceResult<Vec<CustomPlan>> {
        Ok(vec![
            // SHARED HOSTING PLANS (Pay-as-you-go)
            self.create_shared_plan("starter", 0.1, 0.1, 0.1, Some(10.0), Some(100), 0.001, 0.50),
            self.create_shared_plan("basic", 0.2, 0.2, 0.2, Some(25.0), Some(250), 0.002, 1.00),
            self.create_shared_plan("standard", 0.3, 0.3, 0.3, Some(50.0), Some(500), 0.003, 1.50),
            self.create_shared_plan("pro", 0.5, 0.5, 0.5, Some(100.0), Some(1000), 0.005, 2.50),

            // DEDICATED HOSTING PLANS (Creates new Vultr servers)
            self.create_dedicated_plan("dedicated-small", "vc2-1c-1gb", 0.007, 5.00),
            self.create_dedicated_plan("dedicated-medium", "vc2-1c-2gb", 0.014, 10.00),
            self.create_dedicated_plan("dedicated-large", "vc2-2c-2gb", 0.021, 15.00),
            self.create_dedicated_plan("dedicated-xlarge", "vc2-2c-4gb", 0.027, 20.00),

            // ENTERPRISE HOSTING PLANS (Multiple servers)
            self.create_enterprise_plan("enterprise-starter", "vhf-1c-2gb", 0.016, 12.00),
            self.create_enterprise_plan("enterprise-business", "vhf-2c-4gb", 0.033, 24.00),
            self.create_enterprise_plan("enterprise-premium", "vhf-4c-8gb", 0.066, 48.00),
        ])
    }

    // Get plans by hosting tier
    pub fn get_plans_by_tier(&self, tier: &HostingTier) -> ServiceResult<Vec<CustomPlan>> {
        let all_plans = self.get_all_plans()?;
        Ok(all_plans.into_iter().filter(|plan| &plan.tier == tier).collect())
    }

    // Get specific plan by name
    pub fn get_plan_by_name(&self, plan_name: &str) -> ServiceResult<Option<CustomPlan>> {
        let all_plans = self.get_all_plans()?;
        Ok(all_plans.into_iter().find(|plan| plan.plan_name == plan_name))
    }

    // Get default plan for a hosting tier
    pub fn get_default_plan(&self, tier: &HostingTier) -> ServiceResult<CustomPlan> {
        match tier {
            HostingTier::Shared => Ok(self.create_shared_plan("starter", 0.1, 0.1, 0.1, Some(10.0), Some(100), 0.001, 0.50)),
            HostingTier::Dedicated => Ok(self.create_dedicated_plan("dedicated-small", "vc2-1c-1gb", 0.007, 5.00)),
            HostingTier::Enterprise => Ok(self.create_enterprise_plan("enterprise-starter", "vhf-1c-2gb", 0.016, 12.00)),
        }
    }

    // Helper methods to create plans
    fn create_shared_plan(
        &self,
        name: &str,
        cpu_alloc: f32,
        memory_alloc: f32,
        storage_alloc: f32,
        bandwidth_limit: Option<f32>,
        max_users: Option<u32>,
        price_per_hour: f64,
        price_per_month: f64,
    ) -> CustomPlan {
        CustomPlan {
            tier: HostingTier::Shared,
            plan_name: name.to_string(),
            vultr_plan: None, // Uses your existing allocated servers
            cpu_allocation: cpu_alloc,
            memory_allocation: memory_alloc,
            storage_allocation: storage_alloc,
            bandwidth_limit,
            max_concurrent_users: max_users,
            price_per_hour,
            price_per_month,
        }
    }

    fn create_dedicated_plan(
        &self,
        name: &str,
        vultr_plan: &str,
        price_per_hour: f64,
        price_per_month: f64,
    ) -> CustomPlan {
        CustomPlan {
            tier: HostingTier::Dedicated,
            plan_name: name.to_string(),
            vultr_plan: Some(vultr_plan.to_string()),
            cpu_allocation: 1.0, // 100% of server resources
            memory_allocation: 1.0,
            storage_allocation: 1.0,
            bandwidth_limit: None, // No limit for dedicated
            max_concurrent_users: None, // No limit for dedicated
            price_per_hour,
            price_per_month,
        }
    }

    fn create_enterprise_plan(
        &self,
        name: &str,
        vultr_plan: &str,
        price_per_hour: f64,
        price_per_month: f64,
    ) -> CustomPlan {
        CustomPlan {
            tier: HostingTier::Enterprise,
            plan_name: name.to_string(),
            vultr_plan: Some(vultr_plan.to_string()),
            cpu_allocation: 1.0, // 100% of server resources
            memory_allocation: 1.0,
            storage_allocation: 1.0,
            bandwidth_limit: None, // No limit for enterprise
            max_concurrent_users: None, // No limit for enterprise
            price_per_hour,
            price_per_month,
        }
    }

    // Calculate pricing for shared hosting based on usage
    pub fn calculate_shared_pricing(&self, plan: &CustomPlan, cpu_usage: f32, memory_usage: f32, bandwidth_gb: f32) -> f64 {
        let base_cost = plan.price_per_hour;
        
        // Add overage costs if usage exceeds allocation
        let mut overage_cost = 0.0;
        
        if cpu_usage > plan.cpu_allocation {
            overage_cost += (cpu_usage - plan.cpu_allocation) * 0.01; // $0.01 per % CPU overage
        }
        
        if memory_usage > plan.memory_allocation {
            overage_cost += (memory_usage - plan.memory_allocation) * 0.01; // $0.01 per % memory overage
        }
        
        if let Some(bandwidth_limit) = plan.bandwidth_limit {
            if bandwidth_gb > bandwidth_limit {
                overage_cost += (bandwidth_gb - bandwidth_limit) * 0.05; // $0.05 per GB overage
            }
        }
        
        base_cost + overage_cost
    }

    // Get recommended plan based on application requirements
    pub fn recommend_plan(&self, service_type: &str, expected_traffic: &str) -> ServiceResult<CustomPlan> {
        match (service_type, expected_traffic) {
            ("StaticWebsite", "low") => Ok(self.create_shared_plan("starter", 0.1, 0.1, 0.1, Some(10.0), Some(100), 0.001, 0.50)),
            ("StaticWebsite", "medium") => Ok(self.create_shared_plan("basic", 0.2, 0.2, 0.2, Some(25.0), Some(250), 0.002, 1.00)),
            ("StaticWebsite", "high") => Ok(self.create_dedicated_plan("dedicated-small", "vc2-1c-1gb", 0.007, 5.00)),
            
            ("NodeJsApp" | "PythonApp" | "PhpApp", "low") => Ok(self.create_shared_plan("basic", 0.2, 0.2, 0.2, Some(25.0), Some(250), 0.002, 1.00)),
            ("NodeJsApp" | "PythonApp" | "PhpApp", "medium") => Ok(self.create_shared_plan("pro", 0.5, 0.5, 0.5, Some(100.0), Some(1000), 0.005, 2.50)),
            ("NodeJsApp" | "PythonApp" | "PhpApp", "high") => Ok(self.create_dedicated_plan("dedicated-medium", "vc2-1c-2gb", 0.014, 10.00)),
            
            ("ReactApp" | "VueApp" | "NextJsApp", "low") => Ok(self.create_shared_plan("standard", 0.3, 0.3, 0.3, Some(50.0), Some(500), 0.003, 1.50)),
            ("ReactApp" | "VueApp" | "NextJsApp", "medium") => Ok(self.create_dedicated_plan("dedicated-small", "vc2-1c-1gb", 0.007, 5.00)),
            ("ReactApp" | "VueApp" | "NextJsApp", "high") => Ok(self.create_dedicated_plan("dedicated-large", "vc2-2c-2gb", 0.021, 15.00)),
            
            ("DockerApp", _) => Ok(self.create_dedicated_plan("dedicated-medium", "vc2-1c-2gb", 0.014, 10.00)),
            
            _ => Ok(self.create_shared_plan("starter", 0.1, 0.1, 0.1, Some(10.0), Some(100), 0.001, 0.50)),
        }
    }

    // Get your allocated servers for shared hosting
    pub fn get_shared_servers(&self) -> Vec<String> {
        // This would come from your configuration or database
        // For now, returning example server IDs
        vec![
            "shared-server-1".to_string(),
            "shared-server-2".to_string(),
            "shared-server-3".to_string(),
            "shared-server-4".to_string(),
            "shared-server-5".to_string(),
        ]
    }

    // Get load balancing configuration for shared hosting
    pub fn get_load_balancing_config(&self) -> crate::models::LoadBalancingConfig {
        crate::models::LoadBalancingConfig {
            algorithm: LoadBalancingAlgorithm::RoundRobin,
            health_check_enabled: true,
            failover_enabled: true,
            sticky_sessions: false,
        }
    }
}
