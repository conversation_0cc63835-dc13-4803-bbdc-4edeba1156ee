# Achidas Platform API Documentation

## Overview
Achidas is a Render-like cloud platform service leveraging Vultr infrastructure, designed specifically for the African market with affordable pay-as-you-go pricing.

## Base URL
```
https://api.achidas.com
```

## API Organization
The API is organized into three main categories:

### 🌍 Public APIs (`/api/v1/public/`)
- **No authentication required**
- Public information, webhooks, authentication
- Available to everyone

### 👤 User APIs (`/api/v1/users/`)
- **Authentication required**
- User-specific operations and services
- Platform services, billing, hosting management

### 🔧 Admin APIs (`/api/v1/admin/`)
- **Admin authentication required**
- Administrative operations and Vultr infrastructure management
- System management, user administration, direct Vultr API access

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Response Format
All API responses follow this consistent format:
```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "timestamp": "2025-06-19T11:11:06.315137800+00:00",
    "request_id": "e48cdaa8-4689-4ec2-ae05-3a174d524841",
    "trace_id": "9665c083-568d-4310-83db-136c605d46a7",
    "version": "v1",
    "status_code": 200
  }
}
```

## Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "password is required",
    "details": { ... }
  },
  "meta": { ... }
}
```

---

# 🌍 PUBLIC APIs (`/api/v1/public/`)
*No authentication required*

## 🔐 Authentication

### Register User
```http
POST /api/v1/public/auth/register
```
**Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### Login
```http
POST /api/v1/public/auth/login
```
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### Refresh Token
```http
POST /api/v1/public/auth/refresh
```
**Body:**
```json
{
  "refresh_token": "your_refresh_token"
}
```

## 🏥 Health Check

### System Health
```http
GET /api/v1/public/health
```

## 🔗 Webhooks (Public)

### GitHub Webhook
```http
POST /api/v1/public/webhooks/github
```

### GitLab Webhook
```http
POST /api/v1/public/webhooks/gitlab
```

### Application Webhook
```http
POST /api/v1/public/webhooks/git/{app_id}
```

### Generic Webhook
```http
POST /api/v1/public/webhooks
```

### Webhook Health
```http
GET /api/v1/public/webhooks/health
```

## 🌍 African Regions (Public Information)

### List All African Regions
```http
GET /api/v1/public/african-regions
```
**Query Parameters:**
- `available_only` (boolean): Filter to only available regions
- `country` (string): Filter by country code (e.g., "NG", "ZA")

### Get Specific Region
```http
GET /api/v1/public/african-regions/{region_code}
```
**Example:** `/api/v1/public/african-regions/ng-lag` (Lagos, Nigeria)

### Get Region Pricing
```http
GET /api/v1/public/african-regions/{region_code}/pricing
```

### List All African Countries
```http
GET /api/v1/public/countries
```

### Get Country Regions
```http
GET /api/v1/public/countries/{country_code}/regions
```
**Example:** `/api/v1/public/countries/NG/regions` (All regions in Nigeria)

## 🎯 Hosting Plans (Public Information)

### List Hosting Plans
```http
GET /api/v1/public/hosting/plans
```

### Get Specific Plan
```http
GET /api/v1/public/hosting/plans/{plan_name}
```

### Get African Pricing
```http
GET /api/v1/public/hosting/pricing/{country_code}
```
**Example:** `/api/v1/public/hosting/pricing/NG` (Nigeria pricing)

### Hosting Health
```http
GET /api/v1/public/hosting/health
```

---

# 👤 USER APIs (`/api/v1/users/`)
*Requires Authentication: `Authorization: Bearer <token>`*

## 👤 User Profile

### Get User Profile
```http
GET /api/v1/users/profile
```

## 🚀 Application Management

### List Applications
```http
GET /api/v1/users/applications
```
**Query Parameters:**
- `page` (number): Page number (default: 1)
- `per_page` (number): Items per page (default: 20)

### Create Application
```http
POST /api/v1/users/applications
```
**Body:**
```json
{
  "name": "my-web-app",
  "description": "My awesome web application",
  "repository": {
    "url": "https://github.com/user/repo.git",
    "branch": "main"
  },
  "service_type": "web_service",
  "region": "ng-lag",
  "runtime_config": {
    "build_command": "npm run build",
    "start_command": "npm start",
    "environment": "node"
  }
}
```

### Get Application
```http
GET /api/v1/users/applications/{app_id}
```

### Update Application
```http
PUT /api/v1/users/applications/{app_id}
```

### Delete Application
```http
DELETE /api/v1/users/applications/{app_id}
```

### Deploy Application
```http
POST /api/v1/users/applications/{app_id}/deploy
```

### List Deployments
```http
GET /api/v1/users/applications/{app_id}/deployments
```

### Get Deployment Details
```http
GET /api/v1/users/applications/{app_id}/deployments/{deployment_id}
```

### Rollback Deployment
```http
POST /api/v1/users/applications/{app_id}/deployments/{deployment_id}/rollback
```

## 🔧 Environment Variables & Secrets

### List Environment Groups
```http
GET /api/v1/users/environment-groups
```

### Create Environment Group
```http
POST /api/v1/users/environment-groups
```

### Get Environment Group
```http
GET /api/v1/users/environment-groups/{group_id}
```

### Set Environment Variable
```http
POST /api/v1/users/environment-groups/{group_id}/variables
```
**Body:**
```json
{
  "key": "DATABASE_URL",
  "value": "mongodb://localhost:27017/mydb"
}
```

### Delete Environment Variable
```http
DELETE /api/v1/users/environment-groups/{group_id}/variables/{key}
```

### Set Secret
```http
POST /api/v1/users/environment-groups/{group_id}/secrets
```
**Body:**
```json
{
  "key": "API_SECRET",
  "value": "super-secret-value"
}
```

### Delete Secret
```http
DELETE /api/v1/users/environment-groups/{group_id}/secrets/{key}
```

### Link Application
```http
POST /api/v1/users/environment-groups/{group_id}/link
```

## 📊 Logs & Monitoring

### Get Application Logs
```http
GET /api/v1/users/applications/{app_id}/logs
```
**Query Parameters:**
- `lines` (number): Number of log lines (default: 100)
- `follow` (boolean): Stream logs in real-time
- `since` (string): ISO timestamp to get logs since

### Stream Application Logs
```http
GET /api/v1/users/applications/{app_id}/logs/stream
```

### Get Deployment Logs
```http
GET /api/v1/users/applications/{app_id}/deployments/{deployment_id}/logs
```

### Get Build Logs
```http
GET /api/v1/users/builds/{job_id}/logs
```

### Stream Build Logs
```http
GET /api/v1/users/builds/{job_id}/logs/stream
```

## 💰 Account & Billing

### Get Account Information
```http
GET /api/v1/users/account
```

### Get Account Usage
```http
GET /api/v1/users/account/usage
```

### Get Billing Information
```http
GET /api/v1/users/billing
```

### List Invoices
```http
GET /api/v1/users/billing/invoices
```

### Get Invoice Details
```http
GET /api/v1/users/billing/invoices/{id}
```

### Get Usage Details
```http
GET /api/v1/users/billing/usage
```

## 🏗️ Infrastructure Management

### List Instances
```http
GET /api/v1/users/instances
```

### Get Instance Details
```http
GET /api/v1/users/instances/{id}
```

### Delete Instance
```http
DELETE /api/v1/users/instances/{id}
```

### Start Instance
```http
POST /api/v1/users/instances/{id}/start
```

### Stop Instance
```http
POST /api/v1/users/instances/{id}/stop
```

### Restart Instance
```http
POST /api/v1/users/instances/{id}/restart
```

## 🔑 SSH Keys Management

### List SSH Keys
```http
GET /api/v1/users/ssh-keys
```

### Create SSH Key
```http
POST /api/v1/users/ssh-keys
```
**Body:**
```json
{
  "name": "my-ssh-key",
  "ssh_key": "ssh-rsa AAAAB3NzaC1yc2E..."
}
```

### Get SSH Key
```http
GET /api/v1/users/ssh-keys/{id}
```

### Update SSH Key
```http
PATCH /api/v1/users/ssh-keys/{id}
```

### Delete SSH Key
```http
DELETE /api/v1/users/ssh-keys/{id}
```

## 🎯 Hosting Services

### List Vultr Servers
```http
GET /api/v1/users/hosting/servers/vultr
```

### Get Server Details
```http
GET /api/v1/users/hosting/servers/vultr/{instance_id}
```

### Import Existing Server
```http
POST /api/v1/users/hosting/servers/import/{instance_id}
```

### Get Server Setup
```http
GET /api/v1/users/hosting/servers/setup/{instance_id}
```

### Deploy User
```http
POST /api/v1/users/hosting/deploy/user/{user_id}
```

### Get Infrastructure Status
```http
GET /api/v1/users/hosting/infrastructure/status
```

### Get Scaling Recommendations
```http
GET /api/v1/users/hosting/scaling/recommendations
```

### Execute Scaling Action
```http
POST /api/v1/users/hosting/scaling/execute
```

---

# 🔧 ADMIN APIs (`/api/v1/admin/`)
*Requires Admin Authentication: `Authorization: Bearer <admin_token>`*

## 👥 User Management

### List All Users
```http
GET /api/v1/admin/users
```

### Get User Details
```http
GET /api/v1/admin/users/{user_id}
```

### Suspend User
```http
POST /api/v1/admin/users/{user_id}/suspend
```

### Activate User
```http
POST /api/v1/admin/users/{user_id}/activate
```

## 📊 System Management

### List All Instances
```http
GET /api/v1/admin/instances
```

### Get Billing Overview
```http
GET /api/v1/admin/billing/overview
```

### Get System Metrics
```http
GET /api/v1/admin/metrics
```

### Calculate Profit Analysis
```http
POST /api/v1/admin/analytics/profit
```

## 🌐 Vultr Infrastructure (Admin Only)

### Plans & Regions
```http
GET /api/v1/admin/vultr/plans
GET /api/v1/admin/vultr/plans-metal
GET /api/v1/admin/vultr/regions
GET /api/v1/admin/vultr/regions/{region_id}/availability
GET /api/v1/admin/vultr/os
GET /api/v1/admin/vultr/backups
```

### Dedicated Servers
```http
GET /api/v1/admin/vultr/servers
POST /api/v1/admin/vultr/servers
GET /api/v1/admin/vultr/servers/{id}
PUT /api/v1/admin/vultr/servers/{id}
DELETE /api/v1/admin/vultr/servers/{id}
```

### Server Operations
```http
POST /api/v1/admin/vultr/servers/bare-metal/{id}/start
POST /api/v1/admin/vultr/servers/bare-metal/{id}/reboot
POST /api/v1/admin/vultr/servers/bare-metal/{id}/reinstall
POST /api/v1/admin/vultr/servers/bare-metal/{id}/halt
```

### Block Storage
```http
GET /api/v1/admin/vultr/storage/blocks
POST /api/v1/admin/vultr/storage/blocks
GET /api/v1/admin/vultr/storage/blocks/{id}
PUT /api/v1/admin/vultr/storage/blocks/{id}
DELETE /api/v1/admin/vultr/storage/blocks/{id}
POST /api/v1/admin/vultr/storage/blocks/{id}/attach
POST /api/v1/admin/vultr/storage/blocks/{id}/detach
```

### CDN Management
```http
GET /api/v1/admin/vultr/cdn/pull-zones
POST /api/v1/admin/vultr/cdn/pull-zones
GET /api/v1/admin/vultr/cdn/pull-zones/{id}
PUT /api/v1/admin/vultr/cdn/pull-zones/{id}
DELETE /api/v1/admin/vultr/cdn/pull-zones/{id}
```

### Kubernetes Clusters
```http
GET /api/v1/admin/vultr/kubernetes/clusters
POST /api/v1/admin/vultr/kubernetes/clusters
GET /api/v1/admin/vultr/kubernetes/clusters/{vke_id}
PUT /api/v1/admin/vultr/kubernetes/clusters/{vke_id}
DELETE /api/v1/admin/vultr/kubernetes/clusters/{vke_id}
```

### Load Balancers
```http
GET /api/v1/admin/vultr/load-balancers
POST /api/v1/admin/vultr/load-balancers
GET /api/v1/admin/vultr/load-balancers/{load_balancer_id}
PATCH /api/v1/admin/vultr/load-balancers/{load_balancer_id}
DELETE /api/v1/admin/vultr/load-balancers/{load_balancer_id}
```

### Managed Databases
```http
GET /api/v1/admin/vultr/databases
POST /api/v1/admin/vultr/databases
GET /api/v1/admin/vultr/databases/{database_id}
PUT /api/v1/admin/vultr/databases/{database_id}
DELETE /api/v1/admin/vultr/databases/{database_id}
```

### DNS Management
```http
GET /api/v1/admin/vultr/dns/domains
POST /api/v1/admin/vultr/dns/domains
GET /api/v1/admin/vultr/dns/domains/{domain}
PUT /api/v1/admin/vultr/dns/domains/{domain}
DELETE /api/v1/admin/vultr/dns/domains/{domain}
```

### Firewall Management
```http
GET /api/v1/admin/vultr/firewalls
POST /api/v1/admin/vultr/firewalls
GET /api/v1/admin/vultr/firewalls/{firewall_group_id}
PUT /api/v1/admin/vultr/firewalls/{firewall_group_id}
DELETE /api/v1/admin/vultr/firewalls/{firewall_group_id}
```

### Object Storage
```http
GET /api/v1/admin/vultr/object-storage
POST /api/v1/admin/vultr/object-storage
GET /api/v1/admin/vultr/object-storage/{object_storage_id}
PUT /api/v1/admin/vultr/object-storage/{object_storage_id}
DELETE /api/v1/admin/vultr/object-storage/{object_storage_id}
```

### VPC Management
```http
GET /api/v1/admin/vultr/vpcs
POST /api/v1/admin/vultr/vpcs
GET /api/v1/admin/vultr/vpcs/{vpc_id}
PUT /api/v1/admin/vultr/vpcs/{vpc_id}
DELETE /api/v1/admin/vultr/vpcs/{vpc_id}
```

### Reserved IPs
```http
GET /api/v1/admin/vultr/reserved-ips
POST /api/v1/admin/vultr/reserved-ips
GET /api/v1/admin/vultr/reserved-ips/{reserved_ip}
PATCH /api/v1/admin/vultr/reserved-ips/{reserved_ip}
DELETE /api/v1/admin/vultr/reserved-ips/{reserved_ip}
```

---

## 🌐 Available African Regions

| Region Code | Country | City | Status | Pricing Tier |
|-------------|---------|------|--------|--------------|
| `za-jhb` | South Africa | Johannesburg | ✅ Available | Standard |
| `za-cpt` | South Africa | Cape Town | ✅ Available | Standard |
| `ng-lag` | Nigeria | Lagos | ✅ Available | Economy |
| `ng-abj` | Nigeria | Abuja | ✅ Available | Economy |
| `ke-nai` | Kenya | Nairobi | ✅ Available | Economy |
| `eg-cai` | Egypt | Cairo | ✅ Available | Standard |
| `gh-acc` | Ghana | Accra | ✅ Available | Economy |
| `ma-cas` | Morocco | Casablanca | ✅ Available | Standard |
| `et-add` | Ethiopia | Addis Ababa | 🚧 Coming Soon | Economy |
| `tz-dar` | Tanzania | Dar es Salaam | 🚧 Coming Soon | Economy |
| `ug-kam` | Uganda | Kampala | 🚧 Coming Soon | Economy |
| `rw-kig` | Rwanda | Kigali | 🚧 Coming Soon | Economy |

---

## 💡 Getting Started

### For Regular Users:
1. **Register an account** using `/api/v1/public/auth/register`
2. **Login** to get your JWT token using `/api/v1/public/auth/login`
3. **Choose a region** from `/api/v1/public/african-regions`
4. **Create an application** using `/api/v1/users/applications`
5. **Deploy your app** using `/api/v1/users/applications/{app_id}/deploy`
6. **Monitor logs** using `/api/v1/users/applications/{app_id}/logs`


### For Administrators:
1. **Use admin credentials** to access admin endpoints
2. **Manage infrastructure** via `/api/v1/admin/vultr/*` endpoints
3. **Monitor system health** using `/api/v1/admin/metrics`
4. **Manage users** via `/api/v1/admin/users/*` endpoints

### Importing Existing Vultr Servers:
If you already have servers running on Vultr, you can import them:
1. **List your servers** using `/api/v1/users/hosting/servers/vultr`
2. **Import a server** using `/api/v1/users/hosting/servers/import/{instance_id}`
3. **Get setup instructions** using `/api/v1/users/hosting/servers/setup/{instance_id}`

## 🔑 API Key Managementc

### Using Your Vultr API Key:
- Set your Vultr API key in the platform settings
- The platform will use your key to manage your existing infrastructure
- All Vultr operations will be performed using your credentials

## 📊 Service Types Comparison

| Service Type | Achidas | Render Equivalent |
|--------------|---------|-------------------|
| Web Services | ✅ Available | Web Services |
| Static Sites | ✅ Available | Static Sites |
| Background Workers | ✅ Available | Background Workers |
| Cron Jobs | ✅ Available | Cron Jobs |
| Private Services | ✅ Available | Private Services |
| Databases | ✅ Available | PostgreSQL/Redis |
| Object Storage | ✅ Available | - |
| CDN | ✅ Available | - |
| Load Balancers | ✅ Available | - |

## 📞 Support

For support, contact <NAME_EMAIL> or visit our documentation at https://docs.achidas.com

## 🔒 Rate Limits

- **Public APIs**: 100 requests per minute
- **User APIs**: 1000 requests per minute
- **Admin APIs**: 5000 requests per minute
- **Vultr APIs**: 30 requests per second (Vultr limit)
