fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, axum::J<PERSON><CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<ApplicationResponse>>, ControllerError>> {update_application}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<ApplicationResponse>>, ControllerError>> {update_application}: Handler<_, _>
