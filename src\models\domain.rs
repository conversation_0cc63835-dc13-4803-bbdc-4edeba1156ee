use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomDomain {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub service_id: ObjectId,
    pub user_id: ObjectId,
    pub status: DomainStatus,
    pub verification: DomainVerification,
    pub ssl_certificate: Option<SSLCertificate>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum DomainStatus {
    Pending,
    Verified,
    Active,
    Error,
    Suspended,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainVerification {
    pub dns_record: String,
    pub dns_value: String,
    pub http_path: String,
    pub http_content: String,
    pub verified: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub verified_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SSLCertificate {
    pub certificate_id: String,
    pub status: SSLStatus,
    pub issued_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub auto_renew: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SSLStatus {
    Pending,
    Active,
    Expired,
    Error,
    Renewing,
}
