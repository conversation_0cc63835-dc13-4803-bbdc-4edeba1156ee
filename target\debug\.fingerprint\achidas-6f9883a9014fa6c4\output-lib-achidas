{"$message_type":"diagnostic","message":"unused imports: `AddDatabaseReadReplicaRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrStorageGatewayExportConfig`, `VultrVFSAttachment`, and `models::{\n    }`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\vultr.rs","byte_start":72,"byte_end":87,"line_start":3,"line_end":4,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    models::{","highlight_start":5,"highlight_end":14},{"text":"    },","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2621,"byte_end":2636,"line_start":34,"line_end":34,"column_start":36,"column_end":51,"is_primary":true,"text":[{"text":"        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,","highlight_start":36,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2638,"byte_end":2661,"line_start":34,"line_end":34,"column_start":53,"column_end":76,"is_primary":true,"text":[{"text":"        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,","highlight_start":53,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2671,"byte_end":2689,"line_start":35,"line_end":35,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2691,"byte_end":2717,"line_start":35,"line_end":35,"column_start":29,"column_end":55,"is_primary":true,"text":[{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":29,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2719,"byte_end":2745,"line_start":35,"line_end":35,"column_start":57,"column_end":83,"is_primary":true,"text":[{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":57,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2755,"byte_end":2773,"line_start":36,"line_end":36,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2775,"byte_end":2801,"line_start":36,"line_end":36,"column_start":29,"column_end":55,"is_primary":true,"text":[{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":29,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2803,"byte_end":2825,"line_start":36,"line_end":36,"column_start":57,"column_end":79,"is_primary":true,"text":[{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":57,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2835,"byte_end":2865,"line_start":37,"line_end":37,"column_start":9,"column_end":39,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,","highlight_start":9,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2867,"byte_end":2897,"line_start":37,"line_end":37,"column_start":41,"column_end":71,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,","highlight_start":41,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2907,"byte_end":2935,"line_start":38,"line_end":38,"column_start":9,"column_end":37,"is_primary":true,"text":[{"text":"        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,","highlight_start":9,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2937,"byte_end":2967,"line_start":38,"line_end":38,"column_start":39,"column_end":69,"is_primary":true,"text":[{"text":"        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,","highlight_start":39,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2977,"byte_end":3002,"line_start":39,"line_end":39,"column_start":9,"column_end":34,"is_primary":true,"text":[{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":9,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3004,"byte_end":3032,"line_start":39,"line_end":39,"column_start":36,"column_end":64,"is_primary":true,"text":[{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":36,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3034,"byte_end":3063,"line_start":39,"line_end":39,"column_start":66,"column_end":95,"is_primary":true,"text":[{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":66,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3073,"byte_end":3102,"line_start":40,"line_end":40,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3104,"byte_end":3128,"line_start":40,"line_end":40,"column_start":40,"column_end":64,"is_primary":true,"text":[{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":40,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3130,"byte_end":3153,"line_start":40,"line_end":40,"column_start":66,"column_end":89,"is_primary":true,"text":[{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":66,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3163,"byte_end":3195,"line_start":41,"line_end":41,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3197,"byte_end":3216,"line_start":41,"line_end":41,"column_start":43,"column_end":62,"is_primary":true,"text":[{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":43,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3218,"byte_end":3245,"line_start":41,"line_end":41,"column_start":64,"column_end":91,"is_primary":true,"text":[{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":64,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3255,"byte_end":3290,"line_start":42,"line_end":42,"column_start":9,"column_end":44,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,","highlight_start":9,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3292,"byte_end":3327,"line_start":42,"line_end":42,"column_start":46,"column_end":81,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,","highlight_start":46,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3337,"byte_end":3365,"line_start":43,"line_end":43,"column_start":9,"column_end":37,"is_primary":true,"text":[{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":9,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3367,"byte_end":3388,"line_start":43,"line_end":43,"column_start":39,"column_end":60,"is_primary":true,"text":[{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":39,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3390,"byte_end":3424,"line_start":43,"line_end":43,"column_start":62,"column_end":96,"is_primary":true,"text":[{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":62,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4316,"byte_end":4349,"line_start":55,"line_end":55,"column_start":9,"column_end":42,"is_primary":true,"text":[{"text":"        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4351,"byte_end":4382,"line_start":55,"line_end":55,"column_start":44,"column_end":75,"is_primary":true,"text":[{"text":"        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,","highlight_start":44,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4495,"byte_end":4513,"line_start":57,"line_end":57,"column_start":27,"column_end":45,"is_primary":true,"text":[{"text":"        UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,","highlight_start":27,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\vultr.rs","byte_start":66,"byte_end":87,"line_start":2,"line_end":4,"column_start":54,"column_end":6,"is_primary":true,"text":[{"text":"    controllers::{success_response, ControllerResult},","highlight_start":54,"highlight_end":55},{"text":"    models::{","highlight_start":1,"highlight_end":14},{"text":"    },","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2619,"byte_end":3424,"line_start":34,"line_end":43,"column_start":34,"column_end":96,"is_primary":true,"text":[{"text":"        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,","highlight_start":34,"highlight_end":77},{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":1,"highlight_end":84},{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":1,"highlight_end":80},{"text":"        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,","highlight_start":1,"highlight_end":72},{"text":"        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,","highlight_start":1,"highlight_end":70},{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":1,"highlight_end":96},{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":1,"highlight_end":90},{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":1,"highlight_end":92},{"text":"        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,","highlight_start":1,"highlight_end":82},{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":1,"highlight_end":96}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4306,"byte_end":4382,"line_start":54,"line_end":55,"column_start":86,"column_end":75,"is_primary":true,"text":[{"text":"        VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,","highlight_start":86,"highlight_end":87},{"text":"        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,","highlight_start":1,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4493,"byte_end":4513,"line_start":57,"line_end":57,"column_start":25,"column_end":45,"is_primary":true,"text":[{"text":"        UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,","highlight_start":25,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AddDatabaseReadReplicaRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrStorageGatewayExportConfig`, `VultrVFSAttachment`, and `models::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;15m             }`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\vultr.rs:3:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    models::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    },\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|_____^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `EnvironmentGroup`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\environment.rs","byte_start":150,"byte_end":166,"line_start":4,"line_end":4,"column_start":49,"column_end":65,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":49,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\environment.rs","byte_start":148,"byte_end":166,"line_start":4,"line_end":4,"column_start":47,"column_end":65,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":47,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\environment.rs","byte_start":129,"byte_end":130,"line_start":4,"line_end":4,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\environment.rs","byte_start":166,"byte_end":167,"line_start":4,"line_end":4,"column_start":65,"column_end":66,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":65,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `EnvironmentGroup`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\environment.rs:4:49\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    services::environment::{EnvironmentService, EnvironmentGroup},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":338,"byte_end":342,"line_start":13,"line_end":13,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use futures::stream::{self, Stream};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":338,"byte_end":344,"line_start":13,"line_end":13,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"use futures::stream::{self, Stream};","highlight_start":23,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:13:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::stream::{self, Stream};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":10673,"byte_end":10700,"line_start":305,"line_end":305,"column_start":13,"column_end":40,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":13,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":10669,"byte_end":10701,"line_start":305,"line_end":305,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::with_circuit_breaker`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:305:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        use crate::with_circuit_breaker;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":14188,"byte_end":14215,"line_start":401,"line_end":401,"column_start":13,"column_end":40,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":13,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":14184,"byte_end":14216,"line_start":401,"line_end":401,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::with_circuit_breaker`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:401:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m401\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        use crate::with_circuit_breaker;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `StatusCode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":276,"byte_end":286,"line_start":9,"line_end":9,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":274,"byte_end":286,"line_start":9,"line_end":9,"column_start":21,"column_end":33,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":21,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":264,"byte_end":265,"line_start":9,"line_end":9,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":286,"byte_end":287,"line_start":9,"line_end":9,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `StatusCode`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\webhooks.rs:9:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::{HeaderMap, StatusCode},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":363,"byte_end":368,"line_start":14,"line_end":14,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":363,"byte_end":370,"line_start":14,"line_end":14,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\webhooks.rs:14:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json::json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\mod.rs","byte_start":387,"byte_end":403,"line_start":21,"line_end":21,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use serde_json::json;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\mod.rs","byte_start":383,"byte_end":405,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::json;","highlight_start":1,"highlight_end":22},{"text":"use thiserror::Error;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `serde_json::json`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\mod.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::json;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ServiceResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":55,"byte_end":68,"line_start":2,"line_end":2,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":53,"byte_end":68,"line_start":2,"line_end":2,"column_start":35,"column_end":50,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":35,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":40,"byte_end":41,"line_start":2,"line_end":2,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":68,"byte_end":69,"line_start":2,"line_end":2,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ServiceResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\circuit_breaker.rs:2:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::services::{ServiceError, ServiceResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\state_machine.rs","byte_start":77,"byte_end":82,"line_start":3,"line_end":3,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\state_machine.rs","byte_start":75,"byte_end":82,"line_start":3,"line_end":3,"column_start":25,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":25,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\state_machine.rs:3:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `clock::DefaultClock` and `state::InMemoryState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":61,"byte_end":80,"line_start":1,"line_end":1,"column_start":62,"column_end":81,"is_primary":true,"text":[{"text":"use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};","highlight_start":62,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":82,"byte_end":102,"line_start":1,"line_end":1,"column_start":83,"column_end":103,"is_primary":true,"text":[{"text":"use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};","highlight_start":83,"highlight_end":103}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":59,"byte_end":102,"line_start":1,"line_end":1,"column_start":60,"column_end":103,"is_primary":true,"text":[{"text":"use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};","highlight_start":60,"highlight_end":103}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `clock::DefaultClock` and `state::InMemoryState`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\rate_limiter.rs:1:62\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\metrics.rs","byte_start":204,"byte_end":209,"line_start":4,"line_end":4,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\metrics.rs","byte_start":202,"byte_end":209,"line_start":4,"line_end":4,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\metrics.rs","byte_start":197,"byte_end":198,"line_start":4,"line_end":4,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\metrics.rs","byte_start":209,"byte_end":210,"line_start":4,"line_end":4,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\metrics.rs:4:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, error};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::VecDeque`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":210,"byte_end":236,"line_start":7,"line_end":7,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"use std::collections::VecDeque;","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":206,"byte_end":238,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::VecDeque;","highlight_start":1,"highlight_end":32},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::VecDeque`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\chunk_processor.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::VecDeque;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\middleware\\auth.rs","byte_start":272,"byte_end":277,"line_start":14,"line_end":14,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\middleware\\auth.rs","byte_start":272,"byte_end":279,"line_start":14,"line_end":14,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\auth.rs","byte_start":271,"byte_end":272,"line_start":14,"line_end":14,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\auth.rs","byte_start":283,"byte_end":284,"line_start":14,"line_end":14,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\middleware\\auth.rs:14:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HeaderValue` and `response::Response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\middleware\\cors.rs","byte_start":31,"byte_end":42,"line_start":2,"line_end":2,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"    http::{header, HeaderValue, Method},","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":57,"byte_end":75,"line_start":3,"line_end":3,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    response::Response,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\middleware\\cors.rs","byte_start":29,"byte_end":42,"line_start":2,"line_end":2,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    http::{header, HeaderValue, Method},","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":51,"byte_end":75,"line_start":2,"line_end":3,"column_start":40,"column_end":23,"is_primary":true,"text":[{"text":"    http::{header, HeaderValue, Method},","highlight_start":40,"highlight_end":41},{"text":"    response::Response,","highlight_start":1,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":10,"byte_end":16,"line_start":1,"line_end":2,"column_start":11,"column_end":5,"is_primary":true,"text":[{"text":"use axum::{","highlight_start":11,"highlight_end":12},{"text":"    http::{header, HeaderValue, Method},","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":75,"byte_end":78,"line_start":3,"line_end":4,"column_start":23,"column_end":2,"is_primary":true,"text":[{"text":"    response::Response,","highlight_start":23,"highlight_end":24},{"text":"};","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `HeaderValue` and `response::Response`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\middleware\\cors.rs:2:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::{header, HeaderValue, Method},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    response::Response,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HeaderMap` and `extract::Query`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\middleware\\mod.rs","byte_start":123,"byte_end":132,"line_start":7,"line_end":7,"column_start":46,"column_end":55,"is_primary":true,"text":[{"text":"    http::{Request, StatusCode, HeaderValue, HeaderMap},","highlight_start":46,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\middleware\\mod.rs","byte_start":227,"byte_end":241,"line_start":12,"line_end":12,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    extract::Query,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\middleware\\mod.rs","byte_start":121,"byte_end":132,"line_start":7,"line_end":7,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"    http::{Request, StatusCode, HeaderValue, HeaderMap},","highlight_start":44,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\mod.rs","byte_start":221,"byte_end":241,"line_start":11,"line_end":12,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    Json,","highlight_start":9,"highlight_end":10},{"text":"    extract::Query,","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `HeaderMap` and `extract::Query`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\middleware\\mod.rs:7:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::{Request, StatusCode, HeaderValue, HeaderMap},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::Query,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `validator::Validate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\user.rs","byte_start":95,"byte_end":114,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\user.rs","byte_start":91,"byte_end":116,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":1,"highlight_end":25},{"text":"use axum::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `validator::Validate`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\user.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse validator::Validate;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `validator::Validate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\instance.rs","byte_start":95,"byte_end":114,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\instance.rs","byte_start":91,"byte_end":116,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `validator::Validate`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\instance.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse validator::Validate;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\regions.rs","byte_start":41,"byte_end":66,"line_start":2,"line_end":2,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\regions.rs","byte_start":37,"byte_end":68,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\regions.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Level`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":338,"byte_end":343,"line_start":13,"line_end":13,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":336,"byte_end":343,"line_start":13,"line_end":13,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\observability\\mod.rs","byte_start":331,"byte_end":332,"line_start":13,"line_end":13,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\observability\\mod.rs","byte_start":343,"byte_end":344,"line_start":13,"line_end":13,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Level`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\observability\\mod.rs:13:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, Level};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\billing.rs","byte_start":399,"byte_end":407,"line_start":14,"line_end":14,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Datelike, Timelike};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\billing.rs","byte_start":409,"byte_end":412,"line_start":14,"line_end":14,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Datelike, Timelike};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\billing.rs","byte_start":399,"byte_end":414,"line_start":14,"line_end":14,"column_start":14,"column_end":29,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Datelike, Timelike};","highlight_start":14,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DateTime` and `Utc`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\billing.rs:14:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc, Datelike, Timelike};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":522,"byte_end":547,"line_start":17,"line_end":17,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":518,"byte_end":549,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use tracing::{error, info, instrument, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":609,"byte_end":629,"line_start":15,"line_end":15,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    with_circuit_breaker,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":603,"byte_end":629,"line_start":14,"line_end":15,"column_start":23,"column_end":25,"is_primary":true,"text":[{"text":"    vultr::VultrClient,","highlight_start":23,"highlight_end":24},{"text":"    with_circuit_breaker,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `with_circuit_breaker`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    with_circuit_breaker,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":739,"byte_end":764,"line_start":21,"line_end":21,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":735,"byte_end":766,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::path::Path;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::process::Stdio`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":791,"byte_end":810,"line_start":23,"line_end":23,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::process::Stdio;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":787,"byte_end":812,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::process::Stdio;","highlight_start":1,"highlight_end":25},{"text":"use tokio::process::Command;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::process::Stdio`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:23:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::process::Stdio;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::process::Command`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":816,"byte_end":839,"line_start":24,"line_end":24,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::process::Command;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":812,"byte_end":841,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::process::Command;","highlight_start":1,"highlight_end":29},{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tokio::process::Command`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::process::Command;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AsyncBufReadExt` and `BufReader`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":857,"byte_end":872,"line_start":25,"line_end":25,"column_start":17,"column_end":32,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":17,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":888,"byte_end":897,"line_start":25,"line_end":25,"column_start":48,"column_end":57,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":48,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":857,"byte_end":874,"line_start":25,"line_end":25,"column_start":17,"column_end":34,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":17,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\build.rs","byte_start":886,"byte_end":897,"line_start":25,"line_end":25,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\build.rs","byte_start":856,"byte_end":857,"line_start":25,"line_end":25,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\build.rs","byte_start":897,"byte_end":898,"line_start":25,"line_end":25,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AsyncBufReadExt` and `BufReader`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:25:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":939,"byte_end":943,"line_start":26,"line_end":26,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":937,"byte_end":943,"line_start":26,"line_end":26,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:26:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":950,"byte_end":960,"line_start":27,"line_end":27,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":946,"byte_end":962,"line_start":27,"line_end":28,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AutoScalingConfig`, `BlueprintServiceType as ServiceType`, `DeploymentTrigger`, `Environment`, and `with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":371,"byte_end":388,"line_start":10,"line_end":10,"column_start":52,"column_end":69,"is_primary":true,"text":[{"text":"        Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,","highlight_start":52,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":518,"byte_end":529,"line_start":12,"line_end":12,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":567,"byte_end":602,"line_start":12,"line_end":12,"column_start":58,"column_end":93,"is_primary":true,"text":[{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":58,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":612,"byte_end":629,"line_start":13,"line_end":13,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        AutoScalingConfig, PaginationQuery","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":765,"byte_end":785,"line_start":17,"line_end":17,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    with_circuit_breaker,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":369,"byte_end":388,"line_start":10,"line_end":10,"column_start":50,"column_end":69,"is_primary":true,"text":[{"text":"        Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,","highlight_start":50,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":508,"byte_end":529,"line_start":11,"line_end":12,"column_start":100,"column_end":20,"is_primary":true,"text":[{"text":"        CreateApplicationRequest, ApplicationResponse, DeploymentResponse, TriggerDeploymentRequest,","highlight_start":100,"highlight_end":101},{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":565,"byte_end":629,"line_start":12,"line_end":13,"column_start":56,"column_end":26,"is_primary":true,"text":[{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":56,"highlight_end":94},{"text":"        AutoScalingConfig, PaginationQuery","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":759,"byte_end":785,"line_start":16,"line_end":17,"column_start":23,"column_end":25,"is_primary":true,"text":[{"text":"    vultr::VultrClient,","highlight_start":23,"highlight_end":24},{"text":"    with_circuit_breaker,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AutoScalingConfig`, `BlueprintServiceType as ServiceType`, `DeploymentTrigger`, `Environment`, and `with_circuit_breaker`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:10:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        CreateApplicationRequest, ApplicationResponse, DeploymentResponse, TriggerDeploymentRequest,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        AutoScalingConfig, PaginationQuery\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    with_circuit_breaker,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":895,"byte_end":920,"line_start":23,"line_end":23,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":891,"byte_end":922,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use tracing::{error, info, instrument, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:23:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":936,"byte_end":941,"line_start":24,"line_end":24,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":936,"byte_end":943,"line_start":24,"line_end":24,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:24:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DiskPagination as Pagination`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":264,"byte_end":292,"line_start":8,"line_end":8,"column_start":46,"column_end":74,"is_primary":true,"text":[{"text":"    models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},","highlight_start":46,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\disk.rs","byte_start":262,"byte_end":292,"line_start":8,"line_end":8,"column_start":44,"column_end":74,"is_primary":true,"text":[{"text":"    models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},","highlight_start":44,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `DiskPagination as Pagination`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\disk.rs:8:46\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":545,"byte_end":550,"line_start":18,"line_end":18,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":570,"byte_end":574,"line_start":18,"line_end":18,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\disk.rs","byte_start":545,"byte_end":552,"line_start":18,"line_end":18,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":568,"byte_end":574,"line_start":18,"line_end":18,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\disk.rs:18:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\domain.rs","byte_start":451,"byte_end":456,"line_start":17,"line_end":17,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\domain.rs","byte_start":476,"byte_end":480,"line_start":17,"line_end":17,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\domain.rs","byte_start":451,"byte_end":458,"line_start":17,"line_end":17,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\domain.rs","byte_start":474,"byte_end":480,"line_start":17,"line_end":17,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\domain.rs:17:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Environment`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\environment.rs","byte_start":83,"byte_end":94,"line_start":4,"line_end":4,"column_start":27,"column_end":38,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":27,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\environment.rs","byte_start":81,"byte_end":94,"line_start":4,"line_end":4,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\environment.rs","byte_start":69,"byte_end":70,"line_start":4,"line_end":4,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\environment.rs","byte_start":94,"byte_end":95,"line_start":4,"line_end":4,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Environment`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\environment.rs:4:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    models::{Application, Environment},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\environment.rs","byte_start":456,"byte_end":461,"line_start":18,"line_end":18,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\environment.rs","byte_start":456,"byte_end":463,"line_start":18,"line_end":18,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\environment.rs:18:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `GitCommit` and `GitUser`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":188,"byte_end":197,"line_start":6,"line_end":6,"column_start":53,"column_end":62,"is_primary":true,"text":[{"text":"        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser","highlight_start":53,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\git.rs","byte_start":199,"byte_end":206,"line_start":6,"line_end":6,"column_start":64,"column_end":71,"is_primary":true,"text":[{"text":"        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser","highlight_start":64,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":186,"byte_end":206,"line_start":6,"line_end":6,"column_start":51,"column_end":71,"is_primary":true,"text":[{"text":"        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser","highlight_start":51,"highlight_end":71}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `GitCommit` and `GitUser`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:6:53\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":266,"byte_end":280,"line_start":10,"line_end":10,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":262,"byte_end":282,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use bson::{doc, oid::ObjectId};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `chrono::Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":318,"byte_end":329,"line_start":12,"line_end":12,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use chrono::Utc;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":314,"byte_end":331,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::Utc;","highlight_start":1,"highlight_end":17},{"text":"use mongodb::Collection;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `chrono::Utc`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::Utc;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":418,"byte_end":443,"line_start":16,"line_end":16,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":414,"byte_end":445,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use tracing::{error, info, instrument, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":459,"byte_end":464,"line_start":17,"line_end":17,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\git.rs","byte_start":466,"byte_end":470,"line_start":17,"line_end":17,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":459,"byte_end":472,"line_start":17,"line_end":17,"column_start":15,"column_end":28,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:17:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\instance.rs","byte_start":390,"byte_end":395,"line_start":14,"line_end":14,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\instance.rs","byte_start":390,"byte_end":397,"line_start":14,"line_end":14,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\instance.rs:14:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CreateInstanceRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":103,"byte_end":124,"line_start":4,"line_end":4,"column_start":26,"column_end":47,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":26,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":101,"byte_end":124,"line_start":4,"line_end":4,"column_start":24,"column_end":47,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":24,"highlight_end":47}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":89,"byte_end":90,"line_start":4,"line_end":4,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":124,"byte_end":125,"line_start":4,"line_end":4,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `CreateInstanceRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:4:26\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr::{VultrClient, CreateInstanceRequest},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":134,"byte_end":148,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":130,"byte_end":150,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":250,"byte_end":254,"line_start":9,"line_end":9,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn, error};","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":256,"byte_end":261,"line_start":9,"line_end":9,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn, error};","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":248,"byte_end":261,"line_start":9,"line_end":9,"column_start":31,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn, error};","highlight_start":31,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:9:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, instrument, warn, error};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Duration` and `sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":282,"byte_end":287,"line_start":10,"line_end":10,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":289,"byte_end":297,"line_start":10,"line_end":10,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":264,"byte_end":300,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Duration` and `sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:10:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::{sleep, Duration};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":134,"byte_end":148,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":130,"byte_end":150,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\kubernetes_deployment.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":250,"byte_end":254,"line_start":9,"line_end":9,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn};","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":248,"byte_end":254,"line_start":9,"line_end":9,"column_start":31,"column_end":37,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn};","highlight_start":31,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\kubernetes_deployment.rs:9:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, instrument, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::controllers::ControllerError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\mod.rs","byte_start":261,"byte_end":296,"line_start":16,"line_end":16,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"use crate::controllers::ControllerError;","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\mod.rs","byte_start":257,"byte_end":298,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::controllers::ControllerError;","highlight_start":1,"highlight_end":41},{"text":"use thiserror::Error;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::controllers::ControllerError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\mod.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::controllers::ControllerError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DateTime`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\mod.rs","byte_start":29,"byte_end":37,"line_start":3,"line_end":3,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\utils\\mod.rs","byte_start":29,"byte_end":39,"line_start":3,"line_end":3,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\mod.rs","byte_start":28,"byte_end":29,"line_start":3,"line_end":3,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\mod.rs","byte_start":42,"byte_end":43,"line_start":3,"line_end":3,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `DateTime`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\mod.rs:3:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":134,"byte_end":142,"line_start":3,"line_end":3,"column_start":42,"column_end":50,"is_primary":true,"text":[{"text":"use reqwest::{header::HeaderMap, Client, Response};","highlight_start":42,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":132,"byte_end":142,"line_start":3,"line_end":3,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use reqwest::{header::HeaderMap, Client, Response};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Response`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\vultr\\mod.rs:3:42\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse reqwest::{header::HeaderMap, Client, Response};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":158,"byte_end":169,"line_start":4,"line_end":4,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\vultr\\mod.rs","byte_start":171,"byte_end":180,"line_start":4,"line_end":4,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":146,"byte_end":184,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use std::{collections::HashMap, time::Duration};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\vultr\\mod.rs:4:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":279,"byte_end":300,"line_start":7,"line_end":7,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":275,"byte_end":303,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\vultr\\mod.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::TryStreamExt;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\models.rs","byte_start":13,"byte_end":21,"line_start":1,"line_end":1,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\vultr\\models.rs","byte_start":23,"byte_end":26,"line_start":1,"line_end":1,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\models.rs","byte_start":0,"byte_end":29,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":29},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DateTime` and `Utc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\vultr\\models.rs:1:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":16647,"byte_end":16653,"line_start":441,"line_end":441,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"        let content_bytes = base64::decode(content_b64.replace('\\n', \"\"))","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:441:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m441\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let content_bytes = base64::decode(content_b64.replace('\\n', \"\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":18238,"byte_end":18244,"line_start":480,"line_end":480,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"        let content_bytes = base64::decode(content_b64)","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:480:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m480\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let content_bytes = base64::decode(content_b64)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\auth.rs","byte_start":3678,"byte_end":3683,"line_start":113,"line_end":113,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\auth.rs","byte_start":3678,"byte_end":3683,"line_start":113,"line_end":113,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\auth.rs:113:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":10906,"byte_end":10911,"line_start":325,"line_end":325,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":10906,"byte_end":10911,"line_start":325,"line_end":325,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\intelligent_hosting.rs:325:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":12338,"byte_end":12343,"line_start":368,"line_end":368,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":12338,"byte_end":12343,"line_start":368,"line_end":368,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\intelligent_hosting.rs:368:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m368\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `claims`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":3713,"byte_end":3719,"line_start":117,"line_end":117,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    claims: Claims,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":3713,"byte_end":3719,"line_start":117,"line_end":117,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    claims: Claims,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_claims","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `claims`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:117:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    claims: Claims,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_claims`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `signature`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":658,"byte_end":667,"line_start":23,"line_end":23,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let signature = headers","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":658,"byte_end":667,"line_start":23,"line_end":23,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let signature = headers","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_signature","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `signature`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\webhooks.rs:23:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let signature = headers\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_signature`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":2267,"byte_end":2272,"line_start":68,"line_end":68,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let token = headers","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":2267,"byte_end":2272,"line_start":68,"line_end":68,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let token = headers","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `token`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\webhooks.rs:68:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let token = headers\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":7272,"byte_end":7277,"line_start":201,"line_end":201,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":7272,"byte_end":7277,"line_start":201,"line_end":201,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\webhooks.rs:201:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":506,"byte_end":511,"line_start":23,"line_end":23,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":506,"byte_end":511,"line_start":23,"line_end":23,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\regions.rs:23:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":1261,"byte_end":1266,"line_start":49,"line_end":49,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":1261,"byte_end":1266,"line_start":49,"line_end":49,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\regions.rs:49:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":2004,"byte_end":2009,"line_start":73,"line_end":73,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":2004,"byte_end":2009,"line_start":73,"line_end":73,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\regions.rs:73:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":3063,"byte_end":3068,"line_start":106,"line_end":106,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":3063,"byte_end":3068,"line_start":106,"line_end":106,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\regions.rs:106:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":4416,"byte_end":4421,"line_start":150,"line_end":150,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\regions.rs","byte_start":4416,"byte_end":4421,"line_start":150,"line_end":150,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\regions.rs:150:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<Arc<AppState>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `chunk`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5236,"byte_end":5241,"line_start":168,"line_end":168,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(5) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5236,"byte_end":5241,"line_start":168,"line_end":168,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(5) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":"_chunk","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `chunk`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\rate_limiter.rs:168:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for chunk in requests.chunks(5) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_chunk`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `chunk`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5868,"byte_end":5873,"line_start":184,"line_end":184,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(10) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5868,"byte_end":5873,"line_start":184,"line_end":184,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(10) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":"_chunk","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `chunk`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\rate_limiter.rs:184:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m184\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for chunk in requests.chunks(10) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_chunk`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":1215,"byte_end":1225,"line_start":36,"line_end":36,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"users\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":1215,"byte_end":1225,"line_start":36,"line_end":36,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"users\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `collection`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database_indexes.rs:36:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let collection = self.database.collection::<Document>(\"users\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":2906,"byte_end":2916,"line_start":81,"line_end":81,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"applications\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":2906,"byte_end":2916,"line_start":81,"line_end":81,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"applications\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `collection`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database_indexes.rs:81:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let collection = self.database.collection::<Document>(\"applications\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":5306,"byte_end":5316,"line_start":143,"line_end":143,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"deployments\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":5306,"byte_end":5316,"line_start":143,"line_end":143,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"deployments\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `collection`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database_indexes.rs:143:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let collection = self.database.collection::<Document>(\"deployments\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":7706,"byte_end":7716,"line_start":205,"line_end":205,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"build_jobs\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":7706,"byte_end":7716,"line_start":205,"line_end":205,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"build_jobs\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `collection`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database_indexes.rs:205:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let collection = self.database.collection::<Document>(\"build_jobs\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":10193,"byte_end":10203,"line_start":272,"line_end":272,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"environment_groups\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":10193,"byte_end":10203,"line_start":272,"line_end":272,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"environment_groups\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `collection`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database_indexes.rs:272:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let collection = self.database.collection::<Document>(\"environment_groups\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `index_doc`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":18968,"byte_end":18977,"line_start":494,"line_end":494,"column_start":36,"column_end":45,"is_primary":true,"text":[{"text":"                    while let Some(index_doc) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {","highlight_start":36,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":18968,"byte_end":18977,"line_start":494,"line_end":494,"column_start":36,"column_end":45,"is_primary":true,"text":[{"text":"                    while let Some(index_doc) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {","highlight_start":36,"highlight_end":45}],"label":null,"suggested_replacement":"_index_doc","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `index_doc`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\database_indexes.rs:494:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m494\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    while let Some(index_doc) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_index_doc`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `method`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4254,"byte_end":4260,"line_start":108,"line_end":108,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let method = req.method().clone();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4254,"byte_end":4260,"line_start":108,"line_end":108,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let method = req.method().clone();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_method","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `method`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\observability\\mod.rs:108:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let method = req.method().clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_method`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4293,"byte_end":4297,"line_start":109,"line_end":109,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let path = req.uri().path().to_string();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4293,"byte_end":4297,"line_start":109,"line_end":109,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let path = req.uri().path().to_string();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\observability\\mod.rs:109:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let path = req.uri().path().to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `duration`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4380,"byte_end":4388,"line_start":113,"line_end":113,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let duration = start.elapsed();","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4380,"byte_end":4388,"line_start":113,"line_end":113,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let duration = start.elapsed();","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_duration","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `duration`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\observability\\mod.rs:113:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let duration = start.elapsed();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_duration`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `status`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4416,"byte_end":4422,"line_start":114,"line_end":114,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let status = response.status().as_u16();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4416,"byte_end":4422,"line_start":114,"line_end":114,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let status = response.status().as_u16();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_status","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `status`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\observability\\mod.rs:114:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let status = response.status().as_u16();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_status`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `blueprint_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24746,"byte_end":24758,"line_start":628,"line_end":628,"column_start":43,"column_end":55,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":43,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24746,"byte_end":24758,"line_start":628,"line_end":628,"column_start":43,"column_end":55,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":43,"highlight_end":55}],"label":null,"suggested_replacement":"_blueprint_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `blueprint_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:628:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m628\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_blueprint_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24770,"byte_end":24774,"line_start":628,"line_end":628,"column_start":67,"column_end":71,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":67,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24770,"byte_end":24774,"line_start":628,"line_end":628,"column_start":67,"column_end":71,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":67,"highlight_end":71}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:628:67\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m628\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `blueprint_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":25203,"byte_end":25215,"line_start":641,"line_end":641,"column_start":36,"column_end":48,"is_primary":true,"text":[{"text":"    async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {","highlight_start":36,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":25203,"byte_end":25215,"line_start":641,"line_end":641,"column_start":36,"column_end":48,"is_primary":true,"text":[{"text":"    async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {","highlight_start":36,"highlight_end":48}],"label":null,"suggested_replacement":"_blueprint_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `blueprint_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:641:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m641\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_blueprint_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `metadata`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":29982,"byte_end":29990,"line_start":801,"line_end":801,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"                    let metadata = entry.metadata().await","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":29982,"byte_end":29990,"line_start":801,"line_end":801,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"                    let metadata = entry.metadata().await","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":"_metadata","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `metadata`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:801:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m801\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let metadata = entry.metadata().await\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_metadata`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":9759,"byte_end":9766,"line_start":229,"line_end":229,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        request: TriggerDeploymentRequest,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":9759,"byte_end":9766,"line_start":229,"line_end":229,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        request: TriggerDeploymentRequest,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:229:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        request: TriggerDeploymentRequest,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `update_result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":12292,"byte_end":12305,"line_start":292,"line_end":292,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let update_result = self.deployments","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":12292,"byte_end":12305,"line_start":292,"line_end":292,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let update_result = self.deployments","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"_update_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `update_result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:292:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let update_result = self.deployments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_update_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `deployment_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14958,"byte_end":14971,"line_start":352,"line_end":352,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        deployment_id: ObjectId,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14958,"byte_end":14971,"line_start":352,"line_end":352,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        deployment_id: ObjectId,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_deployment_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `deployment_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:352:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m352\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        deployment_id: ObjectId,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_deployment_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `build_job_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14991,"byte_end":15003,"line_start":353,"line_end":353,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        build_job_id: String,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14991,"byte_end":15003,"line_start":353,"line_end":353,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        build_job_id: String,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_build_job_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `build_job_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:353:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m353\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        build_job_id: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_build_job_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":15025,"byte_end":15038,"line_start":354,"line_end":354,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":15025,"byte_end":15038,"line_start":354,"line_end":354,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"_state_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:354:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m354\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut state_manager: DeploymentStateManager,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":15021,"byte_end":15038,"line_start":354,"line_end":354,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":15021,"byte_end":15025,"line_start":354,"line_end":354,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:354:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m354\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut state_manager: DeploymentStateManager,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `application`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":18777,"byte_end":18788,"line_start":434,"line_end":434,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":18777,"byte_end":18788,"line_start":434,"line_end":434,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"_application","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `application`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:434:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m434\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_application`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `deployment`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":35971,"byte_end":35981,"line_start":873,"line_end":873,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let deployment = self.deployments","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":35971,"byte_end":35981,"line_start":873,"line_end":873,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let deployment = self.deployments","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_deployment","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `deployment`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:873:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m873\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let deployment = self.deployments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_deployment`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":14186,"byte_end":14193,"line_start":396,"line_end":396,"column_start":73,"column_end":80,"is_primary":true,"text":[{"text":"    async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {","highlight_start":73,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\disk.rs","byte_start":14186,"byte_end":14193,"line_start":396,"line_end":396,"column_start":73,"column_end":80,"is_primary":true,"text":[{"text":"    async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {","highlight_start":73,"highlight_end":80}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\disk.rs:396:73\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m396\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mucture_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `application_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9539,"byte_end":9553,"line_start":249,"line_end":249,"column_start":42,"column_end":56,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":42,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9539,"byte_end":9553,"line_start":249,"line_end":249,"column_start":42,"column_end":56,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":42,"highlight_end":56}],"label":null,"suggested_replacement":"_application_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `application_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:249:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mok(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_application_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `repository`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9566,"byte_end":9576,"line_start":249,"line_end":249,"column_start":69,"column_end":79,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":69,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9566,"byte_end":9576,"line_start":249,"line_end":249,"column_start":69,"column_end":79,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":69,"highlight_end":79}],"label":null,"suggested_replacement":"_repository","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `repository`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:249:69\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0md: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_repository`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `webhook_secret`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9591,"byte_end":9605,"line_start":249,"line_end":249,"column_start":94,"column_end":108,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":94,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9591,"byte_end":9605,"line_start":249,"line_end":249,"column_start":94,"column_end":108,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":94,"highlight_end":108}],"label":null,"suggested_replacement":"_webhook_secret","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `webhook_secret`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:249:94\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mpository, webhook_secret: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_webhook_secret`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `application_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9969,"byte_end":9983,"line_start":257,"line_end":257,"column_start":45,"column_end":59,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":45,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9969,"byte_end":9983,"line_start":257,"line_end":257,"column_start":45,"column_end":59,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":45,"highlight_end":59}],"label":null,"suggested_replacement":"_application_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `application_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:257:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mok(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_application_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `repository`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9996,"byte_end":10006,"line_start":257,"line_end":257,"column_start":72,"column_end":82,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":72,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9996,"byte_end":10006,"line_start":257,"line_end":257,"column_start":72,"column_end":82,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":72,"highlight_end":82}],"label":null,"suggested_replacement":"_repository","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `repository`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:257:72\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0md: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_repository`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `webhook_secret`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":10021,"byte_end":10035,"line_start":257,"line_end":257,"column_start":97,"column_end":111,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":97,"highlight_end":111}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":10021,"byte_end":10035,"line_start":257,"line_end":257,"column_start":97,"column_end":111,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":97,"highlight_end":111}],"label":null,"suggested_replacement":"_webhook_secret","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `webhook_secret`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:257:97\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mpository, webhook_secret: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_webhook_secret`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `applications`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":11031,"byte_end":11043,"line_start":278,"line_end":278,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let applications = self.applications","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":11031,"byte_end":11043,"line_start":278,"line_end":278,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let applications = self.applications","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_applications","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `applications`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:278:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let applications = self.applications\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_applications`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":11308,"byte_end":11333,"line_start":286,"line_end":286,"column_start":13,"column_end":38,"is_primary":true,"text":[{"text":"        let mut triggered_deployments = Vec::new();","highlight_start":13,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":11308,"byte_end":11312,"line_start":286,"line_end":286,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut triggered_deployments = Vec::new();","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\git.rs:286:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut triggered_deployments = Vec::new();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `setup_script`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":9334,"byte_end":9346,"line_start":311,"line_end":311,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let setup_script = match pool_type {","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":9334,"byte_end":9346,"line_start":311,"line_end":311,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let setup_script = match pool_type {","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_setup_script","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `setup_script`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:311:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let setup_script = match pool_type {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_setup_script`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47004,"byte_end":47010,"line_start":1328,"line_end":1328,"column_start":53,"column_end":59,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":53,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47004,"byte_end":47010,"line_start":1328,"line_end":1328,"column_start":53,"column_end":59,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":53,"highlight_end":59}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `config`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:1328:53\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `server_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47018,"byte_end":47027,"line_start":1328,"line_end":1328,"column_start":67,"column_end":76,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":67,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47018,"byte_end":47027,"line_start":1328,"line_end":1328,"column_start":67,"column_end":76,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":67,"highlight_end":76}],"label":null,"suggested_replacement":"_server_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `server_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:1328:67\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_server_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `specs`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47240,"byte_end":47245,"line_start":1333,"line_end":1333,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47240,"byte_end":47245,"line_start":1333,"line_end":1333,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":"_specs","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `specs`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:1333:56\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mlf, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_specs`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `region`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47268,"byte_end":47274,"line_start":1333,"line_end":1333,"column_start":84,"column_end":90,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":84,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47268,"byte_end":47274,"line_start":1333,"line_end":1333,"column_start":84,"column_end":90,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":84,"highlight_end":90}],"label":null,"suggested_replacement":"_region","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `region`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:1333:84\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0msourceAllocation, region: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_region`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `specs`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47473,"byte_end":47478,"line_start":1338,"line_end":1338,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47473,"byte_end":47478,"line_start":1338,"line_end":1338,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":"_specs","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `specs`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:1338:61\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mlf, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_specs`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `region`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47501,"byte_end":47507,"line_start":1338,"line_end":1338,"column_start":89,"column_end":95,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":89,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47501,"byte_end":47507,"line_start":1338,"line_end":1338,"column_start":89,"column_end":95,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":89,"highlight_end":95}],"label":null,"suggested_replacement":"_region","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `region`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:1338:89\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0msourceAllocation, region: &str) -> ServiceResult<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_region`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `response`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":9876,"byte_end":9884,"line_start":251,"line_end":251,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let response = self.client.delete(&url).await?;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":9876,"byte_end":9884,"line_start":251,"line_end":251,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let response = self.client.delete(&url).await?;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_response","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `response`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\vultr\\mod.rs:251:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let response = self.client.delete(&url).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_response`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\mod.rs","byte_start":1001,"byte_end":1016,"line_start":43,"line_end":43,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    now.timestamp_nanos().hash(&mut hasher);","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\mod.rs:43:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    now.timestamp_nanos().hash(&mut hasher);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":10754,"byte_end":10775,"line_start":307,"line_end":307,"column_start":13,"column_end":34,"is_primary":true,"text":[{"text":"        use futures::TryStreamExt;","highlight_start":13,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:307:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        use futures::TryStreamExt;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":14269,"byte_end":14290,"line_start":403,"line_end":403,"column_start":13,"column_end":34,"is_primary":true,"text":[{"text":"        use futures::TryStreamExt;","highlight_start":13,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:403:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m403\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        use futures::TryStreamExt;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":433,"byte_end":454,"line_start":14,"line_end":14,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::TryStreamExt;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":687,"byte_end":708,"line_start":19,"line_end":19,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::TryStreamExt;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":843,"byte_end":864,"line_start":21,"line_end":21,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::TryStreamExt;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\domain.rs","byte_start":352,"byte_end":373,"line_start":14,"line_end":14,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `futures::TryStreamExt`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\domain.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::TryStreamExt;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `limiter`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":8248,"byte_end":8255,"line_start":260,"line_end":260,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let limiter = self.limiters","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":8248,"byte_end":8255,"line_start":260,"line_end":260,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let limiter = self.limiters","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":"_limiter","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `limiter`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\rate_limiter.rs:260:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let limiter = self.limiters\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_limiter`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":2942,"byte_end":2948,"line_start":82,"line_end":82,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let handle = builder","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":2942,"byte_end":2948,"line_start":82,"line_end":82,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let handle = builder","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `handle`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\observability\\mod.rs:82:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let handle = builder\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `config` and `circuit_breaker` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":9768,"byte_end":9779,"line_start":280,"line_end":280,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct LogsService<'a> {","highlight_start":12,"highlight_end":23}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":9835,"byte_end":9841,"line_start":282,"line_end":282,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a crate::config::Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":9874,"byte_end":9889,"line_start":283,"line_end":283,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    circuit_breaker: crate::infrastructure::CircuitBreakerService,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`LogsService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `config` and `circuit_breaker` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\controllers\\logs.rs:282:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct LogsService<'a> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m281\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    database: &'a crate::database::Database,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a crate::config::Config,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    circuit_breaker: crate::infrastructure::CircuitBreakerService,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `LogsService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `remaining_capacity` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":1495,"byte_end":1513,"line_start":53,"line_end":53,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl SlidingWindow {","highlight_start":1,"highlight_end":19}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":2284,"byte_end":2302,"line_start":83,"line_end":83,"column_start":8,"column_end":26,"is_primary":true,"text":[{"text":"    fn remaining_capacity(&self) -> usize {","highlight_start":8,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `remaining_capacity` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\rate_limiter.rs:83:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SlidingWindow {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m83\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn remaining_capacity(&self) -> usize {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `config` and `chunk_processor` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":1503,"byte_end":1519,"line_start":56,"line_end":56,"column_start":12,"column_end":28,"is_primary":false,"text":[{"text":"pub struct BlueprintService<'a> {","highlight_start":12,"highlight_end":28}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\blueprint.rs","byte_start":1617,"byte_end":1623,"line_start":59,"line_end":59,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\blueprint.rs","byte_start":1752,"byte_end":1767,"line_start":63,"line_end":63,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    chunk_processor: ChunkProcessor,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `config` and `chunk_processor` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\blueprint.rs:59:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct BlueprintService<'a> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    chunk_processor: ChunkProcessor,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `vultr_client`, `config`, and `chunk_processor` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":991,"byte_end":1003,"line_start":30,"line_end":30,"column_start":12,"column_end":24,"is_primary":false,"text":[{"text":"pub struct BuildService<'a> {","highlight_start":12,"highlight_end":24}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":1137,"byte_end":1149,"line_start":34,"line_end":34,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    vultr_client: &'a VultrClient,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":1172,"byte_end":1178,"line_start":35,"line_end":35,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":1307,"byte_end":1322,"line_start":39,"line_end":39,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    chunk_processor: ChunkProcessor,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`BuildService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `vultr_client`, `config`, and `chunk_processor` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\build.rs:34:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct BuildService<'a> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr_client: &'a VultrClient,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    chunk_processor: ChunkProcessor,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `BuildService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `vultr_client`, `config`, and `chunk_processor` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":996,"byte_end":1013,"line_start":27,"line_end":27,"column_start":12,"column_end":29,"is_primary":false,"text":[{"text":"pub struct DeploymentService<'a> {","highlight_start":12,"highlight_end":29}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":1108,"byte_end":1120,"line_start":30,"line_end":30,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    vultr_client: &'a VultrClient,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":1213,"byte_end":1219,"line_start":33,"line_end":33,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":1348,"byte_end":1363,"line_start":37,"line_end":37,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    chunk_processor: ChunkProcessor,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `vultr_client`, `config`, and `chunk_processor` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\deployment.rs:30:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DeploymentService<'a> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr_client: &'a VultrClient,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    chunk_processor: ChunkProcessor,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `vultr_client`, `config`, and `chunk_processor` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":1409,"byte_end":1420,"line_start":53,"line_end":53,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct DiskService<'a> {","highlight_start":12,"highlight_end":23}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":1506,"byte_end":1518,"line_start":56,"line_end":56,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    vultr_client: &'a VultrClient,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":1541,"byte_end":1547,"line_start":57,"line_end":57,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":1676,"byte_end":1691,"line_start":61,"line_end":61,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    chunk_processor: ChunkProcessor,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `vultr_client`, `config`, and `chunk_processor` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\disk.rs:56:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DiskService<'a> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr_client: &'a VultrClient,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    chunk_processor: ChunkProcessor,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `vultr_client` and `config` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\domain.rs","byte_start":2085,"byte_end":2098,"line_start":74,"line_end":74,"column_start":12,"column_end":25,"is_primary":false,"text":[{"text":"pub struct DomainService<'a> {","highlight_start":12,"highlight_end":25}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\domain.rs","byte_start":2148,"byte_end":2160,"line_start":76,"line_end":76,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    vultr_client: &'a VultrClient,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\domain.rs","byte_start":2183,"byte_end":2189,"line_start":77,"line_end":77,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `vultr_client` and `config` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\domain.rs:76:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DomainService<'a> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    domains: Collection<CustomDomain>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr_client: &'a VultrClient,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\environment.rs","byte_start":1597,"byte_end":1615,"line_start":53,"line_end":53,"column_start":12,"column_end":30,"is_primary":false,"text":[{"text":"pub struct EnvironmentService<'a> {","highlight_start":12,"highlight_end":30}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\environment.rs","byte_start":1752,"byte_end":1758,"line_start":57,"line_end":57,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: &'a Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\environment.rs:57:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct EnvironmentService<'a> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: &'a Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":5239,"byte_end":5264,"line_start":211,"line_end":211,"column_start":12,"column_end":37,"is_primary":false,"text":[{"text":"pub struct IntelligentHostingService {","highlight_start":12,"highlight_end":37}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":5302,"byte_end":5308,"line_start":213,"line_end":213,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`IntelligentHostingService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `config` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\intelligent_hosting.rs:213:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m211\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct IntelligentHostingService {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr_client: VultrClient,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: Config,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `IntelligentHostingService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":1351,"byte_end":1378,"line_start":56,"line_end":56,"column_start":12,"column_end":39,"is_primary":false,"text":[{"text":"pub struct KubernetesDeploymentService {","highlight_start":12,"highlight_end":39}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":1416,"byte_end":1422,"line_start":58,"line_end":58,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`KubernetesDeploymentService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\kubernetes_deployment.rs:58:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct KubernetesDeploymentService {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vultr_client: VultrClient,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `KubernetesDeploymentService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `api_key` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":429,"byte_end":443,"line_start":16,"line_end":16,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct VultrApiClient {","highlight_start":12,"highlight_end":26}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\vultr\\mod.rs","byte_start":477,"byte_end":484,"line_start":18,"line_end":18,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    api_key: String,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`VultrApiClient` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `api_key` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\vultr\\mod.rs:18:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct VultrApiClient {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    client: RetryClient,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    api_key: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `VultrApiClient` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified","code":{"code":"async_fn_in_trait","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":1685,"byte_end":1690,"line_start":68,"line_end":68,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    async fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(async_fn_in_trait)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":1762,"byte_end":1762,"line_start":68,"line_end":68,"column_start":82,"column_end":82,"is_primary":true,"text":[{"text":"    async fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>","highlight_start":82,"highlight_end":82}],"label":null,"suggested_replacement":"impl std::future::Future<Output = ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":1785,"byte_end":1785,"line_start":68,"line_end":68,"column_start":105,"column_end":105,"is_primary":true,"text":[{"text":"    async fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>","highlight_start":105,"highlight_end":105}],"label":null,"suggested_replacement":"> + Send","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":1685,"byte_end":1691,"line_start":68,"line_end":68,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    async fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":1849,"byte_end":1849,"line_start":71,"line_end":71,"column_start":6,"column_end":6,"is_primary":true,"text":[{"text":"    {","highlight_start":6,"highlight_end":6}],"label":null,"suggested_replacement":"async {","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":1921,"byte_end":1921,"line_start":73,"line_end":73,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    }","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"} ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\infrastructure\\circuit_breaker.rs:68:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> Result<T, ServiceError>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(async_fn_in_trait)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    fn call_with_breaker<F, T>(&self, service_name: &str, operation: F) -> \u001b[0m\u001b[0m\u001b[38;5;10mimpl std::future::Future<Output = \u001b[0m\u001b[0mResult<T, ServiceError>\u001b[0m\u001b[0m\u001b[38;5;10m> + Send\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         F: FnOnce() -> Result<T, ServiceError>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    {\u001b[0m\u001b[0m\u001b[38;5;10masync {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         self.circuit_breaker().call(service_name, operation).await\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10m} \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"131 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 131 warnings emitted\u001b[0m\n\n"}
