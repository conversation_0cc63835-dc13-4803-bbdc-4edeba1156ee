fn(axum::extract::State<Arc<AppState>>, {type error}, axum::<PERSON><PERSON><{type error}>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<{type error}>>, ControllerError>> {create_application}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::Json<{type error}>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<{type error}>>, ControllerError>> {create_application}: Handler<_, _>
