OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-gnu)
HOST = Some(x86_64-pc-windows-gnu)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-gnu
CC_x86_64-pc-windows-gnu = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_gnu
CC_x86_64_pc_windows_gnu = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_gnu
CFLAGS_x86_64_pc_windows_gnu = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-gnu
CFLAGS_x86_64-pc-windows-gnu = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/blocksort.c:22:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/blocksort.c:22:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/huffman.c:22:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/huffman.c:22:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/crctable.c:22:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/crctable.c:22:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/randtable.c:22:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/randtable.c:22:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/compress.c:29:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/compress.c:29:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/decompress.c:22:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/decompress.c:22:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
cargo:warning=                 from bzip2-1.0.8/bzlib.c:31:
cargo:warning=bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
cargo:warning=   70 | #define BZ_EXPORT
cargo:warning=      | 
cargo:warning=<command-line>: note: this is the location of the previous definition
cargo:warning=In file included from bzip2-1.0.8/bzlib_private.h:33,
                 from bzip2-1.0.8/bzlib.c:31:
bzip2-1.0.8/bzlib.h:70: warning: "BZ_EXPORT" redefined
   70 | #define BZ_EXPORT
      | 
<command-line>: note: this is the location of the previous definition

exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-gnu
AR_x86_64-pc-windows-gnu = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_gnu
AR_x86_64_pc_windows_gnu = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_gnu
ARFLAGS_x86_64_pc_windows_gnu = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-gnu
ARFLAGS_x86_64-pc-windows-gnu = None
cargo:rustc-link-lib=static=bz2
cargo:rustc-link-search=native=D:\workspace\.rust\achidas\target\debug\build\bzip2-sys-a4e6f161c4eb205d\out\lib
cargo:root=D:\workspace\.rust\achidas\target\debug\build\bzip2-sys-a4e6f161c4eb205d\out
cargo:include=D:\workspace\.rust\achidas\target\debug\build\bzip2-sys-a4e6f161c4eb205d\out\include
