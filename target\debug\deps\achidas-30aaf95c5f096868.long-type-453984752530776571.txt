fn(axum::extract::State<Arc<AppState>>, Claims, axum::<PERSON><PERSON><CreateEnvironmentGroupRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {create_environment_group}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::Json<CreateEnvironmentGroupRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {create_environment_group}: <PERSON>ler<_, _>
