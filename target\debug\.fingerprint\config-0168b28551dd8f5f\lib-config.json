{"rustc": 2830703817519440116, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 6996883392558192706, "path": 7056227968231688386, "deps": [[1213098572879462490, "json5_rs", false, 5554132356533240481], [1965680986145237447, "yaml_rust2", false, 5035955402119708213], [2244620803250265856, "ron", false, 16483359229051538343], [6502365400774175331, "nom", false, 459768326632996306], [6517602928339163454, "path<PERSON><PERSON>", false, 693392661179787640], [9689903380558560274, "serde", false, 11120990563308116688], [11946729385090170470, "async_trait", false, 3254960072444411179], [13475460906694513802, "convert_case", false, 17135647253728530393], [14618892375165583068, "ini", false, 9254784208993373716], [15367738274754116744, "serde_json", false, 8622871967636998780], [15609422047640926750, "toml", false, 16953683940190287260]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-0168b28551dd8f5f\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}