fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<TriggerDeploymentRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<DeploymentResponse>>, ControllerError>> {trigger_deployment}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<TriggerDeploymentRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<DeploymentResponse>>, ControllerError>> {trigger_deployment}: Handler<_, _>
