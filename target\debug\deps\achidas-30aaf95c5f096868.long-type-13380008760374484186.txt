fn(axum::extract::State<Arc<AppState>>, Clai<PERSON>, Query<utils::Pagination>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<ApplicationResponse>>>, ControllerError>> {list_applications}
fn(axum::extract::State<Arc<AppState>>, Claims, Query<utils::Pagination>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<ApplicationResponse>>>, ControllerError>> {list_applications}: Handler<_, _>
