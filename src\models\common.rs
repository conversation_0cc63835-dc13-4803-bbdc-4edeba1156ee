use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
    pub metadata: Option<ResponseMetadata>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            metadata: Some(ResponseMetadata::default()),
        }
    }

    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            error: None,
            metadata: Some(ResponseMetadata::default()),
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            metadata: Some(ResponseMetadata::default()),
        }
    }

    pub fn not_found(message: String) -> Self {
        Self {
            success: false,
            data: None,
            message: Some(message),
            error: Some("Not Found".to_string()),
            metadata: Some(ResponseMetadata::default()),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseMetadata {
    pub timestamp: DateTime<Utc>,
    pub request_id: Option<String>,
    pub version: String,
    pub pagination: Option<PaginationInfo>,
}

impl Default for ResponseMetadata {
    fn default() -> Self {
        Self {
            timestamp: Utc::now(),
            request_id: None,
            version: "1.0".to_string(),
            pagination: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub pagination: PaginationInfo,
    pub total: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub page: u32,
    pub per_page: u32,
    pub total_pages: u32,
    pub total_items: u64,
    pub has_next: bool,
    pub has_prev: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PaginationQuery {
    #[validate(range(min = 1, max = 1000))]
    pub page: Option<u32>,
    
    #[validate(range(min = 1, max = 100))]
    pub per_page: Option<u32>,
    
    pub sort_by: Option<String>,
    pub sort_order: Option<SortOrder>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
            sort_by: None,
            sort_order: Some(SortOrder::Desc),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum SortOrder {
    Asc,
    Desc,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub code: Option<String>,
    pub details: Option<serde_json::Value>,
    pub timestamp: DateTime<Utc>,
    pub request_id: Option<String>,
}

impl ErrorResponse {
    pub fn new(error: String, message: String) -> Self {
        Self {
            error,
            message,
            code: None,
            details: None,
            timestamp: Utc::now(),
            request_id: None,
        }
    }

    pub fn with_code(mut self, code: String) -> Self {
        self.code = Some(code);
        self
    }

    pub fn with_details(mut self, details: serde_json::Value) -> Self {
        self.details = Some(details);
        self
    }

    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheck {
    pub status: HealthStatus,
    pub timestamp: DateTime<Utc>,
    pub version: String,
    pub uptime: u64, // seconds
    pub checks: std::collections::HashMap<String, ComponentHealth>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub status: HealthStatus,
    pub message: Option<String>,
    pub last_check: DateTime<Utc>,
    pub response_time_ms: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IdResponse {
    pub id: String,
}

impl From<ObjectId> for IdResponse {
    fn from(id: ObjectId) -> Self {
        Self {
            id: id.to_hex(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageResponse {
    pub message: String,
}

impl MessageResponse {
    pub fn new(message: String) -> Self {
        Self { message }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusResponse {
    pub status: String,
    pub message: Option<String>,
}

impl StatusResponse {
    pub fn success() -> Self {
        Self {
            status: "success".to_string(),
            message: None,
        }
    }

    pub fn success_with_message(message: String) -> Self {
        Self {
            status: "success".to_string(),
            message: Some(message),
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            status: "error".to_string(),
            message: Some(message),
        }
    }
}

// Utility types for common patterns
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimestampedRecord {
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Default for TimestampedRecord {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            created_at: now,
            updated_at: now,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserOwnedRecord {
    pub user_id: ObjectId,
    pub created_by: ObjectId,
    pub updated_by: Option<ObjectId>,
}

// Resource limits and quotas
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimits {
    pub cpu_cores: Option<f32>,
    pub memory_mb: Option<u32>,
    pub storage_gb: Option<u32>,
    pub bandwidth_gb: Option<u32>,
    pub max_instances: Option<u32>,
    pub max_deployments: Option<u32>,
}

impl Default for ResourceLimits {
    fn default() -> Self {
        Self {
            cpu_cores: Some(1.0),
            memory_mb: Some(512),
            storage_gb: Some(10),
            bandwidth_gb: Some(100),
            max_instances: Some(5),
            max_deployments: Some(10),
        }
    }
}
