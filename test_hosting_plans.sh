#!/bin/bash

# Test script for hosting plans API
BASE_URL="http://localhost:8080/api/v1/public"

echo "🚀 Testing Achidas Hosting Plans API"
echo "======================================"

# Test 1: List all hosting plans
echo "📋 1. Testing: GET /hosting/plans"
curl -s "$BASE_URL/hosting/plans" | jq '.' || echo "❌ Failed to get hosting plans"
echo ""

# Test 2: Get recommended plan for static website
echo "🎯 2. Testing: GET /hosting/plans/recommend?service_type=StaticWebsite&expected_traffic=low"
curl -s "$BASE_URL/hosting/plans/recommend?service_type=StaticWebsite&expected_traffic=low" | jq '.' || echo "❌ Failed to get recommendation"
echo ""

# Test 3: Get specific plan
echo "📦 3. Testing: GET /hosting/plans/starter"
curl -s "$BASE_URL/hosting/plans/starter" | jq '.' || echo "❌ Failed to get starter plan"
echo ""

# Test 4: Filter by hosting tier
echo "🏢 4. Testing: GET /hosting/plans?tier=shared"
curl -s "$BASE_URL/hosting/plans?tier=shared" | jq '.' || echo "❌ Failed to filter by tier"
echo ""

# Test 5: Health check
echo "❤️ 5. Testing: GET /health"
curl -s "$BASE_URL/health" | jq '.' || echo "❌ Failed health check"
echo ""

echo "✅ Testing complete!"
echo ""
echo "💡 To test the full API:"
echo "1. Start the server: cargo run"
echo "2. Run this script: bash test_hosting_plans.sh"
echo "3. Register a user and test authenticated endpoints"
