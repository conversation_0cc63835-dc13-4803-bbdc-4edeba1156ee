{"$message_type":"diagnostic","message":"No transitions provided for a compact representation","code":null,"level":"error","spans":[{"file_name":"src\\infrastructure\\state_machine.rs","byte_start":3345,"byte_end":3354,"line_start":191,"line_end":191,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    Cancelled => {","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\infrastructure\\state_machine.rs:191:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m: No transitions provided for a compact representation\u001b[0m\n"}
{"$message_type":"diagnostic","message":"cannot find value `labels` in this scope","code":{"code":"E0425","explanation":"An unresolved name was used.\n\nErroneous code examples:\n\n```compile_fail,E0425\nsomething_that_doesnt_exist::foo;\n// error: unresolved name `something_that_doesnt_exist::foo`\n\n// or:\n\ntrait Foo {\n    fn bar() {\n        Self; // error: unresolved name `Self`\n    }\n}\n\n// or:\n\nlet x = unknown_variable;  // error: unresolved name `unknown_variable`\n```\n\nPlease verify that the name wasn't misspelled and ensure that the\nidentifier being referred to is valid for the given situation. Example:\n\n```\nenum something_that_does_exist {\n    Foo,\n}\n```\n\nOr:\n\n```\nmod something_that_does_exist {\n    pub static foo : i32 = 0i32;\n}\n\nsomething_that_does_exist::foo; // ok!\n```\n\nOr:\n\n```\nlet unknown_variable = 12u32;\nlet x = unknown_variable; // ok!\n```\n\nIf the item is not defined in the current module, it must be imported using a\n`use` statement, like so:\n\n```\n# mod foo { pub fn bar() {} }\n# fn main() {\nuse foo::bar;\nbar();\n# }\n```\n\nIf the item you are importing is not defined in some super-module of the\ncurrent module, then it must also be declared as public (e.g., `pub fn`).\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\metrics.rs","byte_start":6236,"byte_end":6242,"line_start":145,"line_end":145,"column_start":43,"column_end":49,"is_primary":true,"text":[{"text":"            counter!(\"api_errors_total\", &labels).increment(1);","highlight_start":43,"highlight_end":49}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\infrastructure\\metrics.rs:145:43: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0425]\u001b[0m\u001b[0m: cannot find value `labels` in this scope: not found in this scope\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `validator::Validate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\auth.rs","byte_start":293,"byte_end":312,"line_start":10,"line_end":10,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\auth.rs","byte_start":289,"byte_end":314,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\auth.rs:10:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `validator::Validate`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `AddDatabaseReadReplicaRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrStorageGatewayExportConfig`, `VultrVFSAttachment`, and `models::{\n    }`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\vultr.rs","byte_start":72,"byte_end":87,"line_start":3,"line_end":4,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    models::{","highlight_start":5,"highlight_end":14},{"text":"    },","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2621,"byte_end":2636,"line_start":34,"line_end":34,"column_start":36,"column_end":51,"is_primary":true,"text":[{"text":"        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,","highlight_start":36,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2638,"byte_end":2661,"line_start":34,"line_end":34,"column_start":53,"column_end":76,"is_primary":true,"text":[{"text":"        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,","highlight_start":53,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2671,"byte_end":2689,"line_start":35,"line_end":35,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2691,"byte_end":2717,"line_start":35,"line_end":35,"column_start":29,"column_end":55,"is_primary":true,"text":[{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":29,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2719,"byte_end":2745,"line_start":35,"line_end":35,"column_start":57,"column_end":83,"is_primary":true,"text":[{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":57,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2755,"byte_end":2773,"line_start":36,"line_end":36,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2775,"byte_end":2801,"line_start":36,"line_end":36,"column_start":29,"column_end":55,"is_primary":true,"text":[{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":29,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2803,"byte_end":2825,"line_start":36,"line_end":36,"column_start":57,"column_end":79,"is_primary":true,"text":[{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":57,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2835,"byte_end":2865,"line_start":37,"line_end":37,"column_start":9,"column_end":39,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,","highlight_start":9,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2867,"byte_end":2897,"line_start":37,"line_end":37,"column_start":41,"column_end":71,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,","highlight_start":41,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2907,"byte_end":2935,"line_start":38,"line_end":38,"column_start":9,"column_end":37,"is_primary":true,"text":[{"text":"        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,","highlight_start":9,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2937,"byte_end":2967,"line_start":38,"line_end":38,"column_start":39,"column_end":69,"is_primary":true,"text":[{"text":"        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,","highlight_start":39,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2977,"byte_end":3002,"line_start":39,"line_end":39,"column_start":9,"column_end":34,"is_primary":true,"text":[{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":9,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3004,"byte_end":3032,"line_start":39,"line_end":39,"column_start":36,"column_end":64,"is_primary":true,"text":[{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":36,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3034,"byte_end":3063,"line_start":39,"line_end":39,"column_start":66,"column_end":95,"is_primary":true,"text":[{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":66,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3073,"byte_end":3102,"line_start":40,"line_end":40,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3104,"byte_end":3128,"line_start":40,"line_end":40,"column_start":40,"column_end":64,"is_primary":true,"text":[{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":40,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3130,"byte_end":3153,"line_start":40,"line_end":40,"column_start":66,"column_end":89,"is_primary":true,"text":[{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":66,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3163,"byte_end":3195,"line_start":41,"line_end":41,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3197,"byte_end":3216,"line_start":41,"line_end":41,"column_start":43,"column_end":62,"is_primary":true,"text":[{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":43,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3218,"byte_end":3245,"line_start":41,"line_end":41,"column_start":64,"column_end":91,"is_primary":true,"text":[{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":64,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3255,"byte_end":3290,"line_start":42,"line_end":42,"column_start":9,"column_end":44,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,","highlight_start":9,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3292,"byte_end":3327,"line_start":42,"line_end":42,"column_start":46,"column_end":81,"is_primary":true,"text":[{"text":"        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,","highlight_start":46,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3337,"byte_end":3365,"line_start":43,"line_end":43,"column_start":9,"column_end":37,"is_primary":true,"text":[{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":9,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3367,"byte_end":3388,"line_start":43,"line_end":43,"column_start":39,"column_end":60,"is_primary":true,"text":[{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":39,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":3390,"byte_end":3424,"line_start":43,"line_end":43,"column_start":62,"column_end":96,"is_primary":true,"text":[{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":62,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4316,"byte_end":4349,"line_start":55,"line_end":55,"column_start":9,"column_end":42,"is_primary":true,"text":[{"text":"        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4351,"byte_end":4382,"line_start":55,"line_end":55,"column_start":44,"column_end":75,"is_primary":true,"text":[{"text":"        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,","highlight_start":44,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4495,"byte_end":4513,"line_start":57,"line_end":57,"column_start":27,"column_end":45,"is_primary":true,"text":[{"text":"        UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,","highlight_start":27,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\vultr.rs","byte_start":66,"byte_end":87,"line_start":2,"line_end":4,"column_start":54,"column_end":6,"is_primary":true,"text":[{"text":"    controllers::{success_response, ControllerResult},","highlight_start":54,"highlight_end":55},{"text":"    models::{","highlight_start":1,"highlight_end":14},{"text":"    },","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":2619,"byte_end":3424,"line_start":34,"line_end":43,"column_start":34,"column_end":96,"is_primary":true,"text":[{"text":"        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,","highlight_start":34,"highlight_end":77},{"text":"        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,","highlight_start":1,"highlight_end":84},{"text":"        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,","highlight_start":1,"highlight_end":80},{"text":"        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,","highlight_start":1,"highlight_end":72},{"text":"        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,","highlight_start":1,"highlight_end":70},{"text":"        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,","highlight_start":1,"highlight_end":96},{"text":"        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,","highlight_start":1,"highlight_end":90},{"text":"        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,","highlight_start":1,"highlight_end":92},{"text":"        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,","highlight_start":1,"highlight_end":82},{"text":"        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,","highlight_start":1,"highlight_end":96}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4306,"byte_end":4382,"line_start":54,"line_end":55,"column_start":86,"column_end":75,"is_primary":true,"text":[{"text":"        VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,","highlight_start":86,"highlight_end":87},{"text":"        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,","highlight_start":1,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\vultr.rs","byte_start":4493,"byte_end":4513,"line_start":57,"line_end":57,"column_start":25,"column_end":45,"is_primary":true,"text":[{"text":"        UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,","highlight_start":25,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\vultr.rs:3:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `AddDatabaseReadReplicaRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrStorageGatewayExportConfig`, `VultrVFSAttachment`, and `models::{\u001b[0m\u001b[0m             }`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `environment::EnvironmentService`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\applications.rs","byte_start":255,"byte_end":286,"line_start":7,"line_end":7,"column_start":47,"column_end":78,"is_primary":true,"text":[{"text":"    services::{deployment::DeploymentService, environment::EnvironmentService},","highlight_start":47,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\applications.rs","byte_start":253,"byte_end":286,"line_start":7,"line_end":7,"column_start":45,"column_end":78,"is_primary":true,"text":[{"text":"    services::{deployment::DeploymentService, environment::EnvironmentService},","highlight_start":45,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\applications.rs","byte_start":223,"byte_end":224,"line_start":7,"line_end":7,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    services::{deployment::DeploymentService, environment::EnvironmentService},","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\applications.rs","byte_start":286,"byte_end":287,"line_start":7,"line_end":7,"column_start":78,"column_end":79,"is_primary":true,"text":[{"text":"    services::{deployment::DeploymentService, environment::EnvironmentService},","highlight_start":78,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\applications.rs:7:47: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `environment::EnvironmentService`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `EnvironmentGroup`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\environment.rs","byte_start":150,"byte_end":166,"line_start":4,"line_end":4,"column_start":49,"column_end":65,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":49,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\environment.rs","byte_start":148,"byte_end":166,"line_start":4,"line_end":4,"column_start":47,"column_end":65,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":47,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\environment.rs","byte_start":129,"byte_end":130,"line_start":4,"line_end":4,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\environment.rs","byte_start":166,"byte_end":167,"line_start":4,"line_end":4,"column_start":65,"column_end":66,"is_primary":true,"text":[{"text":"    services::environment::{EnvironmentService, EnvironmentGroup},","highlight_start":65,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\environment.rs:4:49: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `EnvironmentGroup`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":338,"byte_end":342,"line_start":13,"line_end":13,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use futures::stream::{self, Stream};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":338,"byte_end":344,"line_start":13,"line_end":13,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"use futures::stream::{self, Stream};","highlight_start":23,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\logs.rs:13:23: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `self`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":10115,"byte_end":10142,"line_start":292,"line_end":292,"column_start":13,"column_end":40,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":13,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":10111,"byte_end":10143,"line_start":292,"line_end":292,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\logs.rs:292:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `crate::with_circuit_breaker`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":13630,"byte_end":13657,"line_start":388,"line_end":388,"column_start":13,"column_end":40,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":13,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":13626,"byte_end":13658,"line_start":388,"line_end":388,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"        use crate::with_circuit_breaker;","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\logs.rs:388:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `crate::with_circuit_breaker`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `StatusCode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":276,"byte_end":286,"line_start":9,"line_end":9,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":274,"byte_end":286,"line_start":9,"line_end":9,"column_start":21,"column_end":33,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":21,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":264,"byte_end":265,"line_start":9,"line_end":9,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":286,"byte_end":287,"line_start":9,"line_end":9,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:9:23: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `StatusCode`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":363,"byte_end":368,"line_start":14,"line_end":14,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":363,"byte_end":370,"line_start":14,"line_end":14,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:14:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `doc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":8811,"byte_end":8814,"line_start":249,"line_end":249,"column_start":20,"column_end":23,"is_primary":true,"text":[{"text":"        use bson::{doc, oid::ObjectId};","highlight_start":20,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":8811,"byte_end":8816,"line_start":249,"line_end":249,"column_start":20,"column_end":25,"is_primary":true,"text":[{"text":"        use bson::{doc, oid::ObjectId};","highlight_start":20,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":8810,"byte_end":8811,"line_start":249,"line_end":249,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"        use bson::{doc, oid::ObjectId};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":8829,"byte_end":8830,"line_start":249,"line_end":249,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"        use bson::{doc, oid::ObjectId};","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:249:20: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `doc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json::json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\mod.rs","byte_start":370,"byte_end":386,"line_start":20,"line_end":20,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use serde_json::json;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\mod.rs","byte_start":366,"byte_end":388,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::json;","highlight_start":1,"highlight_end":22},{"text":"use thiserror::Error;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\mod.rs:20:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `serde_json::json`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `ServiceResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":55,"byte_end":68,"line_start":2,"line_end":2,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":53,"byte_end":68,"line_start":2,"line_end":2,"column_start":35,"column_end":50,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":35,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":40,"byte_end":41,"line_start":2,"line_end":2,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\circuit_breaker.rs","byte_start":68,"byte_end":69,"line_start":2,"line_end":2,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use crate::services::{ServiceError, ServiceResult};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\circuit_breaker.rs:2:37: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `ServiceResult`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\state_machine.rs","byte_start":94,"byte_end":99,"line_start":4,"line_end":4,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\state_machine.rs","byte_start":92,"byte_end":99,"line_start":4,"line_end":4,"column_start":25,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":25,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\state_machine.rs:4:27: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `clock::DefaultClock` and `state::InMemoryState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":61,"byte_end":80,"line_start":1,"line_end":1,"column_start":62,"column_end":81,"is_primary":true,"text":[{"text":"use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};","highlight_start":62,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":82,"byte_end":102,"line_start":1,"line_end":1,"column_start":83,"column_end":103,"is_primary":true,"text":[{"text":"use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};","highlight_start":83,"highlight_end":103}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":59,"byte_end":102,"line_start":1,"line_end":1,"column_start":60,"column_end":103,"is_primary":true,"text":[{"text":"use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};","highlight_start":60,"highlight_end":103}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\rate_limiter.rs:1:62: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `clock::DefaultClock` and `state::InMemoryState`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\metrics.rs","byte_start":204,"byte_end":209,"line_start":4,"line_end":4,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\metrics.rs","byte_start":202,"byte_end":209,"line_start":4,"line_end":4,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\metrics.rs","byte_start":197,"byte_end":198,"line_start":4,"line_end":4,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\infrastructure\\metrics.rs","byte_start":209,"byte_end":210,"line_start":4,"line_end":4,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{info, error};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\metrics.rs:4:21: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::VecDeque`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":210,"byte_end":236,"line_start":7,"line_end":7,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"use std::collections::VecDeque;","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":206,"byte_end":238,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::VecDeque;","highlight_start":1,"highlight_end":32},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\chunk_processor.rs:7:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `std::collections::VecDeque`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\middleware\\auth.rs","byte_start":272,"byte_end":277,"line_start":14,"line_end":14,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\middleware\\auth.rs","byte_start":272,"byte_end":279,"line_start":14,"line_end":14,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\auth.rs","byte_start":271,"byte_end":272,"line_start":14,"line_end":14,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\auth.rs","byte_start":283,"byte_end":284,"line_start":14,"line_end":14,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{error, warn};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\middleware\\auth.rs:14:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `HeaderValue` and `response::Response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\middleware\\cors.rs","byte_start":31,"byte_end":42,"line_start":2,"line_end":2,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"    http::{header, HeaderValue, Method},","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":57,"byte_end":75,"line_start":3,"line_end":3,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    response::Response,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\middleware\\cors.rs","byte_start":29,"byte_end":42,"line_start":2,"line_end":2,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    http::{header, HeaderValue, Method},","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":51,"byte_end":75,"line_start":2,"line_end":3,"column_start":40,"column_end":23,"is_primary":true,"text":[{"text":"    http::{header, HeaderValue, Method},","highlight_start":40,"highlight_end":41},{"text":"    response::Response,","highlight_start":1,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":10,"byte_end":16,"line_start":1,"line_end":2,"column_start":11,"column_end":5,"is_primary":true,"text":[{"text":"use axum::{","highlight_start":11,"highlight_end":12},{"text":"    http::{header, HeaderValue, Method},","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\cors.rs","byte_start":75,"byte_end":78,"line_start":3,"line_end":4,"column_start":23,"column_end":2,"is_primary":true,"text":[{"text":"    response::Response,","highlight_start":23,"highlight_end":24},{"text":"};","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\middleware\\cors.rs:2:20: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `HeaderValue` and `response::Response`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `HeaderMap` and `extract::Query`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\middleware\\mod.rs","byte_start":123,"byte_end":132,"line_start":7,"line_end":7,"column_start":46,"column_end":55,"is_primary":true,"text":[{"text":"    http::{Request, StatusCode, HeaderValue, HeaderMap},","highlight_start":46,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\middleware\\mod.rs","byte_start":227,"byte_end":241,"line_start":12,"line_end":12,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    extract::Query,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\middleware\\mod.rs","byte_start":121,"byte_end":132,"line_start":7,"line_end":7,"column_start":44,"column_end":55,"is_primary":true,"text":[{"text":"    http::{Request, StatusCode, HeaderValue, HeaderMap},","highlight_start":44,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\middleware\\mod.rs","byte_start":221,"byte_end":241,"line_start":11,"line_end":12,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    Json,","highlight_start":9,"highlight_end":10},{"text":"    extract::Query,","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\middleware\\mod.rs:7:46: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `HeaderMap` and `extract::Query`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `bson::oid::ObjectId`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":4,"byte_end":23,"line_start":1,"line_end":1,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use bson::oid::ObjectId;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":0,"byte_end":25,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bson::oid::ObjectId;","highlight_start":1,"highlight_end":25},{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\models\\mod.rs:1:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `bson::oid::ObjectId`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":38,"byte_end":46,"line_start":2,"line_end":2,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\mod.rs","byte_start":48,"byte_end":51,"line_start":2,"line_end":2,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":25,"byte_end":54,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":29},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\models\\mod.rs:2:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `DateTime` and `Utc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":95,"byte_end":105,"line_start":4,"line_end":4,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":91,"byte_end":107,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"use validator::Validate;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\models\\mod.rs:4:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `uuid::Uuid`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `validator::Validate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\user.rs","byte_start":95,"byte_end":114,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\user.rs","byte_start":91,"byte_end":116,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":1,"highlight_end":25},{"text":"use axum::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\models\\user.rs:4:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `validator::Validate`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `validator::Validate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\instance.rs","byte_start":95,"byte_end":114,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\instance.rs","byte_start":91,"byte_end":116,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use validator::Validate;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\models\\instance.rs:4:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `validator::Validate`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"variant `Backing_up` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\database.rs","byte_start":1158,"byte_end":1168,"line_start":44,"line_end":44,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Backing_up,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_camel_case_types)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src\\models\\database.rs","byte_start":1158,"byte_end":1168,"line_start":44,"line_end":44,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Backing_up,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"BackingUp","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\models\\database.rs:44:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: variant `Backing_up` should have an upper camel case name: help: convert the identifier to upper camel case: `BackingUp`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `Level`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":338,"byte_end":343,"line_start":13,"line_end":13,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":336,"byte_end":343,"line_start":13,"line_end":13,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\observability\\mod.rs","byte_start":331,"byte_end":332,"line_start":13,"line_end":13,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\observability\\mod.rs","byte_start":343,"byte_end":344,"line_start":13,"line_end":13,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{info, Level};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\observability\\mod.rs:13:21: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `Level`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\auth.rs","byte_start":463,"byte_end":468,"line_start":15,"line_end":15,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\auth.rs","byte_start":463,"byte_end":470,"line_start":15,"line_end":15,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\auth.rs:15:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\billing.rs","byte_start":399,"byte_end":407,"line_start":14,"line_end":14,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Datelike, Timelike};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\billing.rs","byte_start":409,"byte_end":412,"line_start":14,"line_end":14,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Datelike, Timelike};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\billing.rs","byte_start":399,"byte_end":414,"line_start":14,"line_end":14,"column_start":14,"column_end":29,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Datelike, Timelike};","highlight_start":14,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\billing.rs:14:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `DateTime` and `Utc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":522,"byte_end":547,"line_start":17,"line_end":17,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":518,"byte_end":549,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use tracing::{error, info, instrument, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\blueprint.rs:17:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `std::collections::HashMap`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":609,"byte_end":629,"line_start":15,"line_end":15,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    with_circuit_breaker,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":603,"byte_end":629,"line_start":14,"line_end":15,"column_start":23,"column_end":25,"is_primary":true,"text":[{"text":"    vultr::VultrClient,","highlight_start":23,"highlight_end":24},{"text":"    with_circuit_breaker,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:15:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `with_circuit_breaker`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":739,"byte_end":764,"line_start":21,"line_end":21,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":735,"byte_end":766,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::path::Path;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:21:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `std::collections::HashMap`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `std::process::Stdio`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":791,"byte_end":810,"line_start":23,"line_end":23,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::process::Stdio;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":787,"byte_end":812,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::process::Stdio;","highlight_start":1,"highlight_end":25},{"text":"use tokio::process::Command;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:23:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `std::process::Stdio`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::process::Command`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":816,"byte_end":839,"line_start":24,"line_end":24,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::process::Command;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":812,"byte_end":841,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::process::Command;","highlight_start":1,"highlight_end":29},{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:24:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `tokio::process::Command`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `AsyncBufReadExt` and `BufReader`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":857,"byte_end":872,"line_start":25,"line_end":25,"column_start":17,"column_end":32,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":17,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":888,"byte_end":897,"line_start":25,"line_end":25,"column_start":48,"column_end":57,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":48,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":857,"byte_end":874,"line_start":25,"line_end":25,"column_start":17,"column_end":34,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":17,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\build.rs","byte_start":886,"byte_end":897,"line_start":25,"line_end":25,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\build.rs","byte_start":856,"byte_end":857,"line_start":25,"line_end":25,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\build.rs","byte_start":897,"byte_end":898,"line_start":25,"line_end":25,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:25:17: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `AsyncBufReadExt` and `BufReader`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":939,"byte_end":943,"line_start":26,"line_end":26,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":937,"byte_end":943,"line_start":26,"line_end":26,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:26:40: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `warn`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":950,"byte_end":960,"line_start":27,"line_end":27,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":946,"byte_end":962,"line_start":27,"line_end":28,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:27:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `uuid::Uuid`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `AutoScalingConfig`, `BlueprintServiceType as ServiceType`, `DeploymentTrigger`, `Environment`, and `with_circuit_breaker`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":371,"byte_end":388,"line_start":10,"line_end":10,"column_start":52,"column_end":69,"is_primary":true,"text":[{"text":"        Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,","highlight_start":52,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":518,"byte_end":529,"line_start":12,"line_end":12,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":567,"byte_end":602,"line_start":12,"line_end":12,"column_start":58,"column_end":93,"is_primary":true,"text":[{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":58,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":612,"byte_end":629,"line_start":13,"line_end":13,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        AutoScalingConfig, PaginationQuery as Pagination","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":779,"byte_end":799,"line_start":17,"line_end":17,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    with_circuit_breaker,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":369,"byte_end":388,"line_start":10,"line_end":10,"column_start":50,"column_end":69,"is_primary":true,"text":[{"text":"        Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,","highlight_start":50,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":508,"byte_end":529,"line_start":11,"line_end":12,"column_start":100,"column_end":20,"is_primary":true,"text":[{"text":"        CreateApplicationRequest, ApplicationResponse, DeploymentResponse, TriggerDeploymentRequest,","highlight_start":100,"highlight_end":101},{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":565,"byte_end":629,"line_start":12,"line_end":13,"column_start":56,"column_end":26,"is_primary":true,"text":[{"text":"        Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,","highlight_start":56,"highlight_end":94},{"text":"        AutoScalingConfig, PaginationQuery as Pagination","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":773,"byte_end":799,"line_start":16,"line_end":17,"column_start":23,"column_end":25,"is_primary":true,"text":[{"text":"    vultr::VultrClient,","highlight_start":23,"highlight_end":24},{"text":"    with_circuit_breaker,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:10:52: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `AutoScalingConfig`, `BlueprintServiceType as ServiceType`, `DeploymentTrigger`, `Environment`, and `with_circuit_breaker`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":909,"byte_end":934,"line_start":23,"line_end":23,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":905,"byte_end":936,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use tracing::{error, info, instrument, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:23:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `std::collections::HashMap`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":950,"byte_end":955,"line_start":24,"line_end":24,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":950,"byte_end":957,"line_start":24,"line_end":24,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:24:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `DiskPagination as Pagination`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":264,"byte_end":292,"line_start":8,"line_end":8,"column_start":46,"column_end":74,"is_primary":true,"text":[{"text":"    models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},","highlight_start":46,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\disk.rs","byte_start":262,"byte_end":292,"line_start":8,"line_end":8,"column_start":44,"column_end":74,"is_primary":true,"text":[{"text":"    models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},","highlight_start":44,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\disk.rs:8:46: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `DiskPagination as Pagination`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":545,"byte_end":550,"line_start":18,"line_end":18,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":570,"byte_end":574,"line_start":18,"line_end":18,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\disk.rs","byte_start":545,"byte_end":552,"line_start":18,"line_end":18,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\disk.rs","byte_start":568,"byte_end":574,"line_start":18,"line_end":18,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\disk.rs:18:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `error` and `warn`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\domain.rs","byte_start":451,"byte_end":456,"line_start":17,"line_end":17,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\domain.rs","byte_start":476,"byte_end":480,"line_start":17,"line_end":17,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\domain.rs","byte_start":451,"byte_end":458,"line_start":17,"line_end":17,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\domain.rs","byte_start":474,"byte_end":480,"line_start":17,"line_end":17,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\domain.rs:17:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `error` and `warn`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `Environment`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\environment.rs","byte_start":83,"byte_end":94,"line_start":4,"line_end":4,"column_start":27,"column_end":38,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":27,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\environment.rs","byte_start":81,"byte_end":94,"line_start":4,"line_end":4,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\environment.rs","byte_start":69,"byte_end":70,"line_start":4,"line_end":4,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\environment.rs","byte_start":94,"byte_end":95,"line_start":4,"line_end":4,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"    models::{Application, Environment},","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\environment.rs:4:27: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `Environment`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\environment.rs","byte_start":456,"byte_end":461,"line_start":18,"line_end":18,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\environment.rs","byte_start":456,"byte_end":463,"line_start":18,"line_end":18,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\environment.rs:18:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `GitCommit` and `GitUser`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":188,"byte_end":197,"line_start":6,"line_end":6,"column_start":53,"column_end":62,"is_primary":true,"text":[{"text":"        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser","highlight_start":53,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\git.rs","byte_start":199,"byte_end":206,"line_start":6,"line_end":6,"column_start":64,"column_end":71,"is_primary":true,"text":[{"text":"        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser","highlight_start":64,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":186,"byte_end":206,"line_start":6,"line_end":6,"column_start":51,"column_end":71,"is_primary":true,"text":[{"text":"        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser","highlight_start":51,"highlight_end":71}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:6:53: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `GitCommit` and `GitUser`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":266,"byte_end":280,"line_start":10,"line_end":10,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":262,"byte_end":282,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use bson::{doc, oid::ObjectId};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:10:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `anyhow::Result`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `chrono::Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":318,"byte_end":329,"line_start":12,"line_end":12,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use chrono::Utc;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":314,"byte_end":331,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::Utc;","highlight_start":1,"highlight_end":17},{"text":"use mongodb::Collection;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:12:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `chrono::Utc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":418,"byte_end":443,"line_start":16,"line_end":16,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":414,"byte_end":445,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use tracing::{error, info, instrument, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:16:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `std::collections::HashMap`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":459,"byte_end":464,"line_start":17,"line_end":17,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\git.rs","byte_start":466,"byte_end":470,"line_start":17,"line_end":17,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":459,"byte_end":472,"line_start":17,"line_end":17,"column_start":15,"column_end":28,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument, warn};","highlight_start":15,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:17:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `error` and `info`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\instance.rs","byte_start":390,"byte_end":395,"line_start":14,"line_end":14,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\instance.rs","byte_start":390,"byte_end":397,"line_start":14,"line_end":14,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\instance.rs:14:15: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `error`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `CreateInstanceRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":103,"byte_end":124,"line_start":4,"line_end":4,"column_start":26,"column_end":47,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":26,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":101,"byte_end":124,"line_start":4,"line_end":4,"column_start":24,"column_end":47,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":24,"highlight_end":47}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":89,"byte_end":90,"line_start":4,"line_end":4,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":124,"byte_end":125,"line_start":4,"line_end":4,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"    vultr::{VultrClient, CreateInstanceRequest},","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:4:26: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `CreateInstanceRequest`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":134,"byte_end":148,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":130,"byte_end":150,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:6:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `anyhow::Result`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":250,"byte_end":254,"line_start":9,"line_end":9,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn, error};","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":256,"byte_end":261,"line_start":9,"line_end":9,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn, error};","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":248,"byte_end":261,"line_start":9,"line_end":9,"column_start":31,"column_end":44,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn, error};","highlight_start":31,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:9:33: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `error` and `warn`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `Duration` and `sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":282,"byte_end":287,"line_start":10,"line_end":10,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":289,"byte_end":297,"line_start":10,"line_end":10,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":264,"byte_end":300,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:10:19: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `Duration` and `sleep`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":134,"byte_end":148,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":130,"byte_end":150,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\kubernetes_deployment.rs:6:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `anyhow::Result`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":250,"byte_end":254,"line_start":9,"line_end":9,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn};","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services\\kubernetes_deployment.rs","byte_start":248,"byte_end":254,"line_start":9,"line_end":9,"column_start":31,"column_end":37,"is_primary":true,"text":[{"text":"use tracing::{info, instrument, warn};","highlight_start":31,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\kubernetes_deployment.rs:9:33: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `warn`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::controllers::ControllerError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\mod.rs","byte_start":261,"byte_end":296,"line_start":16,"line_end":16,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"use crate::controllers::ControllerError;","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\services\\mod.rs","byte_start":257,"byte_end":298,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::controllers::ControllerError;","highlight_start":1,"highlight_end":41},{"text":"use thiserror::Error;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\mod.rs:16:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `crate::controllers::ControllerError`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `DateTime`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\mod.rs","byte_start":29,"byte_end":37,"line_start":3,"line_end":3,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\utils\\mod.rs","byte_start":29,"byte_end":39,"line_start":3,"line_end":3,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\mod.rs","byte_start":28,"byte_end":29,"line_start":3,"line_end":3,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\mod.rs","byte_start":42,"byte_end":43,"line_start":3,"line_end":3,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\utils\\mod.rs:3:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `DateTime`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `Response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":134,"byte_end":142,"line_start":3,"line_end":3,"column_start":42,"column_end":50,"is_primary":true,"text":[{"text":"use reqwest::{header::HeaderMap, Client, Response};","highlight_start":42,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":132,"byte_end":142,"line_start":3,"line_end":3,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use reqwest::{header::HeaderMap, Client, Response};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\vultr\\mod.rs:3:42: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `Response`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":158,"byte_end":169,"line_start":4,"line_end":4,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\vultr\\mod.rs","byte_start":171,"byte_end":180,"line_start":4,"line_end":4,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":146,"byte_end":184,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use std::{collections::HashMap, time::Duration};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\vultr\\mod.rs:4:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `futures::TryStreamExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":279,"byte_end":300,"line_start":7,"line_end":7,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":275,"byte_end":303,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use futures::TryStreamExt;","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\vultr\\mod.rs:7:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `futures::TryStreamExt`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\models.rs","byte_start":13,"byte_end":21,"line_start":1,"line_end":1,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\vultr\\models.rs","byte_start":23,"byte_end":26,"line_start":1,"line_end":1,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\models.rs","byte_start":0,"byte_end":29,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":29},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\vultr\\models.rs:1:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `DateTime` and `Utc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":16647,"byte_end":16653,"line_start":441,"line_end":441,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"        let content_bytes = base64::decode(content_b64.replace('\\n', \"\"))","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\blueprint.rs:441:37: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":18238,"byte_end":18244,"line_start":480,"line_end":480,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"        let content_bytes = base64::decode(content_b64)","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\services\\blueprint.rs:480:37: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\auth.rs","byte_start":3702,"byte_end":3707,"line_start":113,"line_end":113,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\auth.rs","byte_start":3702,"byte_end":3707,"line_start":113,"line_end":113,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\auth.rs:113:11: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state`: help: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":10906,"byte_end":10911,"line_start":325,"line_end":325,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":10906,"byte_end":10911,"line_start":325,"line_end":325,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\intelligent_hosting.rs:325:11: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state`: help: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":12338,"byte_end":12343,"line_start":368,"line_end":368,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\intelligent_hosting.rs","byte_start":12338,"byte_end":12343,"line_start":368,"line_end":368,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\intelligent_hosting.rs:368:11: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state`: help: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\controllers\\applications.rs","byte_start":3138,"byte_end":3148,"line_start":94,"line_end":94,"column_start":41,"column_end":51,"is_primary":true,"text":[{"text":"        .list_applications(&claims.sub, pagination)","highlight_start":41,"highlight_end":51}],"label":"expected `PaginationQuery`, found `Pagination`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\applications.rs","byte_start":3107,"byte_end":3124,"line_start":94,"line_end":94,"column_start":10,"column_end":27,"is_primary":false,"text":[{"text":"        .list_applications(&claims.sub, pagination)","highlight_start":10,"highlight_end":27}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":28776,"byte_end":28798,"line_start":685,"line_end":685,"column_start":9,"column_end":31,"is_primary":false,"text":[{"text":"        pagination: Pagination,","highlight_start":9,"highlight_end":31}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":28711,"byte_end":28728,"line_start":682,"line_end":682,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"    pub async fn list_applications(","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\applications.rs:94:41: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `PaginationQuery`, found `Pagination`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\controllers\\applications.rs","byte_start":7869,"byte_end":7879,"line_start":226,"line_end":226,"column_start":49,"column_end":59,"is_primary":true,"text":[{"text":"        .list_deployments(&claims.sub, &app_id, pagination)","highlight_start":49,"highlight_end":59}],"label":"expected `PaginationQuery`, found `Pagination`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\applications.rs","byte_start":7830,"byte_end":7846,"line_start":226,"line_end":226,"column_start":10,"column_end":26,"is_primary":false,"text":[{"text":"        .list_deployments(&claims.sub, &app_id, pagination)","highlight_start":10,"highlight_end":26}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":32192,"byte_end":32214,"line_start":776,"line_end":776,"column_start":9,"column_end":31,"is_primary":false,"text":[{"text":"        pagination: Pagination,","highlight_start":9,"highlight_end":31}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":32106,"byte_end":32122,"line_start":772,"line_end":772,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"    pub async fn list_deployments(","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\applications.rs:226:49: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `PaginationQuery`, found `Pagination`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\controllers\\environment.rs","byte_start":4726,"byte_end":4739,"line_start":137,"line_end":137,"column_start":47,"column_end":60,"is_primary":true,"text":[{"text":"        .map_err(|e| ControllerError::Service(e.to_string()))?;","highlight_start":47,"highlight_end":60}],"label":"expected `ServiceError`, found `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\environment.rs","byte_start":4701,"byte_end":4725,"line_start":137,"line_end":137,"column_start":22,"column_end":46,"is_primary":false,"text":[{"text":"        .map_err(|e| ControllerError::Service(e.to_string()))?;","highlight_start":22,"highlight_end":46}],"label":"arguments to this enum variant are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"tuple variant defined here","code":null,"level":"note","spans":[{"file_name":"src\\controllers\\mod.rs","byte_start":1073,"byte_end":1080,"line_start":51,"line_end":51,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    Service(services::ServiceError),","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"try removing the method call","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\environment.rs","byte_start":4727,"byte_end":4739,"line_start":137,"line_end":137,"column_start":48,"column_end":60,"is_primary":true,"text":[{"text":"        .map_err(|e| ControllerError::Service(e.to_string()))?;","highlight_start":48,"highlight_end":60}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\environment.rs:137:47: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `ServiceError`, found `String`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `claims`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":3719,"byte_end":3725,"line_start":117,"line_end":117,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    claims: Claims,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":3719,"byte_end":3725,"line_start":117,"line_end":117,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    claims: Claims,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_claims","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\logs.rs:117:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `claims`: help: if this is intentional, prefix it with an underscore: `_claims`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"`state` does not live long enough","code":{"code":"E0597","explanation":"This error occurs because a value was dropped while it was still borrowed.\n\nErroneous code example:\n\n```compile_fail,E0597\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n{\n    let y = 0;\n    x.x = Some(&y); // error: `y` does not live long enough\n}\nprintln!(\"{:?}\", x.x);\n```\n\nHere, `y` is dropped at the end of the inner scope, but it is borrowed by\n`x` until the `println`. To fix the previous example, just remove the scope\nso that `y` isn't dropped until after the println\n\n```\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n\nlet y = 0;\nx.x = Some(&y);\n\nprintln!(\"{:?}\", x.x);\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":5360,"byte_end":5365,"line_start":161,"line_end":161,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"    let logs_service = LogsService::new(&state.database, &state.config);","highlight_start":42,"highlight_end":47}],"label":"borrowed value does not live long enough","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":4649,"byte_end":4650,"line_start":143,"line_end":143,"column_start":42,"column_end":43,"is_primary":false,"text":[{"text":"#[instrument(skip(state, claims, app_id))]","highlight_start":42,"highlight_end":43}],"label":"`state` dropped here while still borrowed","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\controllers\\logs.rs","byte_start":4608,"byte_end":4650,"line_start":143,"line_end":143,"column_start":1,"column_end":43,"is_primary":false,"text":[{"text":"#[instrument(skip(state, claims, app_id))]","highlight_start":1,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[instrument]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-attributes-0.1.29\\src\\lib.rs","byte_start":18566,"byte_end":18683,"line_start":566,"line_end":569,"column_start":1,"column_end":29,"is_primary":false,"text":[{"text":"pub fn instrument(","highlight_start":1,"highlight_end":19},{"text":"    args: proc_macro::TokenStream,","highlight_start":1,"highlight_end":35},{"text":"    item: proc_macro::TokenStream,","highlight_start":1,"highlight_end":35},{"text":") -> proc_macro::TokenStream {","highlight_start":1,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\controllers\\logs.rs","byte_start":4699,"byte_end":4704,"line_start":145,"line_end":145,"column_start":11,"column_end":16,"is_primary":false,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":"binding `state` declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":5942,"byte_end":5958,"line_start":175,"line_end":175,"column_start":8,"column_end":24,"is_primary":false,"text":[{"text":"    Ok(Sse::new(stream).keep_alive(KeepAlive::default()))","highlight_start":8,"highlight_end":24}],"label":"argument requires that `state` is borrowed for `'static`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\controllers\\logs.rs:161:42: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0597]\u001b[0m\u001b[0m: `state` does not live long enough: borrowed value does not live long enough\u001b[0m\n"}
{"$message_type":"diagnostic","message":"`state` does not live long enough","code":{"code":"E0597","explanation":"This error occurs because a value was dropped while it was still borrowed.\n\nErroneous code example:\n\n```compile_fail,E0597\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n{\n    let y = 0;\n    x.x = Some(&y); // error: `y` does not live long enough\n}\nprintln!(\"{:?}\", x.x);\n```\n\nHere, `y` is dropped at the end of the inner scope, but it is borrowed by\n`x` until the `println`. To fix the previous example, just remove the scope\nso that `y` isn't dropped until after the println\n\n```\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n\nlet y = 0;\nx.x = Some(&y);\n\nprintln!(\"{:?}\", x.x);\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":5377,"byte_end":5382,"line_start":161,"line_end":161,"column_start":59,"column_end":64,"is_primary":true,"text":[{"text":"    let logs_service = LogsService::new(&state.database, &state.config);","highlight_start":59,"highlight_end":64}],"label":"borrowed value does not live long enough","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":4649,"byte_end":4650,"line_start":143,"line_end":143,"column_start":42,"column_end":43,"is_primary":false,"text":[{"text":"#[instrument(skip(state, claims, app_id))]","highlight_start":42,"highlight_end":43}],"label":"`state` dropped here while still borrowed","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\controllers\\logs.rs","byte_start":4608,"byte_end":4650,"line_start":143,"line_end":143,"column_start":1,"column_end":43,"is_primary":false,"text":[{"text":"#[instrument(skip(state, claims, app_id))]","highlight_start":1,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[instrument]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-attributes-0.1.29\\src\\lib.rs","byte_start":18566,"byte_end":18683,"line_start":566,"line_end":569,"column_start":1,"column_end":29,"is_primary":false,"text":[{"text":"pub fn instrument(","highlight_start":1,"highlight_end":19},{"text":"    args: proc_macro::TokenStream,","highlight_start":1,"highlight_end":35},{"text":"    item: proc_macro::TokenStream,","highlight_start":1,"highlight_end":35},{"text":") -> proc_macro::TokenStream {","highlight_start":1,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\controllers\\logs.rs","byte_start":4699,"byte_end":4704,"line_start":145,"line_end":145,"column_start":11,"column_end":16,"is_primary":false,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":"binding `state` declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":5942,"byte_end":5958,"line_start":175,"line_end":175,"column_start":8,"column_end":24,"is_primary":false,"text":[{"text":"    Ok(Sse::new(stream).keep_alive(KeepAlive::default()))","highlight_start":8,"highlight_end":24}],"label":"argument requires that `state` is borrowed for `'static`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\controllers\\logs.rs:161:59: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0597]\u001b[0m\u001b[0m: `state` does not live long enough: borrowed value does not live long enough\u001b[0m\n"}
{"$message_type":"diagnostic","message":"hidden type for `impl futures::Stream<Item = Result<axum::response::sse::Event, Infallible>>` captures lifetime that does not appear in bounds","code":{"code":"E0700","explanation":"The `impl Trait` return type captures lifetime parameters that do not\nappear within the `impl Trait` itself.\n\nErroneous code example:\n\n```compile_fail,E0700\nuse std::cell::Cell;\n\ntrait Trait<'a> { }\n\nimpl<'a, 'b> Trait<'b> for Cell<&'a u32> { }\n\nfn foo<'x, 'y>(x: Cell<&'x u32>) -> impl Trait<'y>\nwhere 'x: 'y\n{\n    x\n}\n```\n\nHere, the function `foo` returns a value of type `Cell<&'x u32>`,\nwhich references the lifetime `'x`. However, the return type is\ndeclared as `impl Trait<'y>` -- this indicates that `foo` returns\n\"some type that implements `Trait<'y>`\", but it also indicates that\nthe return type **only captures data referencing the lifetime `'y`**.\nIn this case, though, we are referencing data with lifetime `'x`, so\nthis function is in error.\n\nTo fix this, you must reference the lifetime `'x` from the return\ntype. For example, changing the return type to `impl Trait<'y> + 'x`\nwould work:\n\n```\nuse std::cell::Cell;\n\ntrait Trait<'a> { }\n\nimpl<'a,'b> Trait<'b> for Cell<&'a u32> { }\n\nfn foo<'x, 'y>(x: Cell<&'x u32>) -> impl Trait<'y> + 'x\nwhere 'x: 'y\n{\n    x\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":8330,"byte_end":8375,"line_start":242,"line_end":242,"column_start":6,"column_end":51,"is_primary":false,"text":[{"text":") -> impl Stream<Item = Result<Event, Infallible>> {","highlight_start":6,"highlight_end":51}],"label":"opaque type defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\controllers\\logs.rs","byte_start":8330,"byte_end":8375,"line_start":242,"line_end":242,"column_start":6,"column_end":51,"is_primary":false,"text":[{"text":") -> impl Stream<Item = Result<Event, Infallible>> {","highlight_start":6,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `impl Trait`","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\controllers\\logs.rs","byte_start":8311,"byte_end":8323,"line_start":241,"line_end":241,"column_start":20,"column_end":32,"is_primary":false,"text":[{"text":"    build_service: BuildService,","highlight_start":20,"highlight_end":32}],"label":"hidden type `tokio_stream::adapters::Then<IntervalStream, {async block@src\\controllers\\logs.rs:250:9: 250:19}, {closure@src\\controllers\\logs.rs:246:17: 246:25}>` captures the anonymous lifetime defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\logs.rs","byte_start":8521,"byte_end":9096,"line_start":246,"line_end":262,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    stream.then(move |_| {","highlight_start":5,"highlight_end":27},{"text":"        let job_id = job_id.clone();","highlight_start":1,"highlight_end":37},{"text":"        let build_service = build_service.clone();","highlight_start":1,"highlight_end":51},{"text":"        ","highlight_start":1,"highlight_end":9},{"text":"        async move {","highlight_start":1,"highlight_end":21},{"text":"            match build_service.get_latest_build_logs(&job_id).await {","highlight_start":1,"highlight_end":71},{"text":"                Ok(logs) => {","highlight_start":1,"highlight_end":30},{"text":"                    let data = serde_json::to_string(&logs).unwrap_or_default();","highlight_start":1,"highlight_end":81},{"text":"                    Ok(Event::default().data(data))","highlight_start":1,"highlight_end":52},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                Err(_) => {","highlight_start":1,"highlight_end":28},{"text":"                    // Send heartbeat on error","highlight_start":1,"highlight_end":47},{"text":"                    Ok(Event::default().data(\"heartbeat\"))","highlight_start":1,"highlight_end":59},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    })","highlight_start":1,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"add a `use<...>` bound to explicitly capture `'_`","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\logs.rs","byte_start":8375,"byte_end":8375,"line_start":242,"line_end":242,"column_start":51,"column_end":51,"is_primary":true,"text":[{"text":") -> impl Stream<Item = Result<Event, Infallible>> {","highlight_start":51,"highlight_end":51}],"label":null,"suggested_replacement":" + use<'_>","suggestion_applicability":"MachineApplicable","expansion":{"span":{"file_name":"src\\controllers\\logs.rs","byte_start":8330,"byte_end":8375,"line_start":242,"line_end":242,"column_start":6,"column_end":51,"is_primary":false,"text":[{"text":") -> impl Stream<Item = Result<Event, Infallible>> {","highlight_start":6,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `impl Trait`","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\logs.rs:246:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0700]\u001b[0m\u001b[0m: hidden type for `impl futures::Stream<Item = Result<axum::response::sse::Event, Infallible>>` captures lifetime that does not appear in bounds\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `signature`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":658,"byte_end":667,"line_start":23,"line_end":23,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let signature = headers","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":658,"byte_end":667,"line_start":23,"line_end":23,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let signature = headers","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_signature","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:23:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `signature`: help: if this is intentional, prefix it with an underscore: `_signature`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":2277,"byte_end":2282,"line_start":68,"line_end":68,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let token = headers","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":2277,"byte_end":2282,"line_start":68,"line_end":68,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let token = headers","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:68:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `token`: help: if this is intentional, prefix it with an underscore: `_token`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"this method takes 2 arguments but 1 argument was supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":4703,"byte_end":4712,"line_start":133,"line_end":133,"column_start":31,"column_end":40,"is_primary":false,"text":[{"text":"        .get_application_by_id(&app_id)","highlight_start":31,"highlight_end":40}],"label":"argument #2 of type `&str` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":4682,"byte_end":4703,"line_start":133,"line_end":133,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"        .get_application_by_id(&app_id)","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":8713,"byte_end":8726,"line_start":248,"line_end":248,"column_start":61,"column_end":74,"is_primary":false,"text":[{"text":"    pub async fn get_application_by_id(&self, app_id: &str, user_id: &str) -> crate::services::ServiceResult<crate::models::Application> {","highlight_start":61,"highlight_end":74}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":8670,"byte_end":8691,"line_start":248,"line_end":248,"column_start":18,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn get_application_by_id(&self, app_id: &str, user_id: &str) -> crate::services::ServiceResult<crate::models::Application> {","highlight_start":18,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":4703,"byte_end":4712,"line_start":133,"line_end":133,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"        .get_application_by_id(&app_id)","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":"(&app_id, /* &str */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:133:10: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m: this method takes 2 arguments but 1 argument was supplied\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":7309,"byte_end":7314,"line_start":201,"line_end":201,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":7309,"byte_end":7314,"line_start":201,"line_end":201,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<Arc<AppState>>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:201:11: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state`: help: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no field `database` on type `&DeploymentService<'_>`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":9136,"byte_end":9144,"line_start":255,"line_end":255,"column_start":63,"column_end":71,"is_primary":true,"text":[{"text":"        let deployment_service = DeploymentService::new(&self.database, &self.vultr_client, &self.config);","highlight_start":63,"highlight_end":71}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:255:63: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m: no field `database` on type `&DeploymentService<'_>`: unknown field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"field `vultr_client` of struct `DeploymentService` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":9152,"byte_end":9164,"line_start":255,"line_end":255,"column_start":79,"column_end":91,"is_primary":true,"text":[{"text":"        let deployment_service = DeploymentService::new(&self.database, &self.vultr_client, &self.config);","highlight_start":79,"highlight_end":91}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:255:79: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m: field `vultr_client` of struct `DeploymentService` is private: private field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"field `config` of struct `DeploymentService` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":9172,"byte_end":9178,"line_start":255,"line_end":255,"column_start":99,"column_end":105,"is_primary":true,"text":[{"text":"        let deployment_service = DeploymentService::new(&self.database, &self.vultr_client, &self.config);","highlight_start":99,"highlight_end":105}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:255:99: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m: field `config` of struct `DeploymentService` is private: private field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":9362,"byte_end":9373,"line_start":258,"line_end":258,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"        Ok(application)","highlight_start":12,"highlight_end":23}],"label":"expected `Application`, found `ApplicationResponse`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":9359,"byte_end":9361,"line_start":258,"line_end":258,"column_start":9,"column_end":11,"is_primary":false,"text":[{"text":"        Ok(application)","highlight_start":9,"highlight_end":11}],"label":"arguments to this enum variant are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the type constructed contains `ApplicationResponse` due to the type of the argument passed","code":null,"level":"help","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":9362,"byte_end":9373,"line_start":258,"line_end":258,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"        Ok(application)","highlight_start":12,"highlight_end":23}],"label":"this argument influences the type of `Ok`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\controllers\\webhooks.rs","byte_start":9359,"byte_end":9374,"line_start":258,"line_end":258,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        Ok(application)","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"tuple variant defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-gnu\\lib/rustlib/src/rust\\library\\core\\src\\result.rs","byte_start":19729,"byte_end":19731,"line_start":532,"line_end":532,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    Ok(#[stable(feature = \"rust1\", since = \"1.0.0\")] T),","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:258:12: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `Application`, found `ApplicationResponse`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"field `deployments` of struct `DeploymentService` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\controllers\\webhooks.rs","byte_start":10319,"byte_end":10330,"line_start":284,"line_end":284,"column_start":27,"column_end":38,"is_primary":true,"text":[{"text":"        let result = self.deployments","highlight_start":27,"highlight_end":38}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\controllers\\webhooks.rs:284:27: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m: field `deployments` of struct `DeploymentService` is private: private field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `chunk`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5236,"byte_end":5241,"line_start":168,"line_end":168,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(5) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5236,"byte_end":5241,"line_start":168,"line_end":168,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(5) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":"_chunk","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\rate_limiter.rs:168:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `chunk`: help: if this is intentional, prefix it with an underscore: `_chunk`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `chunk`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5868,"byte_end":5873,"line_start":184,"line_end":184,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(10) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":5868,"byte_end":5873,"line_start":184,"line_end":184,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        for chunk in requests.chunks(10) {","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":"_chunk","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\rate_limiter.rs:184:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `chunk`: help: if this is intentional, prefix it with an underscore: `_chunk`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\rate_limiter.rs","byte_start":18017,"byte_end":18084,"line_start":514,"line_end":516,"column_start":36,"column_end":6,"is_primary":true,"text":[{"text":"    if let Some(user_id) = user_id {","highlight_start":36,"highlight_end":37},{"text":"        rate_limiter.check_api_rate_limit(&user_id).await?;","highlight_start":1,"highlight_end":60},{"text":"    } else {","highlight_start":1,"highlight_end":6}],"label":"expected `Result<(), ServiceError>`, found `()`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"  expected enum `Result<(), services::ServiceError>`\nfound unit type `()`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\rate_limiter.rs:514:36: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `Result<(), ServiceError>`, found `()`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `items`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":4811,"byte_end":4822,"line_start":146,"line_end":146,"column_start":36,"column_end":47,"is_primary":false,"text":[{"text":"        for (index, item) in items.into_iter().enumerate() {","highlight_start":36,"highlight_end":47}],"label":"`items` moved due to this method call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":5621,"byte_end":5626,"line_start":169,"line_end":169,"column_start":67,"column_end":72,"is_primary":true,"text":[{"text":"        warn!(\"Sent {} items to dead letter queue for queue: {}\", items.len(), queue_name);","highlight_start":67,"highlight_end":72}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":4744,"byte_end":4749,"line_start":145,"line_end":145,"column_start":79,"column_end":84,"is_primary":false,"text":[{"text":"    async fn send_to_dead_letter_queue<T: Serialize>(&self, queue_name: &str, items: Vec<T>, error: String) {","highlight_start":79,"highlight_end":84}],"label":"move occurs because `items` has type `Vec<T>`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`into_iter` takes ownership of the receiver `self`, which moves `items`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-gnu\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\collect.rs","byte_start":9787,"byte_end":9791,"line_start":313,"line_end":313,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    fn into_iter(self) -> Self::IntoIter;","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you could `clone` the value and consume it, if the `T: Clone` trait bound could be satisfied","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\chunk_processor.rs","byte_start":4810,"byte_end":4810,"line_start":146,"line_end":146,"column_start":35,"column_end":35,"is_primary":true,"text":[{"text":"        for (index, item) in items.into_iter().enumerate() {","highlight_start":35,"highlight_end":35}],"label":null,"suggested_replacement":".clone()","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\chunk_processor.rs:169:67: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m: borrow of moved value: `items`: value borrowed here after move\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":696,"byte_end":729,"line_start":23,"line_end":23,"column_start":13,"column_end":46,"is_primary":true,"text":[{"text":"            self.create_application_indexes(),","highlight_start":13,"highlight_end":46}],"label":"expected future, found a different future","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider `await`ing on both `Future`s","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"distinct uses of `impl Trait` result in different opaque types","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:23:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected future, found a different future\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":1810,"byte_end":1820,"line_start":54,"line_end":54,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"users\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":1810,"byte_end":1820,"line_start":54,"line_end":54,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"users\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:54:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `collection`: help: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":3501,"byte_end":3511,"line_start":99,"line_end":99,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"applications\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":3501,"byte_end":3511,"line_start":99,"line_end":99,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"applications\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:99:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `collection`: help: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":5901,"byte_end":5911,"line_start":161,"line_end":161,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"deployments\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":5901,"byte_end":5911,"line_start":161,"line_end":161,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"deployments\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:161:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `collection`: help: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":8301,"byte_end":8311,"line_start":223,"line_end":223,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"build_jobs\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":8301,"byte_end":8311,"line_start":223,"line_end":223,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"build_jobs\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:223:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `collection`: help: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `collection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":10788,"byte_end":10798,"line_start":290,"line_end":290,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"environment_groups\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":10788,"byte_end":10798,"line_start":290,"line_end":290,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let collection = self.database.collection::<Document>(\"environment_groups\");","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_collection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:290:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `collection`: help: if this is intentional, prefix it with an underscore: `_collection`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"temporary value dropped while borrowed","code":{"code":"E0716","explanation":"A temporary value is being dropped while a borrow is still in active use.\n\nErroneous code example:\n\n```compile_fail,E0716\nfn foo() -> i32 { 22 }\nfn bar(x: &i32) -> &i32 { x }\nlet p = bar(&foo());\n         // ------ creates a temporary\nlet q = *p;\n```\n\nHere, the expression `&foo()` is borrowing the expression `foo()`. As `foo()` is\na call to a function, and not the name of a variable, this creates a\n**temporary** -- that temporary stores the return value from `foo()` so that it\ncan be borrowed. You could imagine that `let p = bar(&foo());` is equivalent to\nthe following, which uses an explicit temporary variable.\n\nErroneous code example:\n\n```compile_fail,E0597\n# fn foo() -> i32 { 22 }\n# fn bar(x: &i32) -> &i32 { x }\nlet p = {\n  let tmp = foo(); // the temporary\n  bar(&tmp) // error: `tmp` does not live long enough\n}; // <-- tmp is freed as we exit this block\nlet q = p;\n```\n\nWhenever a temporary is created, it is automatically dropped (freed) according\nto fixed rules. Ordinarily, the temporary is dropped at the end of the enclosing\nstatement -- in this case, after the `let p`. This is illustrated in the example\nabove by showing that `tmp` would be freed as we exit the block.\n\nTo fix this problem, you need to create a local variable to store the value in\nrather than relying on a temporary. For example, you might change the original\nprogram to the following:\n\n```\nfn foo() -> i32 { 22 }\nfn bar(x: &i32) -> &i32 { x }\nlet value = foo(); // dropped at the end of the enclosing block\nlet p = bar(&value);\nlet q = *p;\n```\n\nBy introducing the explicit `let value`, we allocate storage that will last\nuntil the end of the enclosing block (when `value` goes out of scope). When we\nborrow `&value`, we are borrowing a local variable that already exists, and\nhence no temporary is created.\n\nTemporaries are not always dropped at the end of the enclosing statement. In\nsimple cases where the `&` expression is immediately stored into a variable, the\ncompiler will automatically extend the lifetime of the temporary until the end\nof the enclosing block. Therefore, an alternative way to fix the original\nprogram is to write `let tmp = &foo()` and not `let tmp = foo()`:\n\n```\nfn foo() -> i32 { 22 }\nfn bar(x: &i32) -> &i32 { x }\nlet value = &foo();\nlet p = bar(value);\nlet q = *p;\n```\n\nHere, we are still borrowing `foo()`, but as the borrow is assigned directly\ninto a variable, the temporary will not be dropped until the end of the\nenclosing block. Similar rules apply when temporaries are stored into aggregate\nstructures like a tuple or struct:\n\n```\n// Here, two temporaries are created, but\n// as they are stored directly into `value`,\n// they are not dropped until the end of the\n// enclosing block.\nfn foo() -> i32 { 22 }\nlet value = (&foo(), &foo());\n```\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-gnu\\lib/rustlib/src/rust\\library\\alloc\\src\\macros.rs","byte_start":4551,"byte_end":4691,"line_start":126,"line_end":129,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        $crate::__export::must_use({","highlight_start":9,"highlight_end":37},{"text":"            let res = $crate::fmt::format($crate::__export::format_args!($($arg)*));","highlight_start":1,"highlight_end":85},{"text":"            res","highlight_start":1,"highlight_end":16},{"text":"        })","highlight_start":1,"highlight_end":11}],"label":"creates a temporary value which is freed while still in use","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":16835,"byte_end":16863,"line_start":445,"line_end":445,"column_start":29,"column_end":57,"is_primary":false,"text":[{"text":"                .unwrap_or(&format!(\"index_{}\", attempt));","highlight_start":29,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"format!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-gnu\\lib/rustlib/src/rust\\library\\alloc\\src\\macros.rs","byte_start":4498,"byte_end":4517,"line_start":124,"line_end":124,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"macro_rules! format {","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":16864,"byte_end":16865,"line_start":445,"line_end":445,"column_start":58,"column_end":59,"is_primary":false,"text":[{"text":"                .unwrap_or(&format!(\"index_{}\", attempt));","highlight_start":58,"highlight_end":59}],"label":"temporary value is freed at the end of this statement","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":17137,"byte_end":17147,"line_start":453,"line_end":453,"column_start":72,"column_end":82,"is_primary":false,"text":[{"text":"                        info!(\"Created index '{}' on collection '{}'\", index_name, collection_name);","highlight_start":72,"highlight_end":82}],"label":"borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":101370,"byte_end":101418,"line_start":2961,"line_end":2961,"column_start":79,"column_end":127,"is_primary":false,"text":[{"text":"        $crate::valueset!(@ { (&$next, $crate::__macro_support::Option::Some(&$crate::__macro_support::format_args!($($rest)+) as &dyn Value)), $($out),* }, $next, )","highlight_start":79,"highlight_end":127}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":28791,"byte_end":28842,"line_start":904,"line_end":904,"column_start":9,"column_end":60,"is_primary":false,"text":[{"text":"        $crate::event!(target: $target, $lvl, { $($arg)+ })","highlight_start":9,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":71407,"byte_end":71522,"line_start":2110,"line_end":2114,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::INFO,","highlight_start":1,"highlight_end":33},{"text":"            $($arg)+","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":17090,"byte_end":17165,"line_start":453,"line_end":453,"column_start":25,"column_end":100,"is_primary":false,"text":[{"text":"                        info!(\"Created index '{}' on collection '{}'\", index_name, collection_name);","highlight_start":25,"highlight_end":100}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"info!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":62152,"byte_end":62169,"line_start":1866,"line_end":1866,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! info {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__macro_support::format_args!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-gnu\\lib/rustlib/src/rust\\library\\core\\src\\macros\\mod.rs","byte_start":37868,"byte_end":37892,"line_start":1093,"line_end":1093,"column_start":5,"column_end":29,"is_primary":false,"text":[{"text":"    macro_rules! format_args {","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"consider using a `let` binding to create a longer lived value","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:445:29: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0716]\u001b[0m\u001b[0m: temporary value dropped while borrowed: creates a temporary value which is freed while still in use\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no method named `get_str` found for struct `IndexModel` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":19612,"byte_end":19619,"line_start":511,"line_end":511,"column_start":53,"column_end":60,"is_primary":true,"text":[{"text":"                        if let Ok(name) = index_doc.get_str(\"name\") {","highlight_start":53,"highlight_end":60}],"label":"method not found in `IndexModel`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a method of the same name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":19612,"byte_end":19612,"line_start":511,"line_end":511,"column_start":53,"column_end":53,"is_primary":true,"text":[{"text":"                        if let Ok(name) = index_doc.get_str(\"name\") {","highlight_start":53,"highlight_end":53}],"label":null,"suggested_replacement":"keys.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:511:53: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m: no method named `get_str` found for struct `IndexModel` in the current scope: method not found in `IndexModel`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no method named `get_document` found for struct `IndexModel` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":19859,"byte_end":19871,"line_start":515,"line_end":515,"column_start":49,"column_end":61,"is_primary":true,"text":[{"text":"                                keys: index_doc.get_document(\"key\").unwrap_or(&doc! {}).clone(),","highlight_start":49,"highlight_end":61}],"label":"method not found in `IndexModel`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a method of the same name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":19859,"byte_end":19859,"line_start":515,"line_end":515,"column_start":49,"column_end":49,"is_primary":true,"text":[{"text":"                                keys: index_doc.get_document(\"key\").unwrap_or(&doc! {}).clone(),","highlight_start":49,"highlight_end":49}],"label":null,"suggested_replacement":"keys.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:515:49: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m: no method named `get_document` found for struct `IndexModel` in the current scope: method not found in `IndexModel`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no method named `get_bool` found for struct `IndexModel` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":19958,"byte_end":19966,"line_start":516,"line_end":516,"column_start":51,"column_end":59,"is_primary":true,"text":[{"text":"                                unique: index_doc.get_bool(\"unique\").unwrap_or(false),","highlight_start":51,"highlight_end":59}],"label":"method not found in `IndexModel`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a method of the same name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":19958,"byte_end":19958,"line_start":516,"line_end":516,"column_start":51,"column_end":51,"is_primary":true,"text":[{"text":"                                unique: index_doc.get_bool(\"unique\").unwrap_or(false),","highlight_start":51,"highlight_end":51}],"label":null,"suggested_replacement":"keys.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:516:51: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m: no method named `get_bool` found for struct `IndexModel` in the current scope: method not found in `IndexModel`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no method named `get_bool` found for struct `IndexModel` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":20049,"byte_end":20057,"line_start":517,"line_end":517,"column_start":55,"column_end":63,"is_primary":true,"text":[{"text":"                                background: index_doc.get_bool(\"background\").unwrap_or(false),","highlight_start":55,"highlight_end":63}],"label":"method not found in `IndexModel`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"one of the expressions' fields has a method of the same name","code":null,"level":"help","spans":[{"file_name":"src\\infrastructure\\database_indexes.rs","byte_start":20049,"byte_end":20049,"line_start":517,"line_end":517,"column_start":55,"column_end":55,"is_primary":true,"text":[{"text":"                                background: index_doc.get_bool(\"background\").unwrap_or(false),","highlight_start":55,"highlight_end":55}],"label":null,"suggested_replacement":"keys.","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\infrastructure\\database_indexes.rs:517:55: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m: no method named `get_bool` found for struct `IndexModel` in the current scope: method not found in `IndexModel`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `method`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4254,"byte_end":4260,"line_start":108,"line_end":108,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let method = req.method().clone();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4254,"byte_end":4260,"line_start":108,"line_end":108,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let method = req.method().clone();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_method","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\observability\\mod.rs:108:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `method`: help: if this is intentional, prefix it with an underscore: `_method`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4293,"byte_end":4297,"line_start":109,"line_end":109,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let path = req.uri().path().to_string();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4293,"byte_end":4297,"line_start":109,"line_end":109,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let path = req.uri().path().to_string();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\observability\\mod.rs:109:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `path`: help: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `duration`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4380,"byte_end":4388,"line_start":113,"line_end":113,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let duration = start.elapsed();","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4380,"byte_end":4388,"line_start":113,"line_end":113,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let duration = start.elapsed();","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_duration","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\observability\\mod.rs:113:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `duration`: help: if this is intentional, prefix it with an underscore: `_duration`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `status`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4416,"byte_end":4422,"line_start":114,"line_end":114,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let status = response.status().as_u16();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\observability\\mod.rs","byte_start":4416,"byte_end":4422,"line_start":114,"line_end":114,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let status = response.status().as_u16();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_status","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\observability\\mod.rs:114:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `status`: help: if this is intentional, prefix it with an underscore: `_status`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":19906,"byte_end":20009,"line_start":521,"line_end":523,"column_start":30,"column_end":14,"is_primary":true,"text":[{"text":"            |resource_chunk| async move {","highlight_start":30,"highlight_end":42},{"text":"                self.sync_resource_chunk(blueprint_id, resource_chunk).await","highlight_start":1,"highlight_end":77},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":"expected `Result<Vec<_>, ServiceError>`, found `async` block","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"      expected enum `Result<Vec<_>, services::ServiceError>`\nfound `async` block `{async block@src\\services\\blueprint.rs:521:30: 521:40}`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\blueprint.rs:521:30: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `Result<Vec<_>, ServiceError>`, found `async` block\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `blueprint_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24787,"byte_end":24799,"line_start":630,"line_end":630,"column_start":43,"column_end":55,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":43,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24787,"byte_end":24799,"line_start":630,"line_end":630,"column_start":43,"column_end":55,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":43,"highlight_end":55}],"label":null,"suggested_replacement":"_blueprint_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\blueprint.rs:630:43: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `blueprint_id`: help: if this is intentional, prefix it with an underscore: `_blueprint_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24811,"byte_end":24815,"line_start":630,"line_end":630,"column_start":67,"column_end":71,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":67,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":24811,"byte_end":24815,"line_start":630,"line_end":630,"column_start":67,"column_end":71,"is_primary":true,"text":[{"text":"    async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {","highlight_start":67,"highlight_end":71}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\blueprint.rs:630:67: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `name`: help: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `blueprint_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":25244,"byte_end":25256,"line_start":643,"line_end":643,"column_start":36,"column_end":48,"is_primary":true,"text":[{"text":"    async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {","highlight_start":36,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\blueprint.rs","byte_start":25244,"byte_end":25256,"line_start":643,"line_end":643,"column_start":36,"column_end":48,"is_primary":true,"text":[{"text":"    async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {","highlight_start":36,"highlight_end":48}],"label":null,"suggested_replacement":"_blueprint_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\blueprint.rs:643:36: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `blueprint_id`: help: if this is intentional, prefix it with an underscore: `_blueprint_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `metadata`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\build.rs","byte_start":30023,"byte_end":30031,"line_start":803,"line_end":803,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"                    let metadata = entry.metadata().await","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\build.rs","byte_start":30023,"byte_end":30031,"line_start":803,"line_end":803,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"                    let metadata = entry.metadata().await","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":"_metadata","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\build.rs:803:25: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `metadata`: help: if this is intentional, prefix it with an underscore: `_metadata`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"borrowed data escapes outside of method","code":{"code":"E0521","explanation":"Borrowed data escapes outside of closure.\n\nErroneous code example:\n\n```compile_fail,E0521\nlet mut list: Vec<&str> = Vec::new();\n\nlet _add = |el: &str| {\n    list.push(el); // error: `el` escapes the closure body here\n};\n```\n\nA type annotation of a closure parameter implies a new lifetime declaration.\nConsider to drop it, the compiler is reliably able to infer them.\n\n```\nlet mut list: Vec<&str> = Vec::new();\n\nlet _add = |el| {\n    list.push(el);\n};\n```\n\nSee the [Closure type inference and annotation][closure-infere-annotation] and\n[Lifetime elision][lifetime-elision] sections of the Book for more details.\n\n[closure-infere-annotation]: https://doc.rust-lang.org/book/ch13-01-closures.html#closure-type-inference-and-annotation\n[lifetime-elision]: https://doc.rust-lang.org/reference/lifetime-elision.html\n"},"level":"error","spans":[{"file_name":"src\\services\\build.rs","byte_start":8960,"byte_end":8965,"line_start":253,"line_end":253,"column_start":36,"column_end":41,"is_primary":false,"text":[{"text":"    async fn start_build_execution(&self, job_id: ObjectId, worker_id: &str) -> ServiceResult<()> {","highlight_start":36,"highlight_end":41}],"label":"`self` is a reference that is only valid in the method body","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\services\\build.rs","byte_start":8960,"byte_end":8965,"line_start":253,"line_end":253,"column_start":36,"column_end":41,"is_primary":false,"text":[{"text":"    async fn start_build_execution(&self, job_id: ObjectId, worker_id: &str) -> ServiceResult<()> {","highlight_start":36,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `async` block or function","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\services\\build.rs","byte_start":9623,"byte_end":9955,"line_start":272,"line_end":278,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        tokio::spawn(async move {","highlight_start":9,"highlight_end":34},{"text":"            if let Err(e) = build_service.execute_build(job_id).await {","highlight_start":1,"highlight_end":72},{"text":"                error!(\"Build execution failed for job {}: {}\", job_id, e);","highlight_start":1,"highlight_end":76},{"text":"                // Update job status to failed","highlight_start":1,"highlight_end":47},{"text":"                let _ = build_service.mark_build_failed(job_id, &e.to_string()).await;","highlight_start":1,"highlight_end":87},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        });","highlight_start":1,"highlight_end":11}],"label":"`self` escapes the method body here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":1348,"byte_end":1350,"line_start":42,"line_end":42,"column_start":6,"column_end":8,"is_primary":false,"text":[{"text":"impl<'a> BuildService<'a> {","highlight_start":6,"highlight_end":8}],"label":"lifetime `'a` defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\build.rs","byte_start":9623,"byte_end":9955,"line_start":272,"line_end":278,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        tokio::spawn(async move {","highlight_start":9,"highlight_end":34},{"text":"            if let Err(e) = build_service.execute_build(job_id).await {","highlight_start":1,"highlight_end":72},{"text":"                error!(\"Build execution failed for job {}: {}\", job_id, e);","highlight_start":1,"highlight_end":76},{"text":"                // Update job status to failed","highlight_start":1,"highlight_end":47},{"text":"                let _ = build_service.mark_build_failed(job_id, &e.to_string()).await;","highlight_start":1,"highlight_end":87},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        });","highlight_start":1,"highlight_end":11}],"label":"argument requires that `'a` must outlive `'static`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\services\\build.rs:272:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0521]\u001b[0m\u001b[0m: borrowed data escapes outside of method: `self` escapes the method body here, argument requires that `'a` must outlive `'static`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4547,"byte_end":4582,"line_start":115,"line_end":115,"column_start":31,"column_end":66,"is_primary":true,"text":[{"text":"                auto_scaling: request.runtime_config.auto_scaling,","highlight_start":31,"highlight_end":66}],"label":"expected `Option<AutoScalingConfig>`, found `AutoScalingConfig`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<models::deployment::AutoScalingConfig>`\n found struct `models::deployment::AutoScalingConfig`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try wrapping the expression in `Some`","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4547,"byte_end":4547,"line_start":115,"line_end":115,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"                auto_scaling: request.runtime_config.auto_scaling,","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":"Some(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":4582,"byte_end":4582,"line_start":115,"line_end":115,"column_start":66,"column_end":66,"is_primary":true,"text":[{"text":"                auto_scaling: request.runtime_config.auto_scaling,","highlight_start":66,"highlight_end":66}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:115:31: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `Option<AutoScalingConfig>`, found `AutoScalingConfig`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4614,"byte_end":4649,"line_start":116,"line_end":116,"column_start":31,"column_end":66,"is_primary":true,"text":[{"text":"                health_check: request.runtime_config.health_check,","highlight_start":31,"highlight_end":66}],"label":"expected `Option<HealthCheckConfig>`, found `HealthCheckConfig`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<models::deployment::HealthCheckConfig>`\n found struct `models::deployment::HealthCheckConfig`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try wrapping the expression in `Some`","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4614,"byte_end":4614,"line_start":116,"line_end":116,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"                health_check: request.runtime_config.health_check,","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":"Some(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":4649,"byte_end":4649,"line_start":116,"line_end":116,"column_start":66,"column_end":66,"is_primary":true,"text":[{"text":"                health_check: request.runtime_config.health_check,","highlight_start":66,"highlight_end":66}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:116:31: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `Option<HealthCheckConfig>`, found `HealthCheckConfig`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4679,"byte_end":4712,"line_start":117,"line_end":117,"column_start":29,"column_end":62,"is_primary":true,"text":[{"text":"                networking: request.runtime_config.networking,","highlight_start":29,"highlight_end":62}],"label":"expected `Option<NetworkingConfig>`, found `NetworkingConfig`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<models::deployment::NetworkingConfig>`\n found struct `models::deployment::NetworkingConfig`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try wrapping the expression in `Some`","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4679,"byte_end":4679,"line_start":117,"line_end":117,"column_start":29,"column_end":29,"is_primary":true,"text":[{"text":"                networking: request.runtime_config.networking,","highlight_start":29,"highlight_end":29}],"label":null,"suggested_replacement":"Some(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":4712,"byte_end":4712,"line_start":117,"line_end":117,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"                networking: request.runtime_config.networking,","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:117:29: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m: mismatched types: expected `Option<NetworkingConfig>`, found `NetworkingConfig`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no field `instances` on type `CreateRuntimeConfigRequest`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4764,"byte_end":4773,"line_start":118,"line_end":118,"column_start":51,"column_end":60,"is_primary":true,"text":[{"text":"                instances: request.runtime_config.instances,","highlight_start":51,"highlight_end":60}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:118:51: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m: no field `instances` on type `CreateRuntimeConfigRequest`: unknown field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no field `memory_mb` on type `CreateRuntimeConfigRequest`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4825,"byte_end":4834,"line_start":119,"line_end":119,"column_start":51,"column_end":60,"is_primary":true,"text":[{"text":"                memory_mb: request.runtime_config.memory_mb,","highlight_start":51,"highlight_end":60}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:119:51: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m: no field `memory_mb` on type `CreateRuntimeConfigRequest`: unknown field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no field `plan` on type `CreateRuntimeConfigRequest`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":4881,"byte_end":4885,"line_start":120,"line_end":120,"column_start":46,"column_end":50,"is_primary":true,"text":[{"text":"                plan: request.runtime_config.plan,","highlight_start":46,"highlight_end":50}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `service_type`, `instance_type`, `region`, `auto_scaling`, `health_check`, `networking`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:120:46: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m: no field `plan` on type `CreateRuntimeConfigRequest`: unknown field\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":9737,"byte_end":9744,"line_start":229,"line_end":229,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        request: TriggerDeploymentRequest,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":9737,"byte_end":9744,"line_start":229,"line_end":229,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        request: TriggerDeploymentRequest,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:229:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `request`: help: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `update_result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":12270,"byte_end":12283,"line_start":292,"line_end":292,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let update_result = self.deployments","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":12270,"byte_end":12283,"line_start":292,"line_end":292,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let update_result = self.deployments","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"_update_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:292:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `update_result`: help: if this is intentional, prefix it with an underscore: `_update_result`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `deployment_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14936,"byte_end":14949,"line_start":352,"line_end":352,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        deployment_id: ObjectId,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14936,"byte_end":14949,"line_start":352,"line_end":352,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        deployment_id: ObjectId,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_deployment_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:352:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `deployment_id`: help: if this is intentional, prefix it with an underscore: `_deployment_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `build_job_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14969,"byte_end":14981,"line_start":353,"line_end":353,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        build_job_id: String,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14969,"byte_end":14981,"line_start":353,"line_end":353,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        build_job_id: String,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_build_job_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:353:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `build_job_id`: help: if this is intentional, prefix it with an underscore: `_build_job_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":15003,"byte_end":15016,"line_start":354,"line_end":354,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":15003,"byte_end":15016,"line_start":354,"line_end":354,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"_state_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:354:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state_manager`: help: if this is intentional, prefix it with an underscore: `_state_manager`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14999,"byte_end":15016,"line_start":354,"line_end":354,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":14999,"byte_end":15003,"line_start":354,"line_end":354,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut state_manager: DeploymentStateManager,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:354:9: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: variable does not need to be mutable\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `application`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":18755,"byte_end":18766,"line_start":434,"line_end":434,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":18755,"byte_end":18766,"line_start":434,"line_end":434,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"    async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"_application","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:434:46: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `application`: help: if this is intentional, prefix it with an underscore: `_application`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"borrowed data escapes outside of method","code":{"code":"E0521","explanation":"Borrowed data escapes outside of closure.\n\nErroneous code example:\n\n```compile_fail,E0521\nlet mut list: Vec<&str> = Vec::new();\n\nlet _add = |el: &str| {\n    list.push(el); // error: `el` escapes the closure body here\n};\n```\n\nA type annotation of a closure parameter implies a new lifetime declaration.\nConsider to drop it, the compiler is reliably able to infer them.\n\n```\nlet mut list: Vec<&str> = Vec::new();\n\nlet _add = |el| {\n    list.push(el);\n};\n```\n\nSee the [Closure type inference and annotation][closure-infere-annotation] and\n[Lifetime elision][lifetime-elision] sections of the Book for more details.\n\n[closure-infere-annotation]: https://doc.rust-lang.org/book/ch13-01-closures.html#closure-type-inference-and-annotation\n[lifetime-elision]: https://doc.rust-lang.org/reference/lifetime-elision.html\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":28738,"byte_end":28743,"line_start":683,"line_end":683,"column_start":9,"column_end":14,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":14}],"label":"`self` is a reference that is only valid in the method body","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\services\\deployment.rs","byte_start":28738,"byte_end":28743,"line_start":683,"line_end":683,"column_start":9,"column_end":14,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `async` block or function","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\services\\deployment.rs","byte_start":29816,"byte_end":30179,"line_start":709,"line_end":718,"column_start":29,"column_end":10,"is_primary":true,"text":[{"text":"        let app_responses = self.chunk_processor.process_chunks(","highlight_start":29,"highlight_end":65},{"text":"            applications,","highlight_start":1,"highlight_end":26},{"text":"            |chunk| {","highlight_start":1,"highlight_end":22},{"text":"                let responses: Vec<ApplicationResponse> = chunk","highlight_start":1,"highlight_end":64},{"text":"                    .into_iter()","highlight_start":1,"highlight_end":33},{"text":"                    .map(|app| self.convert_to_application_response(app))","highlight_start":1,"highlight_end":74},{"text":"                    .collect::<Result<Vec<_>, _>>()?;","highlight_start":1,"highlight_end":54},{"text":"                Ok(responses)","highlight_start":1,"highlight_end":30},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        ).await?;","highlight_start":1,"highlight_end":10}],"label":"`self` escapes the method body here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":1403,"byte_end":1405,"line_start":40,"line_end":40,"column_start":6,"column_end":8,"is_primary":false,"text":[{"text":"impl<'a> DeploymentService<'a> {","highlight_start":6,"highlight_end":8}],"label":"lifetime `'a` defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":29816,"byte_end":30179,"line_start":709,"line_end":718,"column_start":29,"column_end":10,"is_primary":true,"text":[{"text":"        let app_responses = self.chunk_processor.process_chunks(","highlight_start":29,"highlight_end":65},{"text":"            applications,","highlight_start":1,"highlight_end":26},{"text":"            |chunk| {","highlight_start":1,"highlight_end":22},{"text":"                let responses: Vec<ApplicationResponse> = chunk","highlight_start":1,"highlight_end":64},{"text":"                    .into_iter()","highlight_start":1,"highlight_end":33},{"text":"                    .map(|app| self.convert_to_application_response(app))","highlight_start":1,"highlight_end":74},{"text":"                    .collect::<Result<Vec<_>, _>>()?;","highlight_start":1,"highlight_end":54},{"text":"                Ok(responses)","highlight_start":1,"highlight_end":30},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        ).await?;","highlight_start":1,"highlight_end":10}],"label":"argument requires that `'a` must outlive `'static`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\services\\deployment.rs:709:29: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0521]\u001b[0m\u001b[0m: borrowed data escapes outside of method: `self` escapes the method body here, argument requires that `'a` must outlive `'static`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"borrowed data escapes outside of method","code":{"code":"E0521","explanation":"Borrowed data escapes outside of closure.\n\nErroneous code example:\n\n```compile_fail,E0521\nlet mut list: Vec<&str> = Vec::new();\n\nlet _add = |el: &str| {\n    list.push(el); // error: `el` escapes the closure body here\n};\n```\n\nA type annotation of a closure parameter implies a new lifetime declaration.\nConsider to drop it, the compiler is reliably able to infer them.\n\n```\nlet mut list: Vec<&str> = Vec::new();\n\nlet _add = |el| {\n    list.push(el);\n};\n```\n\nSee the [Closure type inference and annotation][closure-infere-annotation] and\n[Lifetime elision][lifetime-elision] sections of the Book for more details.\n\n[closure-infere-annotation]: https://doc.rust-lang.org/book/ch13-01-closures.html#closure-type-inference-and-annotation\n[lifetime-elision]: https://doc.rust-lang.org/reference/lifetime-elision.html\n"},"level":"error","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":28738,"byte_end":28743,"line_start":683,"line_end":683,"column_start":9,"column_end":14,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":14}],"label":"`self` is a reference that is only valid in the method body","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\services\\deployment.rs","byte_start":28738,"byte_end":28743,"line_start":683,"line_end":683,"column_start":9,"column_end":14,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `async` block or function","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\services\\deployment.rs","byte_start":29816,"byte_end":30179,"line_start":709,"line_end":718,"column_start":29,"column_end":10,"is_primary":true,"text":[{"text":"        let app_responses = self.chunk_processor.process_chunks(","highlight_start":29,"highlight_end":65},{"text":"            applications,","highlight_start":1,"highlight_end":26},{"text":"            |chunk| {","highlight_start":1,"highlight_end":22},{"text":"                let responses: Vec<ApplicationResponse> = chunk","highlight_start":1,"highlight_end":64},{"text":"                    .into_iter()","highlight_start":1,"highlight_end":33},{"text":"                    .map(|app| self.convert_to_application_response(app))","highlight_start":1,"highlight_end":74},{"text":"                    .collect::<Result<Vec<_>, _>>()?;","highlight_start":1,"highlight_end":54},{"text":"                Ok(responses)","highlight_start":1,"highlight_end":30},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        ).await?;","highlight_start":1,"highlight_end":10}],"label":"`self` escapes the method body here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":28738,"byte_end":28739,"line_start":683,"line_end":683,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":10}],"label":"let's call the lifetime of this reference `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\deployment.rs","byte_start":29816,"byte_end":30179,"line_start":709,"line_end":718,"column_start":29,"column_end":10,"is_primary":true,"text":[{"text":"        let app_responses = self.chunk_processor.process_chunks(","highlight_start":29,"highlight_end":65},{"text":"            applications,","highlight_start":1,"highlight_end":26},{"text":"            |chunk| {","highlight_start":1,"highlight_end":22},{"text":"                let responses: Vec<ApplicationResponse> = chunk","highlight_start":1,"highlight_end":64},{"text":"                    .into_iter()","highlight_start":1,"highlight_end":33},{"text":"                    .map(|app| self.convert_to_application_response(app))","highlight_start":1,"highlight_end":74},{"text":"                    .collect::<Result<Vec<_>, _>>()?;","highlight_start":1,"highlight_end":54},{"text":"                Ok(responses)","highlight_start":1,"highlight_end":30},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        ).await?;","highlight_start":1,"highlight_end":10}],"label":"argument requires that `'1` must outlive `'static`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\services\\deployment.rs:709:29: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0521]\u001b[0m\u001b[0m: borrowed data escapes outside of method: `self` escapes the method body here, argument requires that `'1` must outlive `'static`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"replace `'a` with `'static`","code":null,"level":"help","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: replace `'a` with `'static`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `deployment`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":36174,"byte_end":36184,"line_start":879,"line_end":879,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let deployment = self.deployments","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\deployment.rs","byte_start":36174,"byte_end":36184,"line_start":879,"line_end":879,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let deployment = self.deployments","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_deployment","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\deployment.rs:879:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `deployment`: help: if this is intentional, prefix it with an underscore: `_deployment`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\disk.rs","byte_start":14186,"byte_end":14193,"line_start":396,"line_end":396,"column_start":73,"column_end":80,"is_primary":true,"text":[{"text":"    async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {","highlight_start":73,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\disk.rs","byte_start":14186,"byte_end":14193,"line_start":396,"line_end":396,"column_start":73,"column_end":80,"is_primary":true,"text":[{"text":"    async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {","highlight_start":73,"highlight_end":80}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\disk.rs:396:73: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `request`: help: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `application_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9539,"byte_end":9553,"line_start":249,"line_end":249,"column_start":42,"column_end":56,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":42,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9539,"byte_end":9553,"line_start":249,"line_end":249,"column_start":42,"column_end":56,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":42,"highlight_end":56}],"label":null,"suggested_replacement":"_application_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:249:42: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `application_id`: help: if this is intentional, prefix it with an underscore: `_application_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `repository`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9566,"byte_end":9576,"line_start":249,"line_end":249,"column_start":69,"column_end":79,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":69,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9566,"byte_end":9576,"line_start":249,"line_end":249,"column_start":69,"column_end":79,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":69,"highlight_end":79}],"label":null,"suggested_replacement":"_repository","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:249:69: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `repository`: help: if this is intentional, prefix it with an underscore: `_repository`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `webhook_secret`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9591,"byte_end":9605,"line_start":249,"line_end":249,"column_start":94,"column_end":108,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":94,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9591,"byte_end":9605,"line_start":249,"line_end":249,"column_start":94,"column_end":108,"is_primary":true,"text":[{"text":"    async fn setup_gitlab_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":94,"highlight_end":108}],"label":null,"suggested_replacement":"_webhook_secret","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:249:94: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `webhook_secret`: help: if this is intentional, prefix it with an underscore: `_webhook_secret`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `application_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9969,"byte_end":9983,"line_start":257,"line_end":257,"column_start":45,"column_end":59,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":45,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9969,"byte_end":9983,"line_start":257,"line_end":257,"column_start":45,"column_end":59,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":45,"highlight_end":59}],"label":null,"suggested_replacement":"_application_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:257:45: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `application_id`: help: if this is intentional, prefix it with an underscore: `_application_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `repository`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":9996,"byte_end":10006,"line_start":257,"line_end":257,"column_start":72,"column_end":82,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":72,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":9996,"byte_end":10006,"line_start":257,"line_end":257,"column_start":72,"column_end":82,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":72,"highlight_end":82}],"label":null,"suggested_replacement":"_repository","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:257:72: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `repository`: help: if this is intentional, prefix it with an underscore: `_repository`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `webhook_secret`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":10021,"byte_end":10035,"line_start":257,"line_end":257,"column_start":97,"column_end":111,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":97,"highlight_end":111}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":10021,"byte_end":10035,"line_start":257,"line_end":257,"column_start":97,"column_end":111,"is_primary":true,"text":[{"text":"    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {","highlight_start":97,"highlight_end":111}],"label":null,"suggested_replacement":"_webhook_secret","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:257:97: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `webhook_secret`: help: if this is intentional, prefix it with an underscore: `_webhook_secret`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `applications`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":11031,"byte_end":11043,"line_start":278,"line_end":278,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let applications = self.applications","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":11031,"byte_end":11043,"line_start":278,"line_end":278,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let applications = self.applications","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_applications","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:278:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `applications`: help: if this is intentional, prefix it with an underscore: `_applications`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\git.rs","byte_start":11308,"byte_end":11333,"line_start":286,"line_end":286,"column_start":13,"column_end":38,"is_primary":true,"text":[{"text":"        let mut triggered_deployments = Vec::new();","highlight_start":13,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\services\\git.rs","byte_start":11308,"byte_end":11312,"line_start":286,"line_end":286,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut triggered_deployments = Vec::new();","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\git.rs:286:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: variable does not need to be mutable\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `setup_script`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":9334,"byte_end":9346,"line_start":311,"line_end":311,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let setup_script = match pool_type {","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":9334,"byte_end":9346,"line_start":311,"line_end":311,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let setup_script = match pool_type {","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_setup_script","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:311:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `setup_script`: help: if this is intentional, prefix it with an underscore: `_setup_script`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"no method named `unwrap_or_else` found for struct `std::string::String` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":10893,"byte_end":10907,"line_start":345,"line_end":345,"column_start":39,"column_end":53,"is_primary":true,"text":[{"text":"                label: instance.label.unwrap_or_else(|| \"Unlabeled\".to_string()),","highlight_start":39,"highlight_end":53}],"label":"method not found in `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:345:39: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m: no method named `unwrap_or_else` found for struct `std::string::String` in the current scope: method not found in `String`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `instance.plan`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":12684,"byte_end":12697,"line_start":386,"line_end":386,"column_start":19,"column_end":32,"is_primary":false,"text":[{"text":"            plan: instance.plan,","highlight_start":19,"highlight_end":32}],"label":"value moved here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":13035,"byte_end":13049,"line_start":394,"line_end":394,"column_start":59,"column_end":73,"is_primary":true,"text":[{"text":"            suitable_for_achidas: is_suitable_for_achidas(&instance.plan),","highlight_start":59,"highlight_end":73}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"move occurs because `instance.plan` has type `std::string::String`, which does not implement the `Copy` trait","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"borrow occurs due to deref coercion to `str`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"deref defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-gnu\\lib/rustlib/src/rust\\library\\alloc\\src\\string.rs","byte_start":88436,"byte_end":88447,"line_start":2686,"line_end":2686,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    type Target = str;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:394:59: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m: borrow of moved value: `instance.plan`: value borrowed here after move\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":46903,"byte_end":46909,"line_start":1325,"line_end":1325,"column_start":53,"column_end":59,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":53,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":46903,"byte_end":46909,"line_start":1325,"line_end":1325,"column_start":53,"column_end":59,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":53,"highlight_end":59}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:1325:53: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `config`: help: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `server_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":46917,"byte_end":46926,"line_start":1325,"line_end":1325,"column_start":67,"column_end":76,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":67,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":46917,"byte_end":46926,"line_start":1325,"line_end":1325,"column_start":67,"column_end":76,"is_primary":true,"text":[{"text":"    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {","highlight_start":67,"highlight_end":76}],"label":null,"suggested_replacement":"_server_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:1325:67: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `server_id`: help: if this is intentional, prefix it with an underscore: `_server_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `specs`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47139,"byte_end":47144,"line_start":1330,"line_end":1330,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47139,"byte_end":47144,"line_start":1330,"line_end":1330,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":"_specs","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:1330:56: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `specs`: help: if this is intentional, prefix it with an underscore: `_specs`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `region`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47167,"byte_end":47173,"line_start":1330,"line_end":1330,"column_start":84,"column_end":90,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":84,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47167,"byte_end":47173,"line_start":1330,"line_end":1330,"column_start":84,"column_end":90,"is_primary":true,"text":[{"text":"    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":84,"highlight_end":90}],"label":null,"suggested_replacement":"_region","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:1330:84: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `region`: help: if this is intentional, prefix it with an underscore: `_region`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `specs`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47372,"byte_end":47377,"line_start":1335,"line_end":1335,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47372,"byte_end":47377,"line_start":1335,"line_end":1335,"column_start":61,"column_end":66,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":61,"highlight_end":66}],"label":null,"suggested_replacement":"_specs","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:1335:61: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `specs`: help: if this is intentional, prefix it with an underscore: `_specs`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `region`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47400,"byte_end":47406,"line_start":1335,"line_end":1335,"column_start":89,"column_end":95,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":89,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services\\intelligent_hosting.rs","byte_start":47400,"byte_end":47406,"line_start":1335,"line_end":1335,"column_start":89,"column_end":95,"is_primary":true,"text":[{"text":"    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {","highlight_start":89,"highlight_end":95}],"label":null,"suggested_replacement":"_region","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\services\\intelligent_hosting.rs:1335:89: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `region`: help: if this is intentional, prefix it with an underscore: `_region`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `response`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":9876,"byte_end":9884,"line_start":251,"line_end":251,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let response = self.client.delete(&url).await?;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\vultr\\mod.rs","byte_start":9876,"byte_end":9884,"line_start":251,"line_end":251,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let response = self.client.delete(&url).await?;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_response","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\vultr\\mod.rs:251:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `response`: help: if this is intentional, prefix it with an underscore: `_response`\u001b[0m\n"}
