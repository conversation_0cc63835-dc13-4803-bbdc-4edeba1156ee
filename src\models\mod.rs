use serde::{Deserialize, Serialize};
use validator::Validate;

pub mod user;
pub mod instance;
pub mod billing;
pub mod deployment;
pub mod build;
pub mod blueprint;
pub mod disk;
pub mod domain;
pub mod database;
pub mod common;

// User models
pub use user::{User, UserRole, UserStatus, UserProfile, Claims, CreateUserRequest, LoginRequest, LoginResponse};

// Instance models
pub use instance::{Instance, InstanceStatus, CreateInstanceRequest, InstanceResponse};

// Billing models - prefix ServiceType to avoid conflicts
pub use billing::{
    BillingAccount, BillingAccountResponse, BillingResponse,
    Invoice, InvoiceStatus, UsageRecord,
    ServiceType as BillingServiceType, InvoiceResponse
};

// Deployment models
pub use deployment::{
    Application, ApplicationStatus, ApplicationResponse, CreateApplicationRequest,
    Deployment, DeploymentStatus, DeploymentTrigger, DeploymentResponse, TriggerDeploymentRequest,
    Environment, EnvironmentResponse, RuntimeConfig, AutoScalingConfig, HealthCheckConfig, NetworkingConfig,
    BuildStep as DeploymentBuildStep, LogLevel as DeploymentLogLevel, Repository, DeploymentMetrics,
    GitProvider, GitWebhookPayload, CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
};

// Build models
pub use build::{
    BuildJob, BuildStatus, BuildSource, BuildConfig, BuildMetrics, BuildWorker,
    WorkerStatus, BuildQueue, BuildPriority, CreateBuildJobRequest, BuildJobResponse,
    BuildLogsResponse, BuildStep as BuildBuildStep, LogLevel as BuildLogLevel,
    LogStream, ArtifactType, BuildRuntime, BuildArtifact, BuildLogEntry
};

// Blueprint models - prefix ServiceType to avoid conflicts
pub use blueprint::{
    Blueprint, BlueprintStatus, BlueprintResource, ServiceConfig, BlueprintSync, SyncState, AchidasYaml,
    ServiceType as BlueprintServiceType, AutoScalingConfig as BlueprintAutoScalingConfig,
    HealthCheckConfig as BlueprintHealthCheckConfig, NetworkingConfig as BlueprintNetworkingConfig
};

// Disk models
pub use disk::{Disk, DiskStatus, DiskSnapshot, Pagination as DiskPagination};

// Domain models
pub use domain::{DomainStatus};

// Database models
pub use database::{Database, DatabaseStatus, CreateDatabaseRequest, UpdateDatabaseRequest, DatabaseResponse};

// Common models
pub use common::{ErrorResponse, HealthStatus};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponseMeta {
    pub timestamp: String,
    pub request_id: String,
    pub trace_id: String,
    pub version: String,
    pub status_code: u16,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub method: Option<String>,
}

impl Default for ApiResponseMeta {
    fn default() -> Self {
        Self {
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: uuid::Uuid::new_v4().to_string(),
            trace_id: uuid::Uuid::new_v4().to_string(),
            version: "v1".to_string(),
            status_code: 200,
            path: None,
            method: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    pub meta: ApiResponseMeta,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn success_with_meta(data: T, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta,
        }
    }

    pub fn error(error: String) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = 400;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_status(error: String, status_code: u16) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = status_code;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_meta(error: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn message_with_meta(message: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationMeta {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PaginationQuery {
    #[validate(range(min = 1, max = 100))]
    pub page: Option<u32>,
    #[validate(range(min = 1, max = 100))]
    pub per_page: Option<u32>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
        }
    }
}
