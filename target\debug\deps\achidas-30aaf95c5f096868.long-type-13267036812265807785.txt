fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<ApplicationResponse>>, ControllerError>> {update_application}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<ApplicationResponse>>, ControllerError>> {update_application}: Handler<_, _>
