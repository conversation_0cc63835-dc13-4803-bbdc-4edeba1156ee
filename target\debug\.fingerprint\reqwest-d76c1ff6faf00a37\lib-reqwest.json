{"rustc": 2830703817519440116, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2241668132362809309, "path": 6712895673869365022, "deps": [[40386456601120721, "percent_encoding", false, 82287743702150174], [95042085696191081, "ipnet", false, 1328521275746232759], [264090853244900308, "sync_wrapper", false, 12861071049203105036], [784494742817713399, "tower_service", false, 13980336227272009489], [1044435446100926395, "hyper_rustls", false, 5202342547696127460], [1906322745568073236, "pin_project_lite", false, 13886474930693114932], [3150220818285335163, "url", false, 8155342878909288223], [3722963349756955755, "once_cell", false, 10983383885145764756], [4405182208873388884, "http", false, 17638096306642354227], [5986029879202738730, "log", false, 14419858903727812710], [7414427314941361239, "hyper", false, 5736233268165940010], [7620660491849607393, "futures_core", false, 9910657298933926885], [8405603588346937335, "winreg", false, 12120213742894741467], [8915503303801890683, "http_body", false, 7113186864810676009], [9538054652646069845, "tokio", false, 4325870631136706026], [9689903380558560274, "serde", false, 811064473310142638], [10229185211513642314, "mime", false, 16750090556777528361], [10629569228670356391, "futures_util", false, 10549570323539430975], [11295624341523567602, "rustls", false, 313071644363730403], [12186126227181294540, "tokio_native_tls", false, 7519176343732391681], [12367227501898450486, "hyper_tls", false, 9863023733173887464], [13809605890706463735, "h2", false, 1947520839050327309], [14564311161534545801, "encoding_rs", false, 5005845872185192102], [15367738274754116744, "serde_json", false, 15573914083252631258], [16066129441945555748, "bytes", false, 2617022058346697638], [16311359161338405624, "rustls_pemfile", false, 16790249805810568630], [16542808166767769916, "serde_urlencoded", false, 9049045970661386166], [16622232390123975175, "tokio_rustls", false, 1671175774734322582], [16785601910559813697, "native_tls_crate", false, 8766616855177084928], [17652733826348741533, "webpki_roots", false, 14222400127770909713], [18066890886671768183, "base64", false, 17906936828461631922]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-d76c1ff6faf00a37\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}