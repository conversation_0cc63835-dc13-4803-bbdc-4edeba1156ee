{"rustc": 2830703817519440116, "features": "[\"default\", \"multipart\", \"tracing\", \"typed-header\"]", "declared_features": "[\"async-read-body\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 2241668132362809309, "path": 9230839002199561847, "deps": [[784494742817713399, "tower_service", false, 13980336227272009489], [1906322745568073236, "pin_project_lite", false, 13886474930693114932], [4359148418957042248, "axum_core", false, 10999768387176923551], [4891297352905791595, "axum", false, 8474853310602824377], [5695049318159433696, "tower", false, 16002821504046802549], [7435852374066785895, "headers", false, 10956787435684681616], [7712452662827335977, "tower_layer", false, 12375612145001944731], [9010263965687315507, "http", false, 15833340190285319200], [9689903380558560274, "serde", false, 811064473310142638], [10229185211513642314, "mime", false, 16750090556777528361], [10629569228670356391, "futures_util", false, 10549570323539430975], [12285238697122577036, "fastrand", false, 6652148121381478407], [12757619235593077227, "multer", false, 13164552754218707114], [14084095096285906100, "http_body", false, ************24312500], [16066129441945555748, "bytes", false, 2617022058346697638], [16900715236047033623, "http_body_util", false, 6146872258357789516]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-extra-b69948d8a67afcd8\\dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}