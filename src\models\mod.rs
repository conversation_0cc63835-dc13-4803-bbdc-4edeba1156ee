use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

// Date serialization helpers for MongoDB compatibility
pub mod date_serialization {
    use chrono::{DateTime, Utc};
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use serde_json::Value;

    pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Always serialize as BSON DateTime for MongoDB compatibility
        date.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        // Try to deserialize as BSON DateTime first, then fall back to string
        let value = Value::deserialize(deserializer)?;

        match value {
            // Handle BSON DateTime objects (they come as maps with $date field)
            Value::Object(map) if map.contains_key("$date") => {
                if let Some(date_value) = map.get("$date") {
                    match date_value {
                        Value::String(s) => {
                            DateTime::parse_from_rfc3339(s)
                                .map(|dt| dt.with_timezone(&Utc))
                                .map_err(serde::de::Error::custom)
                        }
                        Value::Number(n) => {
                            if let Some(timestamp) = n.as_i64() {
                                DateTime::from_timestamp(timestamp / 1000, ((timestamp % 1000) * 1_000_000) as u32)
                                    .ok_or_else(|| serde::de::Error::custom("Invalid timestamp"))
                            } else {
                                Err(serde::de::Error::custom("Invalid timestamp format"))
                            }
                        }
                        _ => Err(serde::de::Error::custom("Invalid BSON date format"))
                    }
                } else {
                    Err(serde::de::Error::custom("Missing $date field"))
                }
            }
            // Handle direct DateTime deserialization (native BSON DateTime)
            _ => {
                // Try direct DateTime deserialization first
                match DateTime::<Utc>::deserialize(value.clone()) {
                    Ok(dt) => Ok(dt),
                    Err(_) => {
                        // Fall back to string parsing
                        if let Value::String(s) = value {
                            DateTime::parse_from_rfc3339(&s)
                                .map(|dt| dt.with_timezone(&Utc))
                                .map_err(serde::de::Error::custom)
                        } else {
                            Err(serde::de::Error::custom("Expected DateTime, BSON date object, or RFC3339 string"))
                        }
                    }
                }
            }
        }
    }
}

pub mod optional_date_serialization {
    use chrono::{DateTime, Utc};
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use serde_json::Value;

    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(dt) => dt.serialize(serializer),
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt = Option::<Value>::deserialize(deserializer)?;
        match opt {
            Some(value) => {
                match value {
                    // Handle BSON DateTime objects (they come as maps with $date field)
                    Value::Object(map) if map.contains_key("$date") => {
                        if let Some(date_value) = map.get("$date") {
                            match date_value {
                                Value::String(s) => {
                                    DateTime::parse_from_rfc3339(s)
                                        .map(|dt| Some(dt.with_timezone(&Utc)))
                                        .map_err(serde::de::Error::custom)
                                }
                                Value::Number(n) => {
                                    if let Some(timestamp) = n.as_i64() {
                                        DateTime::from_timestamp(timestamp / 1000, ((timestamp % 1000) * 1_000_000) as u32)
                                            .map(Some)
                                            .ok_or_else(|| serde::de::Error::custom("Invalid timestamp"))
                                    } else {
                                        Err(serde::de::Error::custom("Invalid timestamp format"))
                                    }
                                }
                                _ => Err(serde::de::Error::custom("Invalid BSON date format"))
                            }
                        } else {
                            Err(serde::de::Error::custom("Missing $date field"))
                        }
                    }
                    // Handle direct DateTime deserialization (native BSON DateTime)
                    _ => {
                        // Try direct DateTime deserialization first
                        match DateTime::<Utc>::deserialize(value.clone()) {
                            Ok(dt) => Ok(Some(dt)),
                            Err(_) => {
                                // Fall back to string parsing
                                if let Value::String(s) = value {
                                    DateTime::parse_from_rfc3339(&s)
                                        .map(|dt| Some(dt.with_timezone(&Utc)))
                                        .map_err(serde::de::Error::custom)
                                } else {
                                    Err(serde::de::Error::custom("Expected DateTime, BSON date object, or RFC3339 string"))
                                }
                            }
                        }
                    }
                }
            }
            None => Ok(None),
        }
    }
}

pub mod user;
pub mod instance;
pub mod billing;
pub mod deployment;
pub mod build;
pub mod blueprint;
pub mod disk;
pub mod domain;
pub mod database;
pub mod common;

// User models
pub use user::{User, UserRole, UserStatus, UserProfile, Claims, CreateUserRequest, LoginRequest, LoginResponse};

// Instance models
pub use instance::{Instance, InstanceStatus, CreateInstanceRequest, InstanceResponse};

// Billing models - prefix ServiceType to avoid conflicts
pub use billing::{
    BillingAccount, BillingAccountResponse, BillingResponse,
    Invoice, InvoiceStatus, UsageRecord,
    ServiceType as BillingServiceType, InvoiceResponse
};

// Deployment models
pub use deployment::{
    Application, ApplicationStatus, ApplicationResponse, CreateApplicationRequest,
    Deployment, DeploymentStatus, DeploymentTrigger, DeploymentResponse, TriggerDeploymentRequest,
    Environment, EnvironmentResponse, RuntimeConfig, AutoScalingConfig, HealthCheckConfig, NetworkingConfig,
    BuildStep as DeploymentBuildStep, LogLevel as DeploymentLogLevel, Repository, DeploymentMetrics,
    GitProvider, GitWebhookPayload, CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
};

// Build models
pub use build::{
    BuildJob, BuildStatus, BuildSource, BuildConfig, BuildMetrics, BuildWorker,
    WorkerStatus, BuildQueue, BuildPriority, CreateBuildJobRequest, BuildJobResponse,
    BuildLogsResponse, BuildStep as BuildBuildStep, LogLevel as BuildLogLevel,
    LogStream, ArtifactType, BuildRuntime, BuildArtifact, BuildLogEntry
};

// Blueprint models - prefix ServiceType to avoid conflicts
pub use blueprint::{
    Blueprint, BlueprintStatus, BlueprintResource, ServiceConfig, BlueprintSync, SyncState, AchidasYaml,
    ServiceType as BlueprintServiceType, AutoScalingConfig as BlueprintAutoScalingConfig,
    HealthCheckConfig as BlueprintHealthCheckConfig, NetworkingConfig as BlueprintNetworkingConfig
};

// Disk models
pub use disk::{Disk, DiskStatus, DiskSnapshot, Pagination as DiskPagination};

// Domain models
pub use domain::{DomainStatus};

// Database models
pub use database::{Database, DatabaseStatus, CreateDatabaseRequest, UpdateDatabaseRequest, DatabaseResponse};

// Common models
pub use common::{ErrorResponse, HealthStatus};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponseMeta {
    pub timestamp: String,
    pub request_id: String,
    pub trace_id: String,
    pub version: String,
    pub status_code: u16,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub method: Option<String>,
}

impl Default for ApiResponseMeta {
    fn default() -> Self {
        Self {
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: uuid::Uuid::new_v4().to_string(),
            trace_id: uuid::Uuid::new_v4().to_string(),
            version: "v1".to_string(),
            status_code: 200,
            path: None,
            method: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    pub meta: ApiResponseMeta,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn success_with_meta(data: T, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta,
        }
    }

    pub fn error(error: String) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = 400;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_status(error: String, status_code: u16) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = status_code;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_meta(error: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn message_with_meta(message: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationMeta {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PaginationQuery {
    #[validate(range(min = 1, max = 100))]
    pub page: Option<u32>,
    #[validate(range(min = 1, max = 100))]
    pub per_page: Option<u32>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
        }
    }
}
