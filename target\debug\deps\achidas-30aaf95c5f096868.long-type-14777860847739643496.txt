fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, Query<{type error}>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<{type error}, {type error}>>>, ControllerError>> {list_deployments}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, Query<{type error}>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<{type error}, {type error}>>>, ControllerError>> {list_deployments}: Handler<_, _>
