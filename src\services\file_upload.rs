use crate::services::{ServiceError, ServiceResult};
use axum::extract::multipart::Multipart;
use std::path::{Path, PathBuf};
use tokio::fs;
use tokio::io::AsyncWriteExt;
use tracing::{info, warn, instrument};
use uuid::Uuid;
use zip::ZipArchive;
use std::io::Read;

pub struct FileUploadService {
    upload_path: PathBuf,
    max_file_size: u64,
    allowed_extensions: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct UploadedFile {
    pub id: String,
    pub original_name: String,
    pub file_path: PathBuf,
    pub size: u64,
    pub mime_type: String,
    pub extracted_path: Option<PathBuf>, // For compressed files
}

#[derive(Debug, Clone)]
pub struct FileUploadConfig {
    pub upload_path: String,
    pub max_file_size: u64, // in bytes
    pub allowed_extensions: Vec<String>,
}

impl Default for FileUploadConfig {
    fn default() -> Self {
        Self {
            upload_path: "./uploads".to_string(),
            max_file_size: 100 * 1024 * 1024, // 100MB
            allowed_extensions: vec![
                "zip".to_string(),
                "tar".to_string(),
                "tar.gz".to_string(),
                "tgz".to_string(),
                "js".to_string(),
                "html".to_string(),
                "css".to_string(),
                "json".to_string(),
                "md".to_string(),
                "txt".to_string(),
            ],
        }
    }
}

impl FileUploadService {
    pub fn new(config: FileUploadConfig) -> Self {
        Self {
            upload_path: PathBuf::from(config.upload_path),
            max_file_size: config.max_file_size,
            allowed_extensions: config.allowed_extensions,
        }
    }

    #[instrument(skip(self, multipart))]
    pub async fn handle_upload(&self, mut multipart: Multipart) -> ServiceResult<UploadedFile> {
        // Ensure upload directory exists
        fs::create_dir_all(&self.upload_path).await
            .map_err(|e| ServiceError::Internal(format!("Failed to create upload directory: {}", e)))?;

        while let Some(field) = multipart.next_field().await
            .map_err(|e| ServiceError::Validation(format!("Invalid multipart data: {}", e)))? {
            
            let name = field.name().unwrap_or("unknown");
            if name != "file" {
                continue;
            }

            let filename = field.file_name()
                .ok_or_else(|| ServiceError::Validation("No filename provided".to_string()))?
                .to_string();

            let content_type = field.content_type()
                .unwrap_or("application/octet-stream")
                .to_string();

            // Validate file extension
            if !self.is_allowed_file(&filename) {
                return Err(ServiceError::Validation(format!(
                    "File type not allowed. Allowed types: {}", 
                    self.allowed_extensions.join(", ")
                )));
            }

            // Generate unique filename
            let file_id = Uuid::new_v4().to_string();
            let extension = Path::new(&filename)
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("");
            
            let stored_filename = if extension.is_empty() {
                file_id.clone()
            } else {
                format!("{}.{}", file_id, extension)
            };

            let file_path = self.upload_path.join(&stored_filename);

            // Stream file to disk
            let data = field.bytes().await
                .map_err(|e| ServiceError::Internal(format!("Failed to read file data: {}", e)))?;

            // Check file size
            if data.len() as u64 > self.max_file_size {
                return Err(ServiceError::Validation(format!(
                    "File too large. Maximum size: {} MB", 
                    self.max_file_size / (1024 * 1024)
                )));
            }

            // Write file to disk
            let mut file = fs::File::create(&file_path).await
                .map_err(|e| ServiceError::Internal(format!("Failed to create file: {}", e)))?;
            
            file.write_all(&data).await
                .map_err(|e| ServiceError::Internal(format!("Failed to write file: {}", e)))?;

            info!("File uploaded: {} ({} bytes)", filename, data.len());

            let mut uploaded_file = UploadedFile {
                id: file_id,
                original_name: filename.clone(),
                file_path: file_path.clone(),
                size: data.len() as u64,
                mime_type: content_type,
                extracted_path: None,
            };

            // If it's a compressed file, extract it
            if self.is_compressed_file(&filename) {
                uploaded_file.extracted_path = Some(self.extract_compressed_file(&file_path, &file_id).await?);
            }

            return Ok(uploaded_file);
        }

        Err(ServiceError::Validation("No file found in upload".to_string()))
    }

    fn is_allowed_file(&self, filename: &str) -> bool {
        let extension = Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        self.allowed_extensions.iter().any(|ext| ext == &extension)
    }

    fn is_compressed_file(&self, filename: &str) -> bool {
        let extension = Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        matches!(extension.as_str(), "zip" | "tar" | "gz" | "tgz")
    }

    #[instrument(skip(self))]
    async fn extract_compressed_file(&self, file_path: &Path, file_id: &str) -> ServiceResult<PathBuf> {
        let extract_dir = self.upload_path.join(format!("{}_extracted", file_id));
        fs::create_dir_all(&extract_dir).await
            .map_err(|e| ServiceError::Internal(format!("Failed to create extraction directory: {}", e)))?;

        let extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        match extension.as_str() {
            "zip" => self.extract_zip(file_path, &extract_dir).await?,
            "tar" | "gz" | "tgz" => {
                warn!("TAR extraction not implemented yet, treating as regular file");
            }
            _ => {
                return Err(ServiceError::Internal("Unsupported compression format".to_string()));
            }
        }

        Ok(extract_dir)
    }

    async fn extract_zip(&self, zip_path: &Path, extract_dir: &Path) -> ServiceResult<()> {
        let file = std::fs::File::open(zip_path)
            .map_err(|e| ServiceError::Internal(format!("Failed to open zip file: {}", e)))?;

        let mut archive = ZipArchive::new(file)
            .map_err(|e| ServiceError::Internal(format!("Failed to read zip archive: {}", e)))?;

        for i in 0..archive.len() {
            let mut file = archive.by_index(i)
                .map_err(|e| ServiceError::Internal(format!("Failed to read zip entry: {}", e)))?;

            let outpath = match file.enclosed_name() {
                Some(path) => extract_dir.join(path),
                None => continue,
            };

            if file.name().ends_with('/') {
                // Directory
                fs::create_dir_all(&outpath).await
                    .map_err(|e| ServiceError::Internal(format!("Failed to create directory: {}", e)))?;
            } else {
                // File
                if let Some(p) = outpath.parent() {
                    if !p.exists() {
                        fs::create_dir_all(p).await
                            .map_err(|e| ServiceError::Internal(format!("Failed to create parent directory: {}", e)))?;
                    }
                }

                let mut outfile = fs::File::create(&outpath).await
                    .map_err(|e| ServiceError::Internal(format!("Failed to create extracted file: {}", e)))?;

                let mut buffer = Vec::new();
                file.read_to_end(&mut buffer)
                    .map_err(|e| ServiceError::Internal(format!("Failed to read zip entry: {}", e)))?;

                outfile.write_all(&buffer).await
                    .map_err(|e| ServiceError::Internal(format!("Failed to write extracted file: {}", e)))?;
            }
        }

        info!("Successfully extracted zip file to: {}", extract_dir.display());
        Ok(())
    }

    pub async fn cleanup_file(&self, file_id: &str) -> ServiceResult<()> {
        // Remove uploaded file
        let file_pattern = format!("{}.*", file_id);
        let mut entries = fs::read_dir(&self.upload_path).await
            .map_err(|e| ServiceError::Internal(format!("Failed to read upload directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ServiceError::Internal(format!("Failed to read directory entry: {}", e)))? {
            
            let filename = entry.file_name().to_string_lossy().to_string();
            if filename.starts_with(file_id) {
                fs::remove_file(entry.path()).await
                    .map_err(|e| ServiceError::Internal(format!("Failed to remove file: {}", e)))?;
            }
        }

        // Remove extracted directory if exists
        let extract_dir = self.upload_path.join(format!("{}_extracted", file_id));
        if extract_dir.exists() {
            fs::remove_dir_all(&extract_dir).await
                .map_err(|e| ServiceError::Internal(format!("Failed to remove extracted directory: {}", e)))?;
        }

        Ok(())
    }

    pub fn get_file_path(&self, file_id: &str) -> PathBuf {
        self.upload_path.join(file_id)
    }

    pub fn get_extracted_path(&self, file_id: &str) -> PathBuf {
        self.upload_path.join(format!("{}_extracted", file_id))
    }
}
