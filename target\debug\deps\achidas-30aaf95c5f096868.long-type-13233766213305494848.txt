fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<LinkApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {link_application}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<LinkApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {link_application}: Handler<_, _>
