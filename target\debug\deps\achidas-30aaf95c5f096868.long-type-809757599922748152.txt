fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<SetEnvironmentVariableRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {set_environment_variable}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::Json<SetEnvironmentVariableRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {set_environment_variable}: Handler<_, _>
