pub mod auth;
pub mod cors;
pub mod logging;

use axum::{
    http::{Request, StatusCode, HeaderValue, HeaderMap},
    middleware::Next,
    response::{Response, IntoResponse},
    body::<PERSON>,
    <PERSON><PERSON>,
    extract::Query,
};
use std::time::Instant;
use tracing::{info, warn, Span};
use uuid::Uuid;
use crate::models::{ApiResponse, ApiResponseMeta};

// Request ID and Trace ID middleware
pub async fn request_id_middleware(
    mut req: Request<Body>,
    next: Next,
) -> Response {
    // Generate or extract request ID
    let request_id = req.headers()
        .get("x-request-id")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| Uuid::new_v4().to_string());

    // Generate trace ID
    let trace_id = Uuid::new_v4().to_string();

    // Add IDs to request headers
    req.headers_mut().insert(
        "x-request-id",
        HeaderValue::from_str(&request_id).unwrap()
    );
    req.headers_mut().insert(
        "x-trace-id",
        HeaderValue::from_str(&trace_id).unwrap()
    );

    // Add to tracing span
    let span = Span::current();
    span.record("request_id", &request_id);
    span.record("trace_id", &trace_id);

    let mut response = next.run(req).await;

    // Add IDs to response headers
    response.headers_mut().insert(
        "x-request-id",
        HeaderValue::from_str(&request_id).unwrap()
    );
    response.headers_mut().insert(
        "x-trace-id",
        HeaderValue::from_str(&trace_id).unwrap()
    );

    response
}

// Request timing middleware
pub async fn timing_middleware(
    req: Request<Body>,
    next: Next,
) -> Response {
    let start = Instant::now();
    let method = req.method().clone();
    let uri = req.uri().clone();
    
    let response = next.run(req).await;
    
    let duration = start.elapsed();
    let status = response.status();
    
    if status.is_server_error() {
        warn!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            "Request completed with server error"
        );
    } else {
        info!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            "Request completed"
        );
    }
    
    response
}

// JSON error handling middleware - ensures all responses are JSON formatted
pub async fn json_error_handling_middleware(
    req: Request<Body>,
    next: Next,
) -> Response {
    let method = req.method().clone();
    let path = req.uri().path().to_string();
    let request_id = req.headers()
        .get("x-request-id")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    let trace_id = req.headers()
        .get("x-trace-id")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| Uuid::new_v4().to_string());

    let response = next.run(req).await;
    let status = response.status();

    // If response is not successful, check if it's already a JSON response from our controllers
    if !status.is_success() {
        // Check if the response has a content-type header indicating JSON
        let content_type = response.headers().get("content-type")
            .and_then(|h| h.to_str().ok())
            .unwrap_or("");

        // If it's already JSON (from our controllers), pass it through unchanged
        if content_type.contains("application/json") {
            return response;
        }

        // Otherwise, convert to JSON error format for non-JSON responses
        // Create copies for logging
        let log_method = method.clone();
        let log_path = path.clone();
        let log_request_id = request_id.clone();

        let meta = ApiResponseMeta {
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: request_id.unwrap_or_else(|| Uuid::new_v4().to_string()),
            trace_id: trace_id.clone(),
            version: "v1".to_string(),
            status_code: status.as_u16(),
            path: Some(path),
            method: Some(method.to_string()),
        };

        let error_response = match status {
            StatusCode::NOT_FOUND => {
                ApiResponse::<()>::error_with_meta(
                    format!("Route not found: {} {}", method, meta.path.as_ref().unwrap()),
                    meta
                )
            },
            StatusCode::METHOD_NOT_ALLOWED => {
                ApiResponse::<()>::error_with_meta(
                    format!("Method {} not allowed for this route", method),
                    meta
                )
            },
            StatusCode::UNSUPPORTED_MEDIA_TYPE => {
                ApiResponse::<()>::error_with_meta(
                    "Expected request with 'Content-Type: application/json'".to_string(),
                    meta
                )
            },
            StatusCode::BAD_REQUEST => {
                ApiResponse::<()>::error_with_meta(
                    "Bad request - invalid request format or parameters".to_string(),
                    meta
                )
            },
            StatusCode::UNAUTHORIZED => {
                ApiResponse::<()>::error_with_meta(
                    "Unauthorized - authentication required".to_string(),
                    meta
                )
            },
            StatusCode::FORBIDDEN => {
                ApiResponse::<()>::error_with_meta(
                    "Forbidden - insufficient permissions".to_string(),
                    meta
                )
            },
            StatusCode::INTERNAL_SERVER_ERROR => {
                ApiResponse::<()>::error_with_meta(
                    "Internal server error - please try again later".to_string(),
                    meta
                )
            },
            _ => {
                ApiResponse::<()>::error_with_meta(
                    format!("Request failed with status: {}", status),
                    meta
                )
            }
        };

        // Log the error
        warn!(
            method = %log_method,
            path = %log_path,
            status = %status,
            request_id = ?log_request_id,
            "Request failed"
        );

        return (status, Json(error_response)).into_response();
    }

    response
}

// 404 handler for unmatched routes
pub async fn handle_404() -> impl IntoResponse {
    let meta = ApiResponseMeta {
        timestamp: chrono::Utc::now().to_rfc3339(),
        request_id: Uuid::new_v4().to_string(),
        trace_id: Uuid::new_v4().to_string(),
        version: "v1".to_string(),
        status_code: 404,
        path: None,
        method: None,
    };

    let error_response = ApiResponse::<()>::error_with_meta(
        "Route not found".to_string(),
        meta
    );

    (StatusCode::NOT_FOUND, Json(error_response))
}
