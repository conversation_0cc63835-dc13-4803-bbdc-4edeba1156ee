{"rustc": 2830703817519440116, "features": "[\"default\", \"metrics\", \"smallvec\", \"tracing-log\"]", "declared_features": "[\"async-trait\", \"default\", \"metrics\", \"smallvec\", \"thiserror\", \"tracing-log\"]", "target": 12070306627020974532, "profile": 15657897354478470176, "path": 1591878164374224254, "deps": [[1337443197003760792, "opentelemetry", false, 14874685377148721533], [3424551429995674438, "tracing_core", false, 4904015800900561561], [3666196340704888985, "smallvec", false, 2673802910270356896], [3722963349756955755, "once_cell", false, 15293267452495451070], [7009435218076268184, "opentelemetry_sdk", false, 6147228315686530294], [8606274917505247608, "tracing", false, 17320339988540626530], [10806489435541507125, "tracing_log", false, 14258795699243274997], [16230660778393187092, "tracing_subscriber", false, 16646603048147089395]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-opentelemetry-add0933c5a4b0b06\\dep-lib-tracing_opentelemetry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}