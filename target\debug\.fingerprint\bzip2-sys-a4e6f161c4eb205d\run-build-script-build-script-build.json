{"rustc": 2830703817519440116, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4838720168581948726, "build_script_build", false, 10697552881574550995]], "local": [{"RerunIfEnvChanged": {"var": "CC_x86_64-pc-windows-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_pc_windows_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_pc_windows_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-pc-windows-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-gnu", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}