fn(axum::extract::State<Arc<AppState>>, Claims) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<EnvironmentGroupResponse>>>, ControllerError>> {list_environment_groups}
fn(axum::extract::State<Arc<AppState>>, Claims) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<Vec<EnvironmentGroupResponse>>>, ControllerError>> {list_environment_groups}: Handler<_, _>
