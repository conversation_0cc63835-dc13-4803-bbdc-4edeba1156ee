use crate::{
    controllers::{Controller<PERSON><PERSON><PERSON>, ControllerResult},
    models::{ApiResponse, Claims, deployment::{HostingTier, CustomPlan}},
    services::hosting_plans::HostingPlansService,
    AppState,
};
use axum::{
    extract::{Path, Query, State},
    response::Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::instrument;

#[derive(Debug, Deserialize)]
pub struct PlanQuery {
    pub tier: Option<String>,
    pub service_type: Option<String>,
    pub expected_traffic: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct HostingPlanResponse {
    pub tier: HostingTier,
    pub plan_name: String,
    pub description: String,
    pub features: Vec<String>,
    pub cpu_allocation: Option<f32>,
    pub memory_allocation: Option<f32>,
    pub storage_allocation: Option<f32>,
    pub bandwidth_limit: Option<f32>,
    pub max_concurrent_users: Option<u32>,
    pub price_per_hour: f64,
    pub price_per_month: f64,
    pub vultr_plan: Option<String>,
    pub recommended_for: Vec<String>,
}

// Get all available hosting plans
#[instrument(skip(_state))]
pub async fn list_hosting_plans(
    State(_state): State<Arc<AppState>>,
    Query(query): Query<PlanQuery>,
) -> ControllerResult<Json<ApiResponse<Vec<HostingPlanResponse>>>> {
    let hosting_service = HostingPlansService::new();
    
    let plans = if let Some(tier_str) = query.tier {
        let tier = parse_hosting_tier(&tier_str)?;
        hosting_service.get_plans_by_tier(&tier)
            .map_err(|e| ControllerError::Internal(format!("Failed to get plans: {}", e)))?
    } else {
        hosting_service.get_all_plans()
            .map_err(|e| ControllerError::Internal(format!("Failed to get plans: {}", e)))?
    };

    let plan_responses: Vec<HostingPlanResponse> = plans.into_iter()
        .map(|plan| convert_to_response(plan))
        .collect();

    Ok(Json(ApiResponse::success(plan_responses)))
}

// Get recommended plan based on requirements
#[instrument(skip(_state))]
pub async fn get_recommended_plan(
    State(_state): State<Arc<AppState>>,
    Query(query): Query<PlanQuery>,
) -> ControllerResult<Json<ApiResponse<HostingPlanResponse>>> {
    let hosting_service = HostingPlansService::new();
    
    let service_type = query.service_type.unwrap_or_else(|| "StaticWebsite".to_string());
    let expected_traffic = query.expected_traffic.unwrap_or_else(|| "low".to_string());
    
    let plan = hosting_service.recommend_plan(&service_type, &expected_traffic)
        .map_err(|e| ControllerError::Internal(format!("Failed to get recommendation: {}", e)))?;

    let response = convert_to_response(plan);
    Ok(Json(ApiResponse::success(response)))
}

// Get specific plan by name
#[instrument(skip(_state))]
pub async fn get_plan_by_name(
    State(_state): State<Arc<AppState>>,
    Path(plan_name): Path<String>,
) -> ControllerResult<Json<ApiResponse<HostingPlanResponse>>> {
    let hosting_service = HostingPlansService::new();
    
    let plan = hosting_service.get_plan_by_name(&plan_name)
        .map_err(|e| ControllerError::Internal(format!("Failed to get plan: {}", e)))?
        .ok_or_else(|| ControllerError::NotFound(format!("Plan '{}' not found", plan_name)))?;

    let response = convert_to_response(plan);
    Ok(Json(ApiResponse::success(response)))
}

// Get pricing calculator for shared hosting
#[instrument(skip(_state, _claims))]
pub async fn calculate_pricing(
    State(_state): State<Arc<AppState>>,
    _claims: Claims,
    Path(plan_name): Path<String>,
    Query(usage): Query<UsageQuery>,
) -> ControllerResult<Json<ApiResponse<PricingCalculation>>> {
    let hosting_service = HostingPlansService::new();
    
    let plan = hosting_service.get_plan_by_name(&plan_name)
        .map_err(|e| ControllerError::Internal(format!("Failed to get plan: {}", e)))?
        .ok_or_else(|| ControllerError::NotFound(format!("Plan '{}' not found", plan_name)))?;

    // Only calculate for shared hosting
    if !matches!(plan.tier, HostingTier::Shared) {
        return Err(ControllerError::Validation("Pricing calculation only available for shared hosting plans".to_string()));
    }

    let cost = hosting_service.calculate_shared_pricing(
        &plan,
        usage.cpu_usage.unwrap_or(0.0),
        usage.memory_usage.unwrap_or(0.0),
        usage.bandwidth_gb.unwrap_or(0.0),
    );

    let calculation = PricingCalculation {
        plan_name: plan.plan_name,
        base_cost: plan.price_per_hour,
        usage_cost: cost - plan.price_per_hour,
        total_cost: cost,
        currency: "USD".to_string(),
        period: "hour".to_string(),
    };

    Ok(Json(ApiResponse::success(calculation)))
}

#[derive(Debug, Deserialize)]
pub struct UsageQuery {
    pub cpu_usage: Option<f32>,
    pub memory_usage: Option<f32>,
    pub bandwidth_gb: Option<f32>,
}

#[derive(Debug, Serialize)]
pub struct PricingCalculation {
    pub plan_name: String,
    pub base_cost: f64,
    pub usage_cost: f64,
    pub total_cost: f64,
    pub currency: String,
    pub period: String,
}

// Helper functions
fn parse_hosting_tier(tier_str: &str) -> Result<HostingTier, ControllerError> {
    match tier_str.to_lowercase().as_str() {
        "shared" => Ok(HostingTier::Shared),
        "dedicated" => Ok(HostingTier::Dedicated),
        "enterprise" => Ok(HostingTier::Enterprise),
        _ => Err(ControllerError::Validation(format!("Invalid hosting tier: {}", tier_str))),
    }
}

fn convert_to_response(plan: CustomPlan) -> HostingPlanResponse {
    let tier = plan.tier.clone(); // Clone to avoid move
    let (description, features, recommended_for) = match plan.tier {
        HostingTier::Shared => (
            format!("Shared hosting with {}% CPU, {}% memory allocation", 
                   (plan.cpu_allocation * 100.0) as u32, 
                   (plan.memory_allocation * 100.0) as u32),
            vec![
                "Pay-as-you-go pricing".to_string(),
                "Shared server resources".to_string(),
                "Load balancing across multiple servers".to_string(),
                "Auto-scaling".to_string(),
                "SSL certificates included".to_string(),
                format!("Up to {} concurrent users", plan.max_concurrent_users.unwrap_or(0)),
                format!("{}GB bandwidth/month", plan.bandwidth_limit.unwrap_or(0.0)),
            ],
            vec![
                "Small websites".to_string(),
                "Personal projects".to_string(),
                "Low-traffic applications".to_string(),
                "Cost-conscious users".to_string(),
            ]
        ),
        HostingTier::Dedicated => (
            format!("Dedicated server with {} plan", plan.vultr_plan.as_ref().unwrap_or(&"custom".to_string())),
            vec![
                "Dedicated server resources".to_string(),
                "Full root access".to_string(),
                "Guaranteed performance".to_string(),
                "No resource sharing".to_string(),
                "Custom configurations".to_string(),
                "24/7 monitoring".to_string(),
                "Unlimited bandwidth".to_string(),
            ],
            vec![
                "Business applications".to_string(),
                "High-traffic websites".to_string(),
                "Custom applications".to_string(),
                "Performance-critical workloads".to_string(),
            ]
        ),
        HostingTier::Enterprise => (
            format!("Enterprise hosting with {} plan", plan.vultr_plan.as_ref().unwrap_or(&"custom".to_string())),
            vec![
                "Multiple dedicated servers".to_string(),
                "Advanced load balancing".to_string(),
                "High availability setup".to_string(),
                "Priority support".to_string(),
                "Custom SLA".to_string(),
                "Advanced monitoring".to_string(),
                "Disaster recovery".to_string(),
            ],
            vec![
                "Large enterprises".to_string(),
                "Mission-critical applications".to_string(),
                "High-availability requirements".to_string(),
                "Complex architectures".to_string(),
            ]
        ),
    };

    HostingPlanResponse {
        tier: tier.clone(),
        plan_name: plan.plan_name,
        description,
        features,
        cpu_allocation: if matches!(tier, HostingTier::Shared) { Some(plan.cpu_allocation) } else { None },
        memory_allocation: if matches!(tier, HostingTier::Shared) { Some(plan.memory_allocation) } else { None },
        storage_allocation: if matches!(tier, HostingTier::Shared) { Some(plan.storage_allocation) } else { None },
        bandwidth_limit: plan.bandwidth_limit,
        max_concurrent_users: plan.max_concurrent_users,
        price_per_hour: plan.price_per_hour,
        price_per_month: plan.price_per_month,
        vultr_plan: plan.vultr_plan,
        recommended_for,
    }
}
