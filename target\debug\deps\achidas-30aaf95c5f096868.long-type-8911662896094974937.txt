fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<(std::string::String, std::string::String)>, Query<LogsQuery>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<LogsResponse>>, ControllerError>> {get_deployment_logs}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<(std::string::String, std::string::String)>, Query<LogsQuery>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<LogsResponse>>, ControllerError>> {get_deployment_logs}: Handler<_, _>
