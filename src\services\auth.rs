use crate::{
    config::Config,
    database::Database,
    models::{
        <PERSON><PERSON>ms, CreateUserRequest, LoginRequest, LoginResponse, User, UserProfile, UserRole,
        UserStatus,
    },
    services::{ServiceError, ServiceResult},
};
use bcrypt::{hash, verify, DEFAULT_COST};
use bson::{doc, oid::ObjectId};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, Header, Validation};
use mongodb::Collection;
use tracing::{error, info, instrument};
use crate::{log_auth_event, log_database_operation};

pub struct AuthService<'a> {
    users: Collection<User>,
    config: &'a Config,
}

impl<'a> AuthService<'a> {
    pub fn new(database: &Database, config: &'a Config) -> Self {
        Self {
            users: database.collection("users"),
            config,
        }
    }

    #[instrument(skip(self, request))]
    pub async fn register(&self, request: CreateUserRequest) -> ServiceResult<UserProfile> {
        // Extract validated fields (these should always be Some due to controller validation)
        let email = request.email.as_ref().ok_or_else(|| {
            ServiceError::Validation("Email is required".to_string())
        })?;
        let password = request.password.as_ref().ok_or_else(|| {
            ServiceError::Validation("Password is required".to_string())
        })?;
        let first_name = request.first_name.as_ref().ok_or_else(|| {
            ServiceError::Validation("First name is required".to_string())
        })?;
        let last_name = request.last_name.as_ref().ok_or_else(|| {
            ServiceError::Validation("Last name is required".to_string())
        })?;

        // Check if user already exists
        let existing_user = self
            .users
            .find_one(doc! { "email": email }, None)
            .await?;

        if existing_user.is_some() {
            return Err(ServiceError::Conflict("User already exists".to_string()));
        }

        // Hash password
        let password_hash = hash(password, DEFAULT_COST)
            .map_err(|e| ServiceError::Internal(format!("Failed to hash password: {}", e)))?;

        // Create user
        let now = Utc::now();
        let user = User {
            id: None,
            email: email.clone(),
            password_hash,
            first_name: first_name.clone(),
            last_name: last_name.clone(),
            company: request.company.clone(),
            role: UserRole::User,
            status: UserStatus::Active,
            vultr_subaccount_id: None,
            created_at: now,
            updated_at: now,
            last_login: None,
        };

        let result = self.users.insert_one(&user, None).await?;
        let user_id = result.inserted_id.as_object_id().unwrap();

        let mut created_user = user;
        created_user.id = Some(user_id);

        info!("User registered successfully: {}", created_user.email);
        Ok(created_user.into())
    }

    #[instrument(skip(self, request))]
    pub async fn login(&self, request: LoginRequest) -> ServiceResult<LoginResponse> {
        // Extract validated fields (these should always be Some due to controller validation)
        let email = request.email.as_ref().ok_or_else(|| {
            ServiceError::Validation("Email is required".to_string())
        })?;
        let password = request.password.as_ref().ok_or_else(|| {
            ServiceError::Validation("Password is required".to_string())
        })?;

        log_auth_event!(info, email, "login_attempt", "User attempting to log in");

        // Find user by email
        let start_time = std::time::Instant::now();
        let user = self
            .users
            .find_one(doc! { "email": email }, None)
            .await?
            .ok_or_else(|| {
                log_auth_event!(warn, email, "login_failed", "User not found");
                ServiceError::Authentication("Invalid credentials".to_string())
            })?;

        log_database_operation!(info, "find_user", "users", start_time.elapsed().as_millis() as u64, "none");
        log_auth_event!(info, email, "user_found", "User found, verifying password");

        // Verify password
        let password_valid = verify(password, &user.password_hash)
            .map_err(|e| {
                error!("Password verification error for user {}: {}", email, e);
                ServiceError::Internal(format!("Password verification failed: {}", e))
            })?;

        if !password_valid {
            log_auth_event!(warn, email, "login_failed", "Invalid password");
            return Err(ServiceError::Authentication("Invalid credentials".to_string()));
        }

        log_auth_event!(info, email, "password_verified", "Password verification successful");

        // Check user status
        match user.status {
            UserStatus::Active => {}
            UserStatus::Suspended => {
                return Err(ServiceError::Authorization("Account suspended".to_string()));
            }
            UserStatus::Inactive => {
                return Err(ServiceError::Authorization("Account inactive".to_string()));
            }
            UserStatus::PendingVerification => {
                return Err(ServiceError::Authorization(
                    "Account pending verification".to_string(),
                ));
            }
        }

        // Update last login
        let now = Utc::now();
        self.users
            .update_one(
                doc! { "_id": user.id.unwrap() },
                doc! { "$set": { "last_login": now } },
                None,
            )
            .await?;

        // Generate JWT token
        let token = self.generate_token(&user)?;
        let expires_at = now + Duration::hours(24);

        let mut updated_user = user;
        updated_user.last_login = Some(now);

        log_auth_event!(info, &updated_user.email, "login_success", "User logged in successfully");

        Ok(LoginResponse {
            token,
            user: updated_user.into(),
            expires_at,
        })
    }

    #[instrument(skip(self))]
    pub fn generate_token(&self, user: &User) -> ServiceResult<String> {
        let now = Utc::now();
        let expires_at = now + Duration::hours(24);

        let claims = Claims {
            sub: user.id.unwrap().to_hex(),
            email: user.email.clone(),
            role: user.role.clone(),
            exp: expires_at.timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.config.jwt_secret.as_ref()),
        )
        .map_err(|e| ServiceError::Internal(format!("Failed to generate token: {}", e)))
    }

    #[instrument(skip(self))]
    pub fn verify_token(&self, token: &str) -> ServiceResult<Claims> {
        decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.config.jwt_secret.as_ref()),
            &Validation::default(),
        )
        .map(|data| data.claims)
        .map_err(|e| ServiceError::Authentication(format!("Invalid token: {}", e)))
    }

    #[instrument(skip(self))]
    pub async fn get_user_by_id(&self, user_id: &str) -> ServiceResult<User> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        self.users
            .find_one(doc! { "_id": object_id }, None)
            .await?
            .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))
    }
}
