fn(axum::extract::State<Arc<AppState>>, Claims, axum::Json<CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<ApplicationResponse>>, ControllerError>> {create_application}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::Json<CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<ApplicationResponse>>, ControllerError>> {create_application}: Handler<_, _>
