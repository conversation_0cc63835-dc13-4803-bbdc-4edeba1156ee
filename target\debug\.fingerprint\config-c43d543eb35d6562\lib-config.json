{"rustc": 2830703817519440116, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17255432589167795725, "path": 7056227968231688386, "deps": [[1213098572879462490, "json5_rs", false, 11595433149848061362], [1965680986145237447, "yaml_rust2", false, 548823619611381054], [2244620803250265856, "ron", false, 9286518152926469291], [6502365400774175331, "nom", false, 6669720045195607927], [6517602928339163454, "path<PERSON><PERSON>", false, 17451124000644988204], [9689903380558560274, "serde", false, 811064473310142638], [11946729385090170470, "async_trait", false, 3254960072444411179], [13475460906694513802, "convert_case", false, 7766097299803874337], [14618892375165583068, "ini", false, 17010521212278427731], [15367738274754116744, "serde_json", false, 15573914083252631258], [15609422047640926750, "toml", false, 14832818807182435270]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-c43d543eb35d6562\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}