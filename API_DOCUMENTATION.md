# Achidas Platform API Documentation

## Overview
Achidas is a Render-like cloud platform service leveraging African infrastructure. This document provides comprehensive API documentation for new users.

## Base URL
```
https://api.achidas.com/api/v1
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Response Format
All API responses follow this consistent format:
```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "timestamp": "2025-06-19T11:11:06.315137800+00:00",
    "request_id": "e48cdaa8-4689-4ec2-ae05-3a174d524841",
    "trace_id": "9665c083-568d-4310-83db-136c605d46a7",
    "version": "v1",
    "status_code": 200
  }
}
```

## Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "password is required",
    "details": { ... }
  },
  "meta": { ... }
}
```

---

## 🔐 Authentication Endpoints

### Register User
```http
POST /auth/register
```
**Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### Login
```http
POST /auth/login
```
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### Refresh Token
```http
POST /auth/refresh
```
**Body:**
```json
{
  "refresh_token": "your_refresh_token"
}
```

---

## 👤 User Management

### Get User Profile
```http
GET /users/profile
```
**Headers:** `Authorization: Bearer <token>`

---

## 🌍 African Regions & Countries

### List All African Regions
```http
GET /african-regions
```
**Query Parameters:**
- `available_only` (boolean): Filter to only available regions
- `country` (string): Filter by country code (e.g., "NG", "ZA")

### Get Specific Region
```http
GET /african-regions/{region_code}
```
**Example:** `/african-regions/ng-lag` (Lagos, Nigeria)

### Get Region Pricing
```http
GET /african-regions/{region_code}/pricing
```

### List All African Countries
```http
GET /countries
```

### Get Country Regions
```http
GET /countries/{country_code}/regions
```
**Example:** `/countries/NG/regions` (All regions in Nigeria)

---

## 🚀 Application Management

### List Applications
```http
GET /applications
```
**Query Parameters:**
- `page` (number): Page number (default: 1)
- `per_page` (number): Items per page (default: 20)

### Create Application
```http
POST /applications
```
**Body:**
```json
{
  "name": "my-web-app",
  "description": "My awesome web application",
  "repository": {
    "url": "https://github.com/user/repo.git",
    "branch": "main"
  },
  "service_type": "web_service",
  "region": "ng-lag",
  "runtime_config": {
    "build_command": "npm run build",
    "start_command": "npm start",
    "environment": "node"
  }
}
```

### Get Application
```http
GET /applications/{app_id}
```

### Update Application
```http
PUT /applications/{app_id}
```

### Delete Application
```http
DELETE /applications/{app_id}
```

### Deploy Application
```http
POST /applications/{app_id}/deploy
```

### List Deployments
```http
GET /applications/{app_id}/deployments
```

### Get Deployment Details
```http
GET /applications/{app_id}/deployments/{deployment_id}
```

---

## 🔧 Environment Variables

### List Environment Variables
```http
GET /applications/{app_id}/environment
```

### Create Environment Variable
```http
POST /applications/{app_id}/environment
```
**Body:**
```json
{
  "key": "DATABASE_URL",
  "value": "mongodb://localhost:27017/mydb",
  "is_secret": true
}
```

### Update Environment Variable
```http
PUT /applications/{app_id}/environment/{env_id}
```

### Delete Environment Variable
```http
DELETE /applications/{app_id}/environment/{env_id}
```

---

## 📊 Logs & Monitoring

### Get Application Logs
```http
GET /applications/{app_id}/logs
```
**Query Parameters:**
- `lines` (number): Number of log lines (default: 100)
- `follow` (boolean): Stream logs in real-time
- `since` (string): ISO timestamp to get logs since

### Get Build Logs
```http
GET /applications/{app_id}/deployments/{deployment_id}/logs
```

### Get Runtime Logs
```http
GET /applications/{app_id}/logs/runtime
```

---

## 🔗 Webhooks

### Create Webhook
```http
POST /applications/{app_id}/webhooks
```
**Body:**
```json
{
  "url": "https://your-app.com/webhook",
  "events": ["deployment.success", "deployment.failed"],
  "secret": "webhook_secret"
}
```

### List Webhooks
```http
GET /applications/{app_id}/webhooks
```

### Update Webhook
```http
PUT /applications/{app_id}/webhooks/{webhook_id}
```

### Delete Webhook
```http
DELETE /applications/{app_id}/webhooks/{webhook_id}
```

---

## 💰 Billing & Account

### Get Account Information
```http
GET /account
```

### Get Account Usage
```http
GET /account/usage
```

### Get Billing Overview
```http
GET /billing/overview
```

### Get Invoices
```http
GET /billing/invoices
```

### Get Usage Details
```http
GET /billing/usage
```

---

## 🏗️ Infrastructure

### List Available Plans
```http
GET /plans
```

### List Operating Systems
```http
GET /operating-systems
```

### List SSH Keys
```http
GET /ssh-keys
```

### Create SSH Key
```http
POST /ssh-keys
```
**Body:**
```json
{
  "name": "my-ssh-key",
  "ssh_key": "ssh-rsa AAAAB3NzaC1yc2E..."
}
```

---

## 🎯 Intelligent Hosting

### Get Hosting Plans
```http
GET /hosting/plans
```

### Get African Pricing
```http
GET /hosting/pricing/{country_code}
```
**Example:** `/hosting/pricing/NG` (Nigeria pricing)

### Get Infrastructure Status
```http
GET /hosting/infrastructure/status
```

### Get Scaling Recommendations
```http
GET /hosting/scaling/recommendations
```

---

## 🔍 Health & Status

### Health Check
```http
GET /health
```

### API Health Check
```http
GET /api/v1/health
```

---

## 📋 Admin Endpoints (Admin Access Required)

### List All Users
```http
GET /admin/users
```

### Get User Details
```http
GET /admin/users/{user_id}
```

### Suspend User
```http
POST /admin/users/{user_id}/suspend
```

### Activate User
```http
POST /admin/users/{user_id}/activate
```

### Get System Metrics
```http
GET /admin/metrics
```

---

## 🌐 Available African Regions

| Region Code | Country | City | Status |
|-------------|---------|------|--------|
| `za-jhb` | South Africa | Johannesburg | ✅ Available |
| `za-cpt` | South Africa | Cape Town | ✅ Available |
| `ng-lag` | Nigeria | Lagos | ✅ Available |
| `ng-abj` | Nigeria | Abuja | ✅ Available |
| `ke-nai` | Kenya | Nairobi | ✅ Available |
| `eg-cai` | Egypt | Cairo | ✅ Available |
| `gh-acc` | Ghana | Accra | ✅ Available |
| `ma-cas` | Morocco | Casablanca | ✅ Available |
| `et-add` | Ethiopia | Addis Ababa | 🚧 Coming Soon |
| `tz-dar` | Tanzania | Dar es Salaam | 🚧 Coming Soon |
| `ug-kam` | Uganda | Kampala | 🚧 Coming Soon |
| `rw-kig` | Rwanda | Kigali | 🚧 Coming Soon |

---

## 💡 Getting Started

1. **Register an account** using `/auth/register`
2. **Login** to get your JWT token using `/auth/login`
3. **Choose a region** from `/african-regions` or `/countries`
4. **Create an application** using `/applications`
5. **Deploy your app** using `/applications/{app_id}/deploy`
6. **Monitor logs** using `/applications/{app_id}/logs`

## 📞 Support

For support, contact <NAME_EMAIL> or visit our documentation at https://docs.achidas.com
