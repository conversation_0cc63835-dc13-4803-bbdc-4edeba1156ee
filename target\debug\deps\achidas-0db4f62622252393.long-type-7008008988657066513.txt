fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, axum::<PERSON><PERSON><{type error}>) -> impl futures::Future<Output = Result<axum::J<PERSON><ApiResponse<{type error}>>, ControllerError>> {update_application}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<{type error}>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<{type error}>>, ControllerError>> {update_application}: Handler<_, _>
