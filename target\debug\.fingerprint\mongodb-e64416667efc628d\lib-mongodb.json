{"rustc": 2830703817519440116, "features": "[\"default\", \"serde_bytes\", \"tokio-runtime\"]", "declared_features": "[\"async-executor\", \"async-std\", \"async-std-resolver\", \"async-std-runtime\", \"aws-auth\", \"azure-kms\", \"bson-chrono-0_4\", \"bson-serde_with\", \"bson-uuid-0_8\", \"bson-uuid-1\", \"default\", \"flate2\", \"gcp-kms\", \"in-use-encryption-unstable\", \"log\", \"mongocrypt\", \"num_cpus\", \"openssl\", \"openssl-probe\", \"openssl-tls\", \"rayon\", \"reqwest\", \"serde_bytes\", \"snap\", \"snappy-compression\", \"sync\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-sync\", \"tracing\", \"tracing-unstable\", \"zlib-compression\", \"zstd\", \"zstd-compression\"]", "target": 6467422678098242930, "profile": 2241668132362809309, "path": 15581766931217037629, "deps": [[5103565458935487, "futures_io", false, 14764076604848365451], [40386456601120721, "percent_encoding", false, 82287743702150174], [501918078635137462, "take_mut", false, 3635984706278504594], [530211389790465181, "hex", false, 10608173942580388424], [1211321333142909612, "socket2", false, 11139604356829817397], [1288403060204016458, "tokio_util", false, 4901245422426812616], [1469478705076669426, "trust_dns_proto", false, 11291621119565273805], [1526817731016152233, "stringprep", false, 14212486675781149156], [2283771217451780507, "serde_with", false, 10458843671230032411], [4258399515347749257, "pbkdf2", false, 533423600792967110], [5841926810058920975, "strsim", false, 16314123778326648589], [7051825882133757896, "md5", false, 13336617066881363991], [7620660491849607393, "futures_core", false, 9910657298933926885], [7845877979469732493, "trust_dns_resolver", false, 4868568841329169676], [8008191657135824715, "thiserror", false, 8779817158858857571], [8319709847752024821, "uuid", false, 11503164101202172598], [9209347893430674936, "hmac", false, 8523907191759568034], [9504753771229857410, "derive_more", false, 9435938209292261815], [9538054652646069845, "tokio", false, 4325870631136706026], [9689903380558560274, "serde", false, 811064473310142638], [9751220522286876350, "rustc_version_runtime", false, 2304585059443952912], [9767387392190177945, "typed_builder", false, 2421315702730453325], [9857275760291862238, "sha2", false, 1073351566402878198], [9897246384292347999, "chrono", false, 8667043826432975880], [10435729446543529114, "bitflags", false, 4105023545811055325], [10629569228670356391, "futures_util", false, 10549570323539430975], [10889494155287625682, "serde_bytes", false, 12169384175096482588], [11295624341523567602, "rustls", false, 313071644363730403], [11946729385090170470, "async_trait", false, 3254960072444411179], [12779779637805422465, "futures_executor", false, 6179543211601776828], [13208667028893622512, "rand", false, 2060867666563702624], [13859769749131231458, "derivative", false, 8040178850294843001], [13886384384316890372, "bson", false, 2850668819228118417], [16311359161338405624, "rustls_pemfile", false, 16790249805810568630], [16622232390123975175, "tokio_rustls", false, 1671175774734322582], [17282734725213053079, "base64", false, 14434946832198905029], [17348166850176847349, "sha1", false, 14375343059146978741], [17652733826348741533, "webpki_roots", false, 14222400127770909713], [17917672826516349275, "lazy_static", false, 3577861647645141321]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\mongodb-e64416667efc628d\\dep-lib-mongodb", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}