use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Database {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub name: String,
    pub description: Option<String>,
    pub database_type: DatabaseType,
    pub plan: String,
    pub region: String,
    pub version: String,
    pub status: DatabaseStatus,
    pub connection_info: DatabaseConnectionInfo,
    pub configuration: DatabaseConfiguration,
    pub backup_config: BackupConfiguration,
    pub monitoring: DatabaseMonitoring,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub last_backup_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum DatabaseType {
    PostgreSQL,
    MySQL,
    MongoDB,
    Redis,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum DatabaseStatus {
    Creating,
    Running,
    Stopped,
    Restarting,
    Updating,
    BackingUp,
    Restoring,
    Error,
    Deleting,
    Deleted,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConnectionInfo {
    pub host: String,
    pub port: u16,
    pub database_name: String,
    pub username: String,
    #[serde(skip_serializing)]
    pub password: String,
    pub ssl_enabled: bool,
    pub connection_string: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfiguration {
    pub max_connections: u32,
    pub memory_mb: u32,
    pub storage_gb: u32,
    pub cpu_cores: f32,
    pub auto_scaling_enabled: bool,
    pub maintenance_window: MaintenanceWindow,
    pub parameters: std::collections::HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceWindow {
    pub day_of_week: String, // e.g., "sunday"
    pub start_time: String,  // e.g., "02:00"
    pub duration_hours: u8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackupConfiguration {
    pub enabled: bool,
    pub retention_days: u32,
    pub schedule: String, // cron format
    pub backup_window: String,
    pub point_in_time_recovery: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseMonitoring {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub storage_usage: f64,
    pub connection_count: u32,
    pub queries_per_second: f64,
    pub last_updated: DateTime<Utc>,
}

// Request/Response models
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateDatabaseRequest {
    #[validate(length(min = 1, max = 50))]
    pub name: String,
    pub description: Option<String>,
    pub database_type: DatabaseType,
    #[validate(length(min = 1))]
    pub plan: String,
    #[validate(length(min = 1))]
    pub region: String,
    pub version: Option<String>,
    pub initial_database_name: Option<String>,
    pub backup_enabled: Option<bool>,
    pub backup_retention_days: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct UpdateDatabaseRequest {
    pub description: Option<String>,
    pub plan: Option<String>,
    pub backup_enabled: Option<bool>,
    pub backup_retention_days: Option<u32>,
    pub maintenance_window: Option<MaintenanceWindow>,
    pub auto_scaling_enabled: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub database_type: DatabaseType,
    pub plan: String,
    pub region: String,
    pub version: String,
    pub status: DatabaseStatus,
    pub connection_info: DatabaseConnectionInfo,
    pub configuration: DatabaseConfiguration,
    pub backup_config: BackupConfiguration,
    pub monitoring: DatabaseMonitoring,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub last_backup_at: Option<DateTime<Utc>>,
}

impl From<Database> for DatabaseResponse {
    fn from(db: Database) -> Self {
        Self {
            id: db.id.unwrap_or_default().to_hex(),
            name: db.name,
            description: db.description,
            database_type: db.database_type,
            plan: db.plan,
            region: db.region,
            version: db.version,
            status: db.status,
            connection_info: db.connection_info,
            configuration: db.configuration,
            backup_config: db.backup_config,
            monitoring: db.monitoring,
            created_at: db.created_at,
            updated_at: db.updated_at,
            last_backup_at: db.last_backup_at,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseMetrics {
    pub cpu_usage_percent: f64,
    pub memory_usage_percent: f64,
    pub storage_usage_percent: f64,
    pub active_connections: u32,
    pub queries_per_second: f64,
    pub slow_queries_count: u64,
    pub replication_lag_ms: Option<u64>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseBackup {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub database_id: ObjectId,
    pub backup_type: BackupType,
    pub status: BackupStatus,
    pub size_bytes: u64,
    pub created_at: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub completed_at: Option<DateTime<Utc>>,
    pub expires_at: DateTime<Utc>,
    pub download_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum BackupType {
    Automatic,
    Manual,
    PointInTime,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum BackupStatus {
    InProgress,
    Completed,
    Failed,
    Expired,
}
