openapi: 3.0.3
info:
  title: Achidas Cloud Platform API
  description: |
    Comprehensive API for the Achidas Cloud Platform - A Render-like cloud service leveraging Vultr infrastructure.
    
    ## API Organization
    The API is organized into three main categories:
    
    ### 🌍 Public APIs (`/api/v1/public/`)
    - **No authentication required**
    - Public information, webhooks, authentication
    - Available to everyone
    
    ### 👤 User APIs (`/api/v1/users/`)
    - **Authentication required**
    - User-specific operations and services
    - Platform services, billing, hosting management
    
    ### 🔧 Admin APIs (`/api/v1/admin/`)
    - **Admin authentication required**
    - Administrative operations and Vultr infrastructure management
    - System management, user administration, direct Vultr API access
    
    ## Authentication
    Protected endpoints require JWT authentication. Include the token in the Authorization header:
    `Authorization: Bearer <your_jwt_token>`
    
    ## Response Format
    All responses follow a consistent format:
    ```json
    {
      "success": true,
      "data": {...},
      "message": "Optional message",
      "error": "Optional error message",
      "metadata": {
        "timestamp": "2024-01-01T00:00:00Z",
        "request_id": "uuid",
        "version": "v1"
      }
    }
    ```
  version: 1.0.0
  contact:
    name: Achidas API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: Local development server
  - url: https://api.achidas.com
    description: Production server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
        message:
          type: string
        error:
          type: string
        metadata:
          $ref: '#/components/schemas/ResponseMetadata'
      required:
        - success
        - metadata

    ResponseMetadata:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        request_id:
          type: string
          format: uuid
        version:
          type: string
          example: "v1"
        pagination:
          $ref: '#/components/schemas/PaginationMeta'

    PaginationMeta:
      type: object
      properties:
        page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        total_pages:
          type: integer

    # Authentication Models
    RegisterRequest:
      type: object
      properties:
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          minLength: 8
          example: "securepassword123"
      required:
        - name
        - email
        - password

    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          example: "securepassword123"
      required:
        - email
        - password

    LoginResponse:
      type: object
      properties:
        token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        user:
          $ref: '#/components/schemas/UserProfile'
        expires_at:
          type: string
          format: date-time

    UserProfile:
      type: object
      properties:
        id:
          type: string
          example: "507f1f77bcf86cd799439011"
        name:
          type: string
        email:
          type: string
          format: email
        role:
          type: string
          enum: [Admin, User]
        status:
          type: string
          enum: [Active, Inactive, Suspended]
        created_at:
          type: string
          format: date-time
        last_login:
          type: string
          format: date-time

    # African Region Models
    AfricanRegion:
      type: object
      properties:
        code:
          type: string
          example: "za-jhb"
        name:
          type: string
          example: "Johannesburg"
        country:
          type: string
          example: "South Africa"
        country_code:
          type: string
          example: "ZA"
        status:
          type: string
          enum: [available, coming_soon, maintenance]
        pricing_tier:
          type: string
          enum: [economy, standard, premium]
        vultr_region:
          type: string
          example: "fra"
        description:
          type: string

    RegionPricing:
      type: object
      properties:
        region_code:
          type: string
        currency:
          type: string
          example: "USD"
        plans:
          type: array
          items:
            $ref: '#/components/schemas/PricingPlan'

    PricingPlan:
      type: object
      properties:
        name:
          type: string
          example: "starter"
        display_name:
          type: string
          example: "Starter Plan"
        cpu:
          type: string
          example: "0.5 vCPU"
        memory:
          type: string
          example: "512 MB"
        storage:
          type: string
          example: "10 GB"
        bandwidth:
          type: string
          example: "100 GB"
        price_usd:
          type: number
          example: 5.00
        price_local:
          type: string
          example: "R75.00"

    # Application Models
    CreateApplicationRequest:
      type: object
      properties:
        name:
          type: string
          example: "my-web-app"
        description:
          type: string
          example: "My awesome web application"
        repository:
          type: object
          properties:
            url:
              type: string
              format: uri
              example: "https://github.com/user/repo.git"
            branch:
              type: string
              example: "main"
        service_type:
          type: string
          enum: [web_service, static_site, background_worker, cron_job, private_service]
        region:
          type: string
          example: "ng-lag"
        runtime_config:
          type: object
          properties:
            build_command:
              type: string
              example: "npm run build"
            start_command:
              type: string
              example: "npm start"
            environment:
              type: string
              example: "node"
      required:
        - name
        - repository
        - service_type
        - region

    ApplicationResponse:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [Active, Inactive, Building, Deploying, Failed]
        repository:
          type: object
        service_type:
          type: string
        region:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

paths:
  # Legacy Health Check (for backward compatibility)
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check if the API is running
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/health:
    get:
      tags:
        - Health
      summary: Versioned health check
      description: Check if the API is running (versioned endpoint)
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # 🌍 PUBLIC APIs - No Authentication Required

  # Authentication
  /api/v1/public/auth/register:
    post:
      tags:
        - Public - Authentication
      summary: Register a new user
      description: Create a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserProfile'
        '400':
          description: Validation error
        '409':
          description: User already exists

  /api/v1/public/auth/login:
    post:
      tags:
        - Public - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
        '400':
          description: Validation error

  /api/v1/public/auth/refresh:
    post:
      tags:
        - Public - Authentication
      summary: Refresh JWT token
      description: Refresh an expired JWT token
      security: []
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid refresh token

  # Public Health Check
  /api/v1/public/health:
    get:
      tags:
        - Public - Health
      summary: Public health check
      description: Check if the API is running (public endpoint)
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # Webhooks (Public)
  /api/v1/public/webhooks/github:
    post:
      tags:
        - Public - Webhooks
      summary: GitHub webhook
      description: Handle GitHub webhook events
      security: []
      responses:
        '200':
          description: Webhook processed successfully

  /api/v1/public/webhooks/gitlab:
    post:
      tags:
        - Public - Webhooks
      summary: GitLab webhook
      description: Handle GitLab webhook events
      security: []
      responses:
        '200':
          description: Webhook processed successfully

  /api/v1/public/webhooks/git/{app_id}:
    post:
      tags:
        - Public - Webhooks
      summary: Application webhook
      description: Handle application-specific webhook events
      security: []
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Webhook processed successfully

  /api/v1/public/webhooks:
    post:
      tags:
        - Public - Webhooks
      summary: Generic webhook
      description: Handle generic webhook events
      security: []
      responses:
        '200':
          description: Webhook processed successfully

  /api/v1/public/webhooks/health:
    get:
      tags:
        - Public - Webhooks
      summary: Webhook health check
      description: Check webhook service health
      security: []
      responses:
        '200':
          description: Webhook service is healthy

  # African Regions (Public Information)
  /api/v1/public/african-regions:
    get:
      tags:
        - Public - African Regions
      summary: List African regions
      description: Get all available African regions
      security: []
      parameters:
        - name: available_only
          in: query
          schema:
            type: boolean
          description: Filter to only available regions
        - name: country
          in: query
          schema:
            type: string
          description: Filter by country code (e.g., "NG", "ZA")
      responses:
        '200':
          description: List of African regions
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/AfricanRegion'

  /api/v1/public/african-regions/{region_code}:
    get:
      tags:
        - Public - African Regions
      summary: Get specific region
      description: Get details for a specific African region
      security: []
      parameters:
        - name: region_code
          in: path
          required: true
          schema:
            type: string
          example: "ng-lag"
      responses:
        '200':
          description: Region details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/AfricanRegion'
        '404':
          description: Region not found

  /api/v1/public/african-regions/{region_code}/pricing:
    get:
      tags:
        - Public - African Regions
      summary: Get region pricing
      description: Get pricing information for a specific region
      security: []
      parameters:
        - name: region_code
          in: path
          required: true
          schema:
            type: string
          example: "ng-lag"
      responses:
        '200':
          description: Region pricing information
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/RegionPricing'

  /api/v1/public/countries:
    get:
      tags:
        - Public - African Regions
      summary: List African countries
      description: Get all African countries with hosting services
      security: []
      responses:
        '200':
          description: List of African countries
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            code:
                              type: string
                              example: "NG"
                            name:
                              type: string
                              example: "Nigeria"
                            regions_count:
                              type: integer
                              example: 2

  /api/v1/public/countries/{country_code}/regions:
    get:
      tags:
        - Public - African Regions
      summary: Get country regions
      description: Get all regions available in a specific country
      security: []
      parameters:
        - name: country_code
          in: path
          required: true
          schema:
            type: string
          example: "NG"
      responses:
        '200':
          description: Regions in the country
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/AfricanRegion'

  # Hosting Plans (Public Information)
  /api/v1/public/hosting/plans:
    get:
      tags:
        - Public - Hosting Plans
      summary: List hosting plans
      description: Get all available hosting plans
      security: []
      responses:
        '200':
          description: List of hosting plans
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/PricingPlan'

  /api/v1/public/hosting/plans/{plan_name}:
    get:
      tags:
        - Public - Hosting Plans
      summary: Get specific plan
      description: Get details for a specific hosting plan
      security: []
      parameters:
        - name: plan_name
          in: path
          required: true
          schema:
            type: string
          example: "starter"
      responses:
        '200':
          description: Plan details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PricingPlan'

  /api/v1/public/hosting/pricing/{country_code}:
    get:
      tags:
        - Public - Hosting Plans
      summary: Get African pricing
      description: Get localized pricing for a specific African country
      security: []
      parameters:
        - name: country_code
          in: path
          required: true
          schema:
            type: string
          example: "NG"
      responses:
        '200':
          description: Localized pricing information
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/RegionPricing'

  /api/v1/public/hosting/health:
    get:
      tags:
        - Public - Hosting Plans
      summary: Hosting health check
      description: Check hosting service health
      security: []
      responses:
        '200':
          description: Hosting service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # 👤 USER APIs - Authentication Required

  # User Profile
  /api/v1/users/profile:
    get:
      tags:
        - User - Profile
      summary: Get user profile
      description: Get the current user's profile information
      responses:
        '200':
          description: User profile
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserProfile'
        '401':
          description: Unauthorized

  # Application Management
  /api/v1/users/applications:
    get:
      tags:
        - User - Applications
      summary: List applications
      description: Get all applications for the current user
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: List of applications
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/ApplicationResponse'
    post:
      tags:
        - User - Applications
      summary: Create application
      description: Create a new application
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApplicationRequest'
      responses:
        '201':
          description: Application created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ApplicationResponse'
        '400':
          description: Validation error

  /api/v1/users/applications/{app_id}:
    get:
      tags:
        - User - Applications
      summary: Get application
      description: Get details for a specific application
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Application details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ApplicationResponse'
        '404':
          description: Application not found
    put:
      tags:
        - User - Applications
      summary: Update application
      description: Update an existing application
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApplicationRequest'
      responses:
        '200':
          description: Application updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ApplicationResponse'
        '404':
          description: Application not found
    delete:
      tags:
        - User - Applications
      summary: Delete application
      description: Delete an application
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Application deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Application not found

  /api/v1/users/applications/{app_id}/deploy:
    post:
      tags:
        - User - Applications
      summary: Deploy application
      description: Trigger a deployment for the application
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Deployment triggered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Application not found

  # Deployments
  /api/v1/users/deployments:
    get:
      tags:
        - User - Deployments
      summary: List deployments
      description: Get all deployments for the current user
      parameters:
        - name: app_id
          in: query
          schema:
            type: string
          description: Filter by application ID
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, building, deploying, success, failed]
          description: Filter by deployment status
      responses:
        '200':
          description: List of deployments
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/users/deployments/{deployment_id}:
    get:
      tags:
        - User - Deployments
      summary: Get deployment
      description: Get details for a specific deployment
      parameters:
        - name: deployment_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Deployment details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Deployment not found

  /api/v1/users/deployments/{deployment_id}/logs:
    get:
      tags:
        - User - Deployments
      summary: Get deployment logs
      description: Get logs for a specific deployment
      parameters:
        - name: deployment_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Deployment logs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # Logs
  /api/v1/users/logs/{app_id}:
    get:
      tags:
        - User - Logs
      summary: Get application logs
      description: Get logs for a specific application
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
        - name: lines
          in: query
          schema:
            type: integer
            default: 100
          description: Number of log lines to retrieve
        - name: follow
          in: query
          schema:
            type: boolean
            default: false
          description: Follow logs in real-time
      responses:
        '200':
          description: Application logs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/users/logs/{app_id}/stream:
    get:
      tags:
        - User - Logs
      summary: Stream application logs
      description: Stream logs for a specific application in real-time
      parameters:
        - name: app_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Log stream
          content:
            text/event-stream:
              schema:
                type: string

  # Billing
  /api/v1/users/billing/usage:
    get:
      tags:
        - User - Billing
      summary: Get usage information
      description: Get current usage and billing information
      responses:
        '200':
          description: Usage information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/users/billing/invoices:
    get:
      tags:
        - User - Billing
      summary: List invoices
      description: Get all invoices for the current user
      responses:
        '200':
          description: List of invoices
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/users/billing/pending-charges:
    get:
      tags:
        - User - Billing
      summary: Get pending charges
      description: Get current pending charges
      responses:
        '200':
          description: Pending charges
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # Intelligent Hosting
  /api/v1/users/hosting/status:
    get:
      tags:
        - User - Intelligent Hosting
      summary: Get hosting status
      description: Get current hosting status and resource allocation
      responses:
        '200':
          description: Hosting status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v1/users/hosting/scale:
    post:
      tags:
        - User - Intelligent Hosting
      summary: Scale resources
      description: Request resource scaling for applications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                app_id:
                  type: string
                scale_type:
                  type: string
                  enum: [up, down, auto]
                target_instances:
                  type: integer
      responses:
        '200':
          description: Scaling request submitted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # 🔧 ADMIN APIs - Admin Authentication Required

  # User Management
  /api/v1/admin/users:
    get:
      tags:
        - Admin - User Management
      summary: List all users
      description: Get all users in the system (admin only)
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [Active, Inactive, Suspended]
          description: Filter by user status
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/UserProfile'
        '403':
          description: Admin access required

  /api/v1/admin/users/{user_id}:
    get:
      tags:
        - Admin - User Management
      summary: Get user details
      description: Get details for a specific user (admin only)
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserProfile'
        '404':
          description: User not found
        '403':
          description: Admin access required
    put:
      tags:
        - Admin - User Management
      summary: Update user
      description: Update user information (admin only)
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [Active, Inactive, Suspended]
                role:
                  type: string
                  enum: [Admin, User]
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserProfile'
        '404':
          description: User not found
        '403':
          description: Admin access required

  # System Management
  /api/v1/admin/system/health:
    get:
      tags:
        - Admin - System Management
      summary: System health check
      description: Get detailed system health information (admin only)
      responses:
        '200':
          description: System health information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/system/metrics:
    get:
      tags:
        - Admin - System Management
      summary: System metrics
      description: Get system performance metrics (admin only)
      responses:
        '200':
          description: System metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  # Vultr Infrastructure Management
  /api/v1/admin/vultr/instances:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List Vultr instances
      description: Get all Vultr instances (admin only)
      responses:
        '200':
          description: List of Vultr instances
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required
    post:
      tags:
        - Admin - Vultr Infrastructure
      summary: Create Vultr instance
      description: Create a new Vultr instance (admin only)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                region:
                  type: string
                plan:
                  type: string
                os_id:
                  type: integer
                label:
                  type: string
      responses:
        '201':
          description: Instance created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/instances/{instance_id}:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: Get Vultr instance
      description: Get details for a specific Vultr instance (admin only)
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Instance not found
        '403':
          description: Admin access required
    delete:
      tags:
        - Admin - Vultr Infrastructure
      summary: Delete Vultr instance
      description: Delete a Vultr instance (admin only)
      parameters:
        - name: instance_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Instance deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Instance not found
        '403':
          description: Admin access required

  /api/v1/admin/vultr/regions:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List Vultr regions
      description: Get all available Vultr regions (admin only)
      responses:
        '200':
          description: List of Vultr regions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/plans:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List Vultr plans
      description: Get all available Vultr plans (admin only)
      responses:
        '200':
          description: List of Vultr plans
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/os:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List operating systems
      description: Get all available operating systems (admin only)
      responses:
        '200':
          description: List of operating systems
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/ssh-keys:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List SSH keys
      description: Get all SSH keys (admin only)
      responses:
        '200':
          description: List of SSH keys
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required
    post:
      tags:
        - Admin - Vultr Infrastructure
      summary: Create SSH key
      description: Create a new SSH key (admin only)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                ssh_key:
                  type: string
      responses:
        '201':
          description: SSH key created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/block-storage:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List block storage
      description: Get all block storage volumes (admin only)
      responses:
        '200':
          description: List of block storage volumes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required
    post:
      tags:
        - Admin - Vultr Infrastructure
      summary: Create block storage
      description: Create a new block storage volume (admin only)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                region:
                  type: string
                size_gb:
                  type: integer
                label:
                  type: string
      responses:
        '201':
          description: Block storage created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/load-balancers:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List load balancers
      description: Get all load balancers (admin only)
      responses:
        '200':
          description: List of load balancers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/databases:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List managed databases
      description: Get all managed databases (admin only)
      responses:
        '200':
          description: List of managed databases
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/kubernetes:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: List Kubernetes clusters
      description: Get all Kubernetes clusters (admin only)
      responses:
        '200':
          description: List of Kubernetes clusters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

  /api/v1/admin/vultr/billing:
    get:
      tags:
        - Admin - Vultr Infrastructure
      summary: Get Vultr billing
      description: Get Vultr billing information (admin only)
      responses:
        '200':
          description: Vultr billing information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Admin access required

tags:
  - name: Health
    description: Health check endpoints
  - name: Public - Authentication
    description: Public authentication endpoints
  - name: Public - Health
    description: Public health check endpoints
  - name: Public - Webhooks
    description: Public webhook endpoints
  - name: Public - African Regions
    description: Public African regions information
  - name: Public - Hosting Plans
    description: Public hosting plans and pricing
  - name: User - Profile
    description: User profile management
  - name: User - Applications
    description: User application management
  - name: User - Deployments
    description: User deployment management
  - name: User - Logs
    description: User application logs
  - name: User - Billing
    description: User billing and usage
  - name: User - Intelligent Hosting
    description: User intelligent hosting management
  - name: Admin - User Management
    description: Admin user management
  - name: Admin - System Management
    description: Admin system management
  - name: Admin - Vultr Infrastructure
    description: Admin Vultr infrastructure management
