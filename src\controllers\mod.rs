pub mod admin;
pub mod auth;
pub mod billing;
pub mod file_upload;
pub mod health;
pub mod hosting_plans;
pub mod instances;
pub mod intelligent_hosting;
pub mod users;
pub mod vultr;
pub mod applications;
pub mod environment;
pub mod logs;
pub mod webhooks;
pub mod regions;

use crate::{models::{ApiResponse, ApiResponseMeta}, services};
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;
use tracing::error;

#[derive(Error, Debug)]
pub enum ControllerError {
    #[error("Database error: {0}")]
    Database(String),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Authorization error: {0}")]
    Authorization(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("External API error: {0}")]
    ExternalApi(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Internal server error: {0}")]
    InternalServerError(String),
}

impl IntoResponse for ControllerError {
    fn into_response(self) -> Response {
        let (status, error_message) = match &self {
            ControllerError::Database(msg) => {
                error!("Database error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, msg.as_str())
            }
            ControllerError::Validation(msg) => (StatusCode::BAD_REQUEST, msg.as_str()),
            ControllerError::Authentication(msg) => (StatusCode::UNAUTHORIZED, msg.as_str()),
            ControllerError::Authorization(msg) => (StatusCode::FORBIDDEN, msg.as_str()),
            ControllerError::NotFound(msg) => (StatusCode::NOT_FOUND, msg.as_str()),
            ControllerError::Conflict(msg) => (StatusCode::CONFLICT, msg.as_str()),
            ControllerError::ExternalApi(msg) => {
                error!("External API error: {}", msg);
                (StatusCode::BAD_GATEWAY, msg.as_str())
            }
            ControllerError::Internal(msg) => {
                error!("Internal error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, msg.as_str())
            }
            ControllerError::Unauthorized(msg) => (StatusCode::UNAUTHORIZED, msg.as_str()),
            ControllerError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg.as_str()),
            ControllerError::InternalServerError(msg) => {
                error!("Internal server error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, msg.as_str())
            }
        };

        let meta = ApiResponseMeta {
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: uuid::Uuid::new_v4().to_string(),
            trace_id: uuid::Uuid::new_v4().to_string(),
            version: "v1".to_string(),
            status_code: status.as_u16(),
            path: None,
            method: None,
        };

        let body = Json(ApiResponse::<()>::error_with_meta(error_message.to_string(), meta));
        (status, body).into_response()
    }
}

pub type ControllerResult<T> = Result<T, ControllerError>;

// Helper function to create success responses
pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    let meta = ApiResponseMeta {
        timestamp: chrono::Utc::now().to_rfc3339(),
        request_id: uuid::Uuid::new_v4().to_string(),
        trace_id: uuid::Uuid::new_v4().to_string(),
        version: "v1".to_string(),
        status_code: 200,
        path: None,
        method: None,
    };
    Json(ApiResponse::success_with_meta(data, meta))
}

// Helper function to create message responses
pub fn message_response(message: String) -> Json<ApiResponse<()>> {
    let meta = ApiResponseMeta {
        timestamp: chrono::Utc::now().to_rfc3339(),
        request_id: uuid::Uuid::new_v4().to_string(),
        trace_id: uuid::Uuid::new_v4().to_string(),
        version: "v1".to_string(),
        status_code: 200,
        path: None,
        method: None,
    };
    Json(ApiResponse::message_with_meta(message, meta))
}

impl From<services::ServiceError> for ControllerError {
    fn from(err: services::ServiceError) -> Self {
        match err {
            services::ServiceError::Database(e) => ControllerError::Database(e.to_string()),
            services::ServiceError::Validation(msg) => ControllerError::Validation(msg),
            services::ServiceError::Authentication(msg) => ControllerError::Authentication(msg),
            services::ServiceError::Authorization(msg) => ControllerError::Authorization(msg),
            services::ServiceError::NotFound(msg) => ControllerError::NotFound(msg),
            services::ServiceError::Conflict(msg) => ControllerError::Conflict(msg),
            services::ServiceError::ExternalApi(msg) => ControllerError::ExternalApi(msg),
            services::ServiceError::Internal(msg) => ControllerError::Internal(msg),
            services::ServiceError::BadRequest(msg) => ControllerError::BadRequest(msg),
        }
    }
}

impl From<axum::http::StatusCode> for ControllerError {
    fn from(status: axum::http::StatusCode) -> Self {
        ControllerError::Unauthorized(format!("HTTP error: {}", status))
    }
}


