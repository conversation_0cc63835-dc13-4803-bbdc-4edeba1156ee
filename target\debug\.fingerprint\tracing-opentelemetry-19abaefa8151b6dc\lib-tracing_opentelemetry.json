{"rustc": 2830703817519440116, "features": "[\"default\", \"metrics\", \"smallvec\", \"tracing-log\"]", "declared_features": "[\"async-trait\", \"default\", \"metrics\", \"smallvec\", \"thiserror\", \"tracing-log\"]", "target": 12070306627020974532, "profile": 2241668132362809309, "path": 1591878164374224254, "deps": [[1337443197003760792, "opentelemetry", false, 11402114227878127659], [3424551429995674438, "tracing_core", false, 15848792717994607859], [3666196340704888985, "smallvec", false, 11749763988601564173], [3722963349756955755, "once_cell", false, 10983383885145764756], [7009435218076268184, "opentelemetry_sdk", false, 15299866634881540462], [8606274917505247608, "tracing", false, 13822962143594336692], [10806489435541507125, "tracing_log", false, 8636458597425319330], [16230660778393187092, "tracing_subscriber", false, 7881160514438426725]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-opentelemetry-19abaefa8151b6dc\\dep-lib-tracing_opentelemetry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}