use mongodb::{Database, IndexModel, options::IndexOptions};
use bson::{doc, Document};
use futures::stream::TryStreamExt;
use tracing::{info, error, warn};
use crate::services::{ServiceError, ServiceResult};
use std::time::Duration;

pub struct DatabaseIndexManager {
    database: Database,
}

impl DatabaseIndexManager {
    pub fn new(database: Database) -> Self {
        Self { database }
    }

    /// Create all indexes sequentially for production readiness
    pub async fn create_all_indexes(&self) -> ServiceResult<()> {
        info!("Starting index creation for production deployment");

        // Execute all index creation tasks sequentially to avoid type issues
        self.create_user_indexes().await?;
        self.create_application_indexes().await?;
        self.create_deployment_indexes().await?;
        self.create_build_indexes().await?;
        self.create_environment_indexes().await?;
        self.create_billing_indexes().await?;
        self.create_instance_indexes().await?;
        self.create_monitoring_indexes().await?;

        info!("All database indexes created successfully");
        Ok(())
    }

    async fn create_user_indexes(&self) -> ServiceResult<()> {
        let collection = self.database.collection::<Document>("users");

        let indexes = vec![
            // Unique email index for fast user lookup and authentication
            IndexModel::builder()
                .keys(doc! { "email": 1 })
                .options(IndexOptions::builder()
                    .unique(true)
                    .name("email_unique".to_string())
                    .background(true)
                    .build())
                .build(),
            
            // Created at index for user analytics and pagination
            IndexModel::builder()
                .keys(doc! { "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("created_at_desc".to_string())
                    .background(true)
                    .build())
                .build(),

            // Status index for filtering active users
            IndexModel::builder()
                .keys(doc! { "status": 1 })
                .options(IndexOptions::builder()
                    .name("status_idx".to_string())
                    .background(true)
                    .build())
                .build(),

            // Compound index for user search and filtering
            IndexModel::builder()
                .keys(doc! { "status": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("status_created_compound".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        self.create_indexes_with_retry("users", indexes).await
    }

    async fn create_application_indexes(&self) -> ServiceResult<()> {
        let collection = self.database.collection::<Document>("applications");

        let indexes = vec![
            // User applications lookup - most common query
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("user_apps_by_date".to_string())
                    .background(true)
                    .build())
                .build(),

            // Application status filtering
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "status": 1 })
                .options(IndexOptions::builder()
                    .name("user_apps_by_status".to_string())
                    .background(true)
                    .build())
                .build(),

            // Repository URL for webhook matching
            IndexModel::builder()
                .keys(doc! { "repository.url": 1, "repository.auto_deploy": 1 })
                .options(IndexOptions::builder()
                    .name("repo_webhook_lookup".to_string())
                    .background(true)
                    .build())
                .build(),

            // Application name search within user scope
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "name": "text" })
                .options(IndexOptions::builder()
                    .name("user_app_name_search".to_string())
                    .background(true)
                    .build())
                .build(),

            // Updated at for change tracking
            IndexModel::builder()
                .keys(doc! { "updated_at": -1 })
                .options(IndexOptions::builder()
                    .name("updated_at_desc".to_string())
                    .background(true)
                    .build())
                .build(),

            // Region-based queries for infrastructure management
            IndexModel::builder()
                .keys(doc! { "runtime_config.region": 1, "status": 1 })
                .options(IndexOptions::builder()
                    .name("region_status_idx".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        self.create_indexes_with_retry("applications", indexes).await
    }

    async fn create_deployment_indexes(&self) -> ServiceResult<()> {
        let collection = self.database.collection::<Document>("deployments");

        let indexes = vec![
            // Application deployments - primary query pattern
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("app_deployments_by_date".to_string())
                    .background(true)
                    .build())
                .build(),

            // Deployment status monitoring
            IndexModel::builder()
                .keys(doc! { "status": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("status_monitoring".to_string())
                    .background(true)
                    .build())
                .build(),

            // Active deployments tracking
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "status": 1 })
                .options(IndexOptions::builder()
                    .name("app_active_deployments".to_string())
                    .background(true)
                    .build())
                .build(),

            // Commit SHA lookup for duplicate prevention
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "commit_sha": 1 })
                .options(IndexOptions::builder()
                    .name("app_commit_lookup".to_string())
                    .background(true)
                    .build())
                .build(),

            // Branch-based deployments
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "branch": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("app_branch_deployments".to_string())
                    .background(true)
                    .build())
                .build(),

            // Deployment trigger analytics
            IndexModel::builder()
                .keys(doc! { "trigger": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("trigger_analytics".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        self.create_indexes_with_retry("deployments", indexes).await
    }

    async fn create_build_indexes(&self) -> ServiceResult<()> {
        let collection = self.database.collection::<Document>("build_jobs");

        let indexes = vec![
            // Application builds lookup
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("app_builds_by_date".to_string())
                    .background(true)
                    .build())
                .build(),

            // Build status monitoring
            IndexModel::builder()
                .keys(doc! { "status": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("build_status_monitoring".to_string())
                    .background(true)
                    .build())
                .build(),

            // Worker assignment
            IndexModel::builder()
                .keys(doc! { "worker_id": 1, "status": 1 })
                .options(IndexOptions::builder()
                    .name("worker_builds".to_string())
                    .background(true)
                    .build())
                .build(),

            // Deployment builds
            IndexModel::builder()
                .keys(doc! { "deployment_id": 1 })
                .options(IndexOptions::builder()
                    .name("deployment_builds".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        // Build queue indexes
        let queue_indexes = vec![
            IndexModel::builder()
                .keys(doc! { "priority": -1, "queued_at": 1 })
                .options(IndexOptions::builder()
                    .name("queue_priority".to_string())
                    .background(true)
                    .build())
                .build(),

            IndexModel::builder()
                .keys(doc! { "assigned_worker": 1, "assigned_at": -1 })
                .options(IndexOptions::builder()
                    .name("worker_assignments".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        // Create indexes for both collections
        let build_result = self.create_indexes_with_retry("build_jobs", indexes).await;
        let queue_result = self.create_indexes_with_retry("build_queue", queue_indexes).await;

        build_result.and(queue_result)
    }

    async fn create_environment_indexes(&self) -> ServiceResult<()> {
        let collection = self.database.collection::<Document>("environment_groups");

        let indexes = vec![
            // User environment groups
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("user_env_groups".to_string())
                    .background(true)
                    .build())
                .build(),

            // Linked applications lookup
            IndexModel::builder()
                .keys(doc! { "linked_applications": 1 })
                .options(IndexOptions::builder()
                    .name("linked_apps_lookup".to_string())
                    .background(true)
                    .build())
                .build(),

            // Environment group name search
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "name": "text" })
                .options(IndexOptions::builder()
                    .name("env_group_name_search".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        self.create_indexes_with_retry("environment_groups", indexes).await
    }

    async fn create_billing_indexes(&self) -> ServiceResult<()> {
        let indexes = vec![
            // User billing lookup
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("user_billing_by_date".to_string())
                    .background(true)
                    .build())
                .build(),

            // Invoice status
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "status": 1 })
                .options(IndexOptions::builder()
                    .name("user_invoice_status".to_string())
                    .background(true)
                    .build())
                .build(),

            // Billing period queries
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "billing_period": 1 })
                .options(IndexOptions::builder()
                    .name("user_billing_period".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        self.create_indexes_with_retry("billing", indexes).await
    }

    async fn create_instance_indexes(&self) -> ServiceResult<()> {
        let indexes = vec![
            // User instances
            IndexModel::builder()
                .keys(doc! { "user_id": 1, "created_at": -1 })
                .options(IndexOptions::builder()
                    .name("user_instances_by_date".to_string())
                    .background(true)
                    .build())
                .build(),

            // Instance status monitoring
            IndexModel::builder()
                .keys(doc! { "status": 1, "region": 1 })
                .options(IndexOptions::builder()
                    .name("instance_status_region".to_string())
                    .background(true)
                    .build())
                .build(),

            // Application instances
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "status": 1 })
                .options(IndexOptions::builder()
                    .name("app_instance_status".to_string())
                    .background(true)
                    .build())
                .build(),
        ];

        self.create_indexes_with_retry("instances", indexes).await
    }

    async fn create_monitoring_indexes(&self) -> ServiceResult<()> {
        // Metrics collection indexes
        let metrics_indexes = vec![
            IndexModel::builder()
                .keys(doc! { "timestamp": -1, "metric_name": 1 })
                .options(IndexOptions::builder()
                    .name("metrics_time_series".to_string())
                    .background(true)
                    .expire_after(Duration::from_secs(30 * 24 * 60 * 60)) // 30 days TTL
                    .build())
                .build(),

            IndexModel::builder()
                .keys(doc! { "application_id": 1, "timestamp": -1 })
                .options(IndexOptions::builder()
                    .name("app_metrics_time_series".to_string())
                    .background(true)
                    .expire_after(Duration::from_secs(30 * 24 * 60 * 60)) // 30 days TTL
                    .build())
                .build(),
        ];

        // Logs collection indexes
        let logs_indexes = vec![
            IndexModel::builder()
                .keys(doc! { "application_id": 1, "timestamp": -1 })
                .options(IndexOptions::builder()
                    .name("app_logs_time_series".to_string())
                    .background(true)
                    .expire_after(Duration::from_secs(7 * 24 * 60 * 60)) // 7 days TTL
                    .build())
                .build(),

            IndexModel::builder()
                .keys(doc! { "level": 1, "timestamp": -1 })
                .options(IndexOptions::builder()
                    .name("logs_level_time_series".to_string())
                    .background(true)
                    .expire_after(Duration::from_secs(7 * 24 * 60 * 60)) // 7 days TTL
                    .build())
                .build(),
        ];

        let metrics_result = self.create_indexes_with_retry("metrics", metrics_indexes).await;
        let logs_result = self.create_indexes_with_retry("logs", logs_indexes).await;

        metrics_result.and(logs_result)
    }

    async fn create_indexes_with_retry(&self, collection_name: &str, indexes: Vec<IndexModel>) -> ServiceResult<()> {
        let collection = self.database.collection::<Document>(collection_name);
        
        for (attempt, index) in indexes.into_iter().enumerate() {
            let default_name = format!("index_{}", attempt);
            let index_name = index.options.as_ref()
                .and_then(|opts| opts.name.as_ref())
                .map(|s| s.as_str())
                .unwrap_or(&default_name);

            let mut retries = 0;
            const MAX_RETRIES: u32 = 3;

            loop {
                match collection.create_index(index.clone(), None).await {
                    Ok(_) => {
                        info!("Created index '{}' on collection '{}'", index_name, collection_name);
                        break;
                    }
                    Err(e) => {
                        retries += 1;
                        if retries >= MAX_RETRIES {
                            error!("Failed to create index '{}' on collection '{}' after {} retries: {}", 
                                   index_name, collection_name, MAX_RETRIES, e);
                            return Err(ServiceError::Database(e));
                        }
                        
                        warn!("Failed to create index '{}' on collection '{}', retry {}/{}: {}", 
                              index_name, collection_name, retries, MAX_RETRIES, e);
                        
                        // Exponential backoff
                        tokio::time::sleep(Duration::from_millis(100 * 2_u64.pow(retries))).await;
                    }
                }
            }
        }

        Ok(())
    }

    /// Drop all indexes (for development/testing)
    pub async fn drop_all_indexes(&self) -> ServiceResult<()> {
        let collections = vec![
            "users", "applications", "deployments", "build_jobs", "build_queue",
            "environment_groups", "billing", "instances", "metrics", "logs"
        ];

        for collection_name in collections {
            let collection = self.database.collection::<Document>(collection_name);
            
            match collection.drop_indexes(None).await {
                Ok(_) => info!("Dropped all indexes for collection '{}'", collection_name),
                Err(e) => warn!("Failed to drop indexes for collection '{}': {}", collection_name, e),
            }
        }

        Ok(())
    }

    /// Get index information for monitoring
    pub async fn get_index_stats(&self) -> ServiceResult<Vec<IndexStats>> {
        let mut stats = Vec::new();
        
        let collections = vec![
            "users", "applications", "deployments", "build_jobs", "build_queue",
            "environment_groups", "billing", "instances", "metrics", "logs"
        ];

        for collection_name in collections {
            let collection = self.database.collection::<Document>(collection_name);
            
            match collection.list_indexes(None).await {
                Ok(mut cursor) => {
                    while let Some(index_doc) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
                        // For now, just create a basic index stat since we can't access IndexModel fields directly
                        stats.push(IndexStats {
                            collection: collection_name.to_string(),
                            name: format!("index_{}", stats.len()),
                            keys: doc! {},
                            unique: false,
                            background: false,
                        });
                    }
                }
                Err(e) => warn!("Failed to list indexes for collection '{}': {}", collection_name, e),
            }
        }

        Ok(stats)
    }
}

#[derive(Debug, Clone)]
pub struct IndexStats {
    pub collection: String,
    pub name: String,
    pub keys: Document,
    pub unique: bool,
    pub background: bool,
}
