use crate::{
    controllers::{success_response, ControllerR<PERSON><PERSON>},
    models::{
    },
    vultr::models::{
        Vultr<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, V<PERSON>rAccountBGP, VultrAccountBandwidth,
        VultrBlockStorage, CreateBlockStorageRequest, UpdateBlockStorageRequest,
        AttachBlockStorageRequest, DetachBlockStorageRequest, VultrInvoiceItem,
        VultrPendingCharges, VultrPullZone, CreatePullZoneRequest, UpdatePullZoneRequest,
        PurgePullZoneRequest, VultrPushZone, CreatePushZoneRequest, UpdatePushZoneRequest,
        VultrPushZoneFile, VultrContainerRegistry, CreateRegistryRequest, UpdateRegistryRequest,
        VultrRegistryReplication, CreateReplicationRequest, VultrRegistryRepository,
        UpdateRepositoryRequest, VultrDockerCredentials, VultrKubernetesDockerCredentials,
        UpdateRegistryPasswordRequest, <PERSON>ultrReg<PERSON><PERSON>Robot, UpdateRobotRequest,
        VultrRegistryArtifact, VultrRegistryRegion, VultrRegistryPlan,
        VultrDNSDomainDetailed, CreateDNSDomainRequest, UpdateDNSDomainRequest,
        VultrDNSSOA, UpdateDNSSOARequest, VultrDNSRecordDetailed, CreateDNSRecordRequest,
        UpdateDNSRecordRequest, VultrFirewallGroupDetailed, CreateFirewallGroupRequest,
        UpdateFirewallGroupRequest, VultrFirewallRuleDetailed, CreateFirewallRuleRequest,
        VultrInstanceDetailed, CreateInstanceRequest, UpdateInstanceRequest, InstanceBandwidth,
        InstanceNeighbor, InstanceVPC, InstanceISOStatus, AttachISORequest, AttachVPCRequest,
        DetachVPCRequest, CreateBackupScheduleRequest, InstanceBackupSchedule, RestoreInstanceRequest,
        InstanceIPv4, CreateInstanceIPv4Request, InstanceIPv6, CreateReverseIPv6Request,
        InstanceIPv6Reverse, CreateReverseIPv4Request, InstanceUserData, InstanceUpgrades,
        InstanceJob, BulkInstanceActionRequest, VultrISO, CreateISORequest, VultrPublicISO,
        VultrKubernetesClusterDetailed, CreateKubernetesClusterRequest, UpdateKubernetesClusterRequest,
        VultrKubernetesResources, VultrKubernetesUpgrades, StartKubernetesUpgradeRequest,
        VultrNodePoolDetailed, CreateNodePoolRequest, UpdateNodePoolRequest, VultrKubernetesConfig,
        VultrKubernetesVersions, VultrLoadBalancerDetailed, CreateLoadBalancerRequest,
        UpdateLoadBalancerRequest, CreateForwardingRuleRequest, VultrForwardingRuleDetailed,
        VultrLoadBalancerFirewallRule, VultrDatabasePlan, VultrManagedDatabase,
        CreateManagedDatabaseRequest, UpdateManagedDatabaseRequest, VultrDatabaseUsage,
        VultrDatabaseUser, CreateDatabaseUserRequest, UpdateDatabaseUserRequest,
        SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
        VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
        VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
        CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
        VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
        VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
        AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
        RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
        CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
        VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
        VultrMarketplaceAppVariable, VultrObjectStorage, CreateObjectStorageRequest,
        UpdateObjectStorageRequest, VultrObjectStorageCluster, VultrObjectStorageTier,
        VultrMetalPlan, VultrInference, CreateInferenceRequest,
        UpdateInferenceRequest, VultrInferenceUsage, VultrVPCDetailed, CreateVPCRequest,
        UpdateVPCRequest, VultrReservedIPDetailed, CreateReservedIPRequest,
        UpdateReservedIPRequest, AttachReservedIPRequest, ConvertReservedIPRequest,
        VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
        CreateSnapshotFromUrlRequest, UpdateSnapshotRequest, VultrSubaccount,
        CreateSubaccountRequest, CreateSSHKeyRequest, UpdateSSHKeyRequest,
        VultrStartupScript, CreateStartupScriptRequest, UpdateStartupScriptRequest,
        VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,
        CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,
        CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
        UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,
        AttachBareMetalVPCRequest, DetachBareMetalVPCRequest,
    },
    AppState,
};
use axum::{
    extract::{Path, State},
    Json,
};
use std::{sync::Arc, collections::HashMap};
use tracing::instrument;

// Account endpoints
#[instrument(skip(state))]
pub async fn get_account(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccount>>> {
    let account = state
        .vultr_client
        .get_account()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(account))
}

#[instrument(skip(state))]
pub async fn get_account_bgp(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccountBGP>>> {
    let bgp_info = state
        .vultr_client
        .get_account_bgp()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bgp_info))
}

#[instrument(skip(state))]
pub async fn get_account_bandwidth(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccountBandwidth>>> {
    let bandwidth_info = state
        .vultr_client
        .get_account_bandwidth()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bandwidth_info))
}

#[instrument(skip(state))]
pub async fn list_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::VultrPlan>>>> {
    let plans = state
        .vultr_client
        .list_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    // Plans are already in the correct format
    let converted_plans: Vec<crate::vultr::models::VultrPlan> = plans;

    Ok(success_response(converted_plans))
}

#[instrument(skip(state))]
pub async fn list_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::VultrRegion>>>> {
    let regions = state
        .vultr_client
        .list_regions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    // Regions are already in the correct format
    let converted_regions: Vec<crate::vultr::models::VultrRegion> = regions;

    Ok(success_response(converted_regions))
}

#[instrument(skip(state))]
pub async fn list_os(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::VultrOS>>>> {
    let os_list = state
        .vultr_client
        .list_os()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    // OS list is already in the correct format
    let converted_os: Vec<crate::vultr::models::VultrOS> = os_list;

    Ok(success_response(converted_os))
}

#[instrument(skip(state))]
pub async fn list_ssh_keys(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrSSHKey>>>> {
    let ssh_keys = state
        .vultr_client
        .list_ssh_keys()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ssh_keys))
}

#[instrument(skip(state, request))]
pub async fn create_ssh_key(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateSSHKeyRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSSHKey>>> {
    // Inline validation checks
    let name = request.name.as_ref().ok_or_else(|| {
        crate::controllers::ControllerError::Validation("name is required".to_string())
    })?;

    let ssh_key = request.ssh_key.as_ref().ok_or_else(|| {
        crate::controllers::ControllerError::Validation("ssh_key is required".to_string())
    })?;

    if name.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("name is required".to_string()));
    }

    if ssh_key.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("ssh_key is required".to_string()));
    }

    if !ssh_key.starts_with("ssh-") {
        return Err(crate::controllers::ControllerError::Validation("ssh_key must be in valid SSH key format".to_string()));
    }

    // Create validated request object
    let validated_request = CreateSSHKeyRequest {
        name: Some(name.clone()),
        ssh_key: Some(ssh_key.clone()),
    };

    let ssh_key = state
        .vultr_client
        .create_ssh_key(validated_request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ssh_key))
}

#[instrument(skip(state))]
pub async fn get_ssh_key(
    State(state): State<Arc<AppState>>,
    Path(ssh_key_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSSHKey>>> {
    // Inline validation checks
    if ssh_key_id.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("ssh_key_id is required".to_string()));
    }

    let ssh_key = state
        .vultr_client
        .get_ssh_key(&ssh_key_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ssh_key))
}

#[instrument(skip(state, request))]
pub async fn update_ssh_key(
    State(state): State<Arc<AppState>>,
    Path(ssh_key_id): Path<String>,
    Json(request): Json<UpdateSSHKeyRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    // Inline validation checks
    if ssh_key_id.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("ssh_key_id is required".to_string()));
    }

    if let Some(ref name) = request.name {
        if name.is_empty() {
            return Err(crate::controllers::ControllerError::Validation("name cannot be empty".to_string()));
        }
    }

    state
        .vultr_client
        .update_ssh_key(&ssh_key_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_ssh_key(
    State(state): State<Arc<AppState>>,
    Path(ssh_key_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    // Inline validation checks
    if ssh_key_id.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("ssh_key_id is required".to_string()));
    }

    state
        .vultr_client
        .delete_ssh_key(&ssh_key_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_backups(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrSnapshot>>>> {
    let backups = state
        .vultr_client
        .list_snapshots()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(backups))
}

// Bare Metal endpoints
#[instrument(skip(state))]
pub async fn list_bare_metal(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::VultrBareMetal>>>> {
    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bare_metals))
}

#[instrument(skip(state, request))]
pub async fn create_bare_metal(
    State(state): State<Arc<AppState>>,
    Json(request): Json<crate::vultr::models::CreateBareMetalRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::VultrBareMetal>>> {
    // Inline validation checks
    let region = request.region.as_ref().ok_or_else(|| {
        crate::controllers::ControllerError::Validation("region is required".to_string())
    })?;

    let plan = request.plan.as_ref().ok_or_else(|| {
        crate::controllers::ControllerError::Validation("plan is required".to_string())
    })?;

    if region.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("region is required".to_string()));
    }

    if plan.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("plan is required".to_string()));
    }

    if request.os_id.is_none() && request.app_id.is_none() && request.snapshot_id.is_none() {
        return Err(crate::controllers::ControllerError::Validation("either os_id, app_id, or snapshot_id is required".to_string()));
    }

    // Create validated request object
    let validated_request = crate::vultr::models::CreateBareMetalRequest {
        region: Some(region.clone()),
        plan: Some(plan.clone()),
        os_id: request.os_id,
        app_id: request.app_id,
        snapshot_id: request.snapshot_id.clone(),
        script_id: request.script_id.clone(),
        enable_ipv6: request.enable_ipv6,
        ssh_key_ids: request.ssh_key_ids.clone(),
        user_data: request.user_data.clone(),
        label: request.label.clone(),
        hostname: request.hostname.clone(),
        tag: request.tag.clone(),
        reserved_ipv4: request.reserved_ipv4.clone(),
        activation_email: request.activation_email,
        hostname_prefix: request.hostname_prefix.clone(),
    };

    let bare_metal = state
        .vultr_client
        .create_bare_metal(validated_request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn get_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::VultrBareMetal>>> {
    // Inline validation checks
    if baremetal_id.is_empty() {
        return Err(crate::controllers::ControllerError::Validation("baremetal_id is required".to_string()));
    }

    let bare_metal = state
        .vultr_client
        .get_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state, request))]
pub async fn update_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
    Json(request): Json<crate::vultr::models::UpdateBareMetalRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .update_bare_metal(&baremetal_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn delete_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_ipv4(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::BareMetalIpv4Info>>>> {
    let ipv4_info = state
        .vultr_client
        .get_bare_metal_ipv4(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ipv4_info))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_ipv6(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::BareMetalIpv6Info>>>> {
    let ipv6_info = state
        .vultr_client
        .get_bare_metal_ipv6(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ipv6_info))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_bandwidth(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::BareMetalBandwidth>>> {
    let bandwidth = state
        .vultr_client
        .get_bare_metal_bandwidth(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bandwidth))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_user_data(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::BareMetalUserData>>> {
    let user_data = state
        .vultr_client
        .get_bare_metal_user_data(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(user_data))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_upgrades(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::BareMetalUpgrades>>> {
    let upgrades = state
        .vultr_client
        .get_bare_metal_upgrades(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(upgrades))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_vnc(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::BareMetalVncInfo>>> {
    let vnc = state
        .vultr_client
        .get_bare_metal_vnc(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vnc))
}

#[instrument(skip(state))]
pub async fn list_bare_metal_vpcs(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::BareMetalVpcInfo>>>> {
    let vpcs = state
        .vultr_client
        .list_bare_metal_vpcs(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vpcs))
}

// Commented out until API client methods are implemented
// #[instrument(skip(state))]
// pub async fn attach_bare_metal_vpc(
//     State(state): State<Arc<AppState>>,
//     Path((baremetal_id, vpc_id)): Path<(String, String)>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .attach_bare_metal_vpc(&baremetal_id, &vpc_id)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// #[instrument(skip(state))]
// pub async fn detach_bare_metal_vpc(
//     State(state): State<Arc<AppState>>,
//     Path((baremetal_id, vpc_id)): Path<(String, String)>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .detach_bare_metal_vpc(&baremetal_id, &vpc_id)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// Commented out until API client method is implemented
// #[instrument(skip(state))]
// pub async fn start_bare_metal(
//     State(state): State<Arc<AppState>>,
//     Path(baremetal_id): Path<String>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .start_bare_metal(&baremetal_id)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// Commented out until API client method is implemented
// #[instrument(skip(state))]
// pub async fn reboot_bare_metal(
//     State(state): State<Arc<AppState>>,
//     Path(baremetal_id): Path<String>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .reboot_bare_metal(&baremetal_id)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// Commented out until API client methods are implemented
// #[instrument(skip(state))]
// pub async fn reinstall_bare_metal(
//     State(state): State<Arc<AppState>>,
//     Path(baremetal_id): Path<String>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
//     let bare_metal = state
//         .vultr_client
//         .reinstall_bare_metal(&baremetal_id)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(bare_metal))
// }

// #[instrument(skip(state))]
// pub async fn halt_bare_metal(
//     State(state): State<Arc<AppState>>,
//     Path(baremetal_id): Path<String>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .halt_bare_metal(&baremetal_id)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// #[instrument(skip(state, instance_ids))]
// pub async fn halt_bare_metals(
//     State(state): State<Arc<AppState>>,
//     Json(instance_ids): Json<Vec<String>>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .halt_bare_metals(instance_ids)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// #[instrument(skip(state, instance_ids))]
// pub async fn reboot_bare_metals(
//     State(state): State<Arc<AppState>>,
//     Json(instance_ids): Json<Vec<String>>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .reboot_bare_metals(instance_ids)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// #[instrument(skip(state, instance_ids))]
// pub async fn start_bare_metals(
//     State(state): State<Arc<AppState>>,
//     Json(instance_ids): Json<Vec<String>>,
// ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
//     state
//         .vultr_client
//         .start_bare_metals(instance_ids)
//         .await
//         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

//     Ok(success_response(()))
// }

// Block Storage endpoints
#[instrument(skip(state))]
pub async fn list_block_storage(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBlockStorage>>>> {
    let blocks = state
        .vultr_client
        .list_block_storage()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(blocks))
}

#[instrument(skip(state, request))]
pub async fn create_block_storage(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .create_block_storage(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state))]
pub async fn get_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .get_block_storage(&block_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state, request))]
pub async fn update_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<UpdateBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .update_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state))]
pub async fn delete_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_block_storage(&block_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn attach_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<AttachBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn detach_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<DetachBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Additional Billing endpoints
#[instrument(skip(state))]
pub async fn get_invoice_items(
    State(state): State<Arc<AppState>>,
    Path(invoice_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrInvoiceItem>>>> {
    let invoice_items = state
        .vultr_client
        .get_invoice_items(&invoice_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(invoice_items))
}

#[instrument(skip(state))]
pub async fn get_pending_charges(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPendingCharges>>> {
    let pending_charges = state
        .vultr_client
        .get_pending_charges()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(pending_charges))
}

// CDN Pull Zone endpoints
#[instrument(skip(state))]
pub async fn list_pull_zones(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPullZone>>>> {
    let pull_zones = state
        .vultr_client
        .list_pull_zones()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(pull_zones))
}

#[instrument(skip(state, request))]
pub async fn create_pull_zone(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreatePullZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPullZone>>> {
    let pull_zone = state
        .vultr_client
        .create_pull_zone(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(pull_zone))
}

#[instrument(skip(state))]
pub async fn get_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPullZone>>> {
    let pull_zone = state
        .vultr_client
        .get_pull_zone(&pullzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(pull_zone))
}

#[instrument(skip(state, request))]
pub async fn update_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
    Json(request): Json<UpdatePullZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPullZone>>> {
    let pull_zone = state
        .vultr_client
        .update_pull_zone(&pullzone_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(pull_zone))
}

#[instrument(skip(state))]
pub async fn delete_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_pull_zone(&pullzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn purge_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
    Json(request): Json<PurgePullZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .purge_pull_zone(&pullzone_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// CDN Push Zone endpoints
#[instrument(skip(state))]
pub async fn list_push_zones(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPushZone>>>> {
    let push_zones = state
        .vultr_client
        .list_push_zones()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(push_zones))
}

#[instrument(skip(state, request))]
pub async fn create_push_zone(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreatePushZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPushZone>>> {
    let push_zone = state
        .vultr_client
        .create_push_zone(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(push_zone))
}

#[instrument(skip(state))]
pub async fn get_push_zone(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPushZone>>> {
    let push_zone = state
        .vultr_client
        .get_push_zone(&pushzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(push_zone))
}

#[instrument(skip(state, request))]
pub async fn update_push_zone(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
    Json(request): Json<UpdatePushZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPushZone>>> {
    let push_zone = state
        .vultr_client
        .update_push_zone(&pushzone_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(push_zone))
}

#[instrument(skip(state))]
pub async fn delete_push_zone(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_push_zone(&pushzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_push_zone_files(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPushZoneFile>>>> {
    let files = state
        .vultr_client
        .get_push_zone_files(&pushzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(files))
}

#[instrument(skip(state))]
pub async fn delete_push_zone_file(
    State(state): State<Arc<AppState>>,
    Path((pushzone_id, file_name)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_push_zone_file(&pushzone_id, &file_name)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Enhanced Container Registry endpoints
#[instrument(skip(state, request))]
pub async fn create_registry(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateRegistryRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrContainerRegistry>>> {
    let registry = state
        .vultr_client
        .create_registry(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(registry))
}

#[instrument(skip(state, request))]
pub async fn update_registry(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
    Json(request): Json<UpdateRegistryRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrContainerRegistry>>> {
    let registry = state
        .vultr_client
        .update_registry(&registry_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(registry))
}

#[instrument(skip(state))]
pub async fn delete_registry(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_replications(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryReplication>>>> {
    let replications = state
        .vultr_client
        .list_registry_replications(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(replications))
}

#[instrument(skip(state, request))]
pub async fn create_registry_replication(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
    Json(request): Json<CreateReplicationRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryReplication>>> {
    let replication = state
        .vultr_client
        .create_registry_replication(&registry_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(replication))
}

#[instrument(skip(state))]
pub async fn get_registry_replication(
    State(state): State<Arc<AppState>>,
    Path((registry_id, replication_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryReplication>>> {
    let replication = state
        .vultr_client
        .get_registry_replication(&registry_id, &replication_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(replication))
}

#[instrument(skip(state))]
pub async fn delete_registry_replication(
    State(state): State<Arc<AppState>>,
    Path((registry_id, replication_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_replication(&registry_id, &replication_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_repositories(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryRepository>>>> {
    let repositories = state
        .vultr_client
        .list_registry_repositories(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(repositories))
}

#[instrument(skip(state))]
pub async fn get_registry_repository(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRepository>>> {
    let repository = state
        .vultr_client
        .get_registry_repository(&registry_id, &repository_image)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(repository))
}

#[instrument(skip(state, request))]
pub async fn update_registry_repository(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
    Json(request): Json<UpdateRepositoryRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRepository>>> {
    let repository = state
        .vultr_client
        .update_registry_repository(&registry_id, &repository_image, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(repository))
}

#[instrument(skip(state))]
pub async fn delete_registry_repository(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_repository(&registry_id, &repository_image)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn create_registry_docker_credentials(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDockerCredentials>>> {
    let credentials = state
        .vultr_client
        .create_registry_docker_credentials(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(credentials))
}

#[instrument(skip(state))]
pub async fn create_registry_kubernetes_docker_credentials(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesDockerCredentials>>> {
    let credentials = state
        .vultr_client
        .create_registry_kubernetes_docker_credentials(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(credentials))
}

#[instrument(skip(state, request))]
pub async fn update_registry_password(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
    Json(request): Json<UpdateRegistryPasswordRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_registry_password(&registry_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_robots(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryRobot>>>> {
    let robots = state
        .vultr_client
        .list_registry_robots(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(robots))
}

#[instrument(skip(state))]
pub async fn get_registry_robot(
    State(state): State<Arc<AppState>>,
    Path((registry_id, robot_name)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRobot>>> {
    let robot = state
        .vultr_client
        .get_registry_robot(&registry_id, &robot_name)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(robot))
}

#[instrument(skip(state, request))]
pub async fn update_registry_robot(
    State(state): State<Arc<AppState>>,
    Path((registry_id, robot_name)): Path<(String, String)>,
    Json(request): Json<UpdateRobotRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRobot>>> {
    let robot = state
        .vultr_client
        .update_registry_robot(&registry_id, &robot_name, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(robot))
}

#[instrument(skip(state))]
pub async fn delete_registry_robot(
    State(state): State<Arc<AppState>>,
    Path((registry_id, robot_name)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_robot(&registry_id, &robot_name)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_repository_artifacts(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryArtifact>>>> {
    let artifacts = state
        .vultr_client
        .list_registry_repository_artifacts(&registry_id, &repository_image)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(artifacts))
}

#[instrument(skip(state))]
pub async fn get_registry_repository_artifact(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image, artifact_digest)): Path<(String, String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryArtifact>>> {
    let artifact = state
        .vultr_client
        .get_registry_repository_artifact(&registry_id, &repository_image, &artifact_digest)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(artifact))
}

#[instrument(skip(state))]
pub async fn delete_registry_repository_artifact(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image, artifact_digest)): Path<(String, String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_repository_artifact(&registry_id, &repository_image, &artifact_digest)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryRegion>>>> {
    let regions = state
        .vultr_client
        .list_registry_regions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(regions))
}

#[instrument(skip(state))]
pub async fn list_registry_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryPlan>>>> {
    let plans = state
        .vultr_client
        .list_registry_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(plans))
}

// Enhanced DNS endpoints
#[instrument(skip(state))]
pub async fn list_dns_domains(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrDNSDomainDetailed>>>> {
    let domains = state
        .vultr_client
        .list_dns_domains()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(domains))
}

#[instrument(skip(state, request))]
pub async fn create_dns_domain(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateDNSDomainRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSDomainDetailed>>> {
    let domain = state
        .vultr_client
        .create_dns_domain(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(domain))
}

#[instrument(skip(state))]
pub async fn get_dns_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSDomainDetailed>>> {
    let domain_info = state
        .vultr_client
        .get_dns_domain(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(domain_info))
}

#[instrument(skip(state))]
pub async fn delete_dns_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_dns_domain(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn update_dns_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(request): Json<UpdateDNSDomainRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_dns_domain(&domain, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_dns_domain_soa(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSSOA>>> {
    let soa = state
        .vultr_client
        .get_dns_domain_soa(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(soa))
}

#[instrument(skip(state, request))]
pub async fn update_dns_domain_soa(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(request): Json<UpdateDNSSOARequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_dns_domain_soa(&domain, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_dns_domain_dnssec(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<String>>>> {
    let dnssec = state
        .vultr_client
        .get_dns_domain_dnssec(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(dnssec))
}

#[instrument(skip(state))]
pub async fn list_dns_domain_records(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrDNSRecordDetailed>>>> {
    let records = state
        .vultr_client
        .list_dns_domain_records(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(records))
}

#[instrument(skip(state, request))]
pub async fn create_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(request): Json<CreateDNSRecordRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSRecordDetailed>>> {
    let record = state
        .vultr_client
        .create_dns_domain_record(&domain, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(record))
}

#[instrument(skip(state))]
pub async fn get_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path((domain, record_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSRecordDetailed>>> {
    let record = state
        .vultr_client
        .get_dns_domain_record(&domain, &record_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(record))
}

#[instrument(skip(state, request))]
pub async fn update_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path((domain, record_id)): Path<(String, String)>,
    Json(request): Json<UpdateDNSRecordRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_dns_domain_record(&domain, &record_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path((domain, record_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_dns_domain_record(&domain, &record_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Enhanced Firewall endpoints
#[instrument(skip(state))]
pub async fn list_firewall_groups(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrFirewallGroupDetailed>>>> {
    let firewall_groups = state
        .vultr_client
        .list_firewall_groups()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_groups))
}

#[instrument(skip(state, request))]
pub async fn create_firewall_group(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateFirewallGroupRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallGroupDetailed>>> {
    let firewall_group = state
        .vultr_client
        .create_firewall_group(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_group))
}

#[instrument(skip(state))]
pub async fn get_firewall_group(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallGroupDetailed>>> {
    let firewall_group = state
        .vultr_client
        .get_firewall_group(&firewall_group_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_group))
}

#[instrument(skip(state, request))]
pub async fn update_firewall_group(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
    Json(request): Json<UpdateFirewallGroupRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_firewall_group(&firewall_group_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_firewall_group(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_firewall_group(&firewall_group_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_firewall_group_rules(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrFirewallRuleDetailed>>>> {
    let firewall_rules = state
        .vultr_client
        .list_firewall_group_rules(&firewall_group_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_rules))
}

#[instrument(skip(state, request))]
pub async fn create_firewall_group_rule(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
    Json(request): Json<CreateFirewallRuleRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallRuleDetailed>>> {
    let firewall_rule = state
        .vultr_client
        .create_firewall_group_rule(&firewall_group_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_rule))
}

#[instrument(skip(state))]
pub async fn get_firewall_group_rule(
    State(state): State<Arc<AppState>>,
    Path((firewall_group_id, firewall_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallRuleDetailed>>> {
    let firewall_rule = state
        .vultr_client
        .get_firewall_group_rule(&firewall_group_id, &firewall_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_rule))
}

#[instrument(skip(state))]
pub async fn delete_firewall_group_rule(
    State(state): State<Arc<AppState>>,
    Path((firewall_group_id, firewall_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_firewall_group_rule(&firewall_group_id, &firewall_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Enhanced Instance endpoints
#[instrument(skip(state))]
pub async fn list_instances_detailed(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrInstanceDetailed>>>> {
    let instances = state
        .vultr_client
        .list_instances_detailed()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(instances))
}

#[instrument(skip(state, request))]
pub async fn create_instance_detailed(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateInstanceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInstanceDetailed>>> {
    let instance = state
        .vultr_client
        .create_instance_detailed(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(instance))
}

#[instrument(skip(state))]
pub async fn get_instance_detailed(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInstanceDetailed>>> {
    let instance = state
        .vultr_client
        .get_instance_detailed(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(instance))
}

#[instrument(skip(state, request))]
pub async fn update_instance_detailed(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<UpdateInstanceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInstanceDetailed>>> {
    let instance = state
        .vultr_client
        .update_instance_detailed(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(instance))
}

#[instrument(skip(state))]
pub async fn delete_instance_detailed(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_instance_detailed(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn halt_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_instance(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn halt_instances(
    State(state): State<Arc<AppState>>,
    Json(request): Json<BulkInstanceActionRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_instances(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reboot_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_instance(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn reboot_instances(
    State(state): State<Arc<AppState>>,
    Json(request): Json<BulkInstanceActionRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_instances(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn start_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_instance(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn start_instances(
    State(state): State<Arc<AppState>>,
    Json(request): Json<BulkInstanceActionRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_instances(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reinstall_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInstanceDetailed>>> {
    let instance = state
        .vultr_client
        .reinstall_instance(&instance_id, None)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(instance))
}

#[instrument(skip(state))]
pub async fn get_instance_bandwidth(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<HashMap<String, InstanceBandwidth>>>> {
    let bandwidth = state
        .vultr_client
        .get_instance_bandwidth(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bandwidth))
}

#[instrument(skip(state))]
pub async fn get_instance_neighbors(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<InstanceNeighbor>>>> {
    let neighbors = state
        .vultr_client
        .get_instance_neighbors(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(neighbors))
}

#[instrument(skip(state))]
pub async fn list_instance_vpcs(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<InstanceVPC>>>> {
    let vpcs = state
        .vultr_client
        .list_instance_vpcs(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vpcs))
}

#[instrument(skip(state))]
pub async fn get_instance_iso_status(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceISOStatus>>> {
    let iso_status = state
        .vultr_client
        .get_instance_iso_status(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(iso_status))
}

#[instrument(skip(state, request))]
pub async fn attach_instance_iso(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<AttachISORequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_instance_iso(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn detach_instance_iso(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_instance_iso(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn attach_instance_vpc(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<AttachVPCRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_instance_vpc(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn detach_instance_vpc(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<DetachVPCRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_instance_vpc(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn create_instance_backup_schedule(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<CreateBackupScheduleRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .create_instance_backup_schedule(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_instance_backup_schedule(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceBackupSchedule>>> {
    let backup_schedule = state
        .vultr_client
        .get_instance_backup_schedule(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(backup_schedule))
}

#[instrument(skip(state, request))]
pub async fn restore_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<RestoreInstanceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .restore_instance(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_instance_ipv4(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<InstanceIPv4>>>> {
    let ipv4s = state
        .vultr_client
        .get_instance_ipv4(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ipv4s))
}

#[instrument(skip(state, request))]
pub async fn create_instance_ipv4(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<CreateInstanceIPv4Request>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceIPv4>>> {
    let ipv4 = state
        .vultr_client
        .create_instance_ipv4(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ipv4))
}

#[instrument(skip(state))]
pub async fn delete_instance_ipv4(
    State(state): State<Arc<AppState>>,
    Path((instance_id, ipv4)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_instance_ipv4(&instance_id, &ipv4)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_instance_ipv6(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<InstanceIPv6>>>> {
    let ipv6s = state
        .vultr_client
        .get_instance_ipv6(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(ipv6s))
}

#[instrument(skip(state, request))]
pub async fn create_instance_reverse_ipv6(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<CreateReverseIPv6Request>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .create_instance_reverse_ipv6(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_instance_ipv6_reverse(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<InstanceIPv6Reverse>>>> {
    let reverse_ipv6s = state
        .vultr_client
        .list_instance_ipv6_reverse(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(reverse_ipv6s))
}

#[instrument(skip(state))]
pub async fn delete_instance_reverse_ipv6(
    State(state): State<Arc<AppState>>,
    Path((instance_id, ipv6)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_instance_reverse_ipv6(&instance_id, &ipv6)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn create_instance_reverse_ipv4(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<CreateReverseIPv4Request>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .create_instance_reverse_ipv4(&instance_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn set_instance_default_reverse_ipv4(
    State(state): State<Arc<AppState>>,
    Path((instance_id, ip)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .set_instance_default_reverse_ipv4(&instance_id, &ip)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_instance_userdata(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceUserData>>> {
    let userdata = state
        .vultr_client
        .get_instance_userdata(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(userdata))
}

#[instrument(skip(state))]
pub async fn get_instance_upgrades(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceUpgrades>>> {
    let upgrades = state
        .vultr_client
        .get_instance_upgrades(&instance_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(upgrades))
}

#[instrument(skip(state))]
pub async fn get_instance_job(
    State(state): State<Arc<AppState>>,
    Path((instance_id, job_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceJob>>> {
    let job = state
        .vultr_client
        .get_instance_job(&instance_id, &job_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(job))
}

// ISO endpoints
#[instrument(skip(state))]
pub async fn list_isos(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrISO>>>> {
    let isos = state
        .vultr_client
        .list_isos()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(isos))
}

#[instrument(skip(state, request))]
pub async fn create_iso(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateISORequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrISO>>> {
    let iso = state
        .vultr_client
        .create_iso(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(iso))
}

#[instrument(skip(state))]
pub async fn get_iso(
    State(state): State<Arc<AppState>>,
    Path(iso_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrISO>>> {
    let iso = state
        .vultr_client
        .get_iso(&iso_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(iso))
}

#[instrument(skip(state))]
pub async fn delete_iso(
    State(state): State<Arc<AppState>>,
    Path(iso_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_iso(&iso_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_public_isos(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPublicISO>>>> {
    let public_isos = state
        .vultr_client
        .list_public_isos()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(public_isos))
}

// Enhanced Kubernetes endpoints
#[instrument(skip(state, request))]
pub async fn create_kubernetes_cluster(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateKubernetesClusterRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesClusterDetailed>>> {
    let cluster = state
        .vultr_client
        .create_kubernetes_cluster(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(cluster))
}

#[instrument(skip(state))]
pub async fn list_kubernetes_clusters(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrKubernetesClusterDetailed>>>> {
    let clusters = state
        .vultr_client
        .list_kubernetes_clusters()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(clusters))
}

#[instrument(skip(state))]
pub async fn get_kubernetes_cluster(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesClusterDetailed>>> {
    let cluster = state
        .vultr_client
        .get_kubernetes_cluster(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(cluster))
}

#[instrument(skip(state, request))]
pub async fn update_kubernetes_cluster(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
    Json(request): Json<UpdateKubernetesClusterRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesClusterDetailed>>> {
    let cluster = state
        .vultr_client
        .update_kubernetes_cluster(&vke_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(cluster))
}

#[instrument(skip(state))]
pub async fn delete_kubernetes_cluster(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_kubernetes_cluster(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_kubernetes_cluster_with_resources(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_kubernetes_cluster_with_resources(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_kubernetes_resources(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesResources>>> {
    let resources = state
        .vultr_client
        .get_kubernetes_resources(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(resources))
}

#[instrument(skip(state))]
pub async fn get_kubernetes_available_upgrades(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesUpgrades>>> {
    let upgrades = state
        .vultr_client
        .get_kubernetes_available_upgrades(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(upgrades))
}

#[instrument(skip(state, request))]
pub async fn start_kubernetes_cluster_upgrade(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
    Json(request): Json<StartKubernetesUpgradeRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_kubernetes_cluster_upgrade(&vke_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn create_nodepool(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
    Json(request): Json<CreateNodePoolRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrNodePoolDetailed>>> {
    let nodepool = state
        .vultr_client
        .create_nodepool(&vke_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(nodepool))
}

#[instrument(skip(state))]
pub async fn get_nodepools(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrNodePoolDetailed>>>> {
    let nodepools = state
        .vultr_client
        .get_nodepools(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(nodepools))
}

#[instrument(skip(state))]
pub async fn get_nodepool(
    State(state): State<Arc<AppState>>,
    Path((vke_id, nodepool_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrNodePoolDetailed>>> {
    let nodepool = state
        .vultr_client
        .get_nodepool(&vke_id, &nodepool_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(nodepool))
}

#[instrument(skip(state, request))]
pub async fn update_nodepool(
    State(state): State<Arc<AppState>>,
    Path((vke_id, nodepool_id)): Path<(String, String)>,
    Json(request): Json<UpdateNodePoolRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrNodePoolDetailed>>> {
    let nodepool = state
        .vultr_client
        .update_nodepool(&vke_id, &nodepool_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(nodepool))
}

#[instrument(skip(state))]
pub async fn delete_nodepool(
    State(state): State<Arc<AppState>>,
    Path((vke_id, nodepool_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_nodepool(&vke_id, &nodepool_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_nodepool_instance(
    State(state): State<Arc<AppState>>,
    Path((vke_id, nodepool_id, node_id)): Path<(String, String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_nodepool_instance(&vke_id, &nodepool_id, &node_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn recycle_nodepool_instance(
    State(state): State<Arc<AppState>>,
    Path((vke_id, nodepool_id, node_id)): Path<(String, String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .recycle_nodepool_instance(&vke_id, &nodepool_id, &node_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_kubernetes_cluster_config(
    State(state): State<Arc<AppState>>,
    Path(vke_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesConfig>>> {
    let config = state
        .vultr_client
        .get_kubernetes_cluster_config(&vke_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(config))
}

#[instrument(skip(state))]
pub async fn get_kubernetes_versions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesVersions>>> {
    let versions = state
        .vultr_client
        .get_kubernetes_versions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(versions))
}

// Enhanced Load Balancer endpoints
#[instrument(skip(state))]
pub async fn list_load_balancers(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrLoadBalancerDetailed>>>> {
    let load_balancers = state
        .vultr_client
        .list_load_balancers()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(load_balancers))
}

#[instrument(skip(state, request))]
pub async fn create_load_balancer(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateLoadBalancerRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrLoadBalancerDetailed>>> {
    let load_balancer = state
        .vultr_client
        .create_load_balancer(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(load_balancer))
}

#[instrument(skip(state))]
pub async fn get_load_balancer(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrLoadBalancerDetailed>>> {
    let load_balancer = state
        .vultr_client
        .get_load_balancer(&load_balancer_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(load_balancer))
}

#[instrument(skip(state, request))]
pub async fn update_load_balancer(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
    Json(request): Json<UpdateLoadBalancerRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_load_balancer(&load_balancer_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_load_balancer(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_load_balancer(&load_balancer_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_load_balancer_ssl(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_load_balancer_ssl(&load_balancer_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_load_balancer_auto_ssl(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_load_balancer_auto_ssl(&load_balancer_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn create_load_balancer_forwarding_rule(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
    Json(request): Json<CreateForwardingRuleRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrForwardingRuleDetailed>>> {
    let forwarding_rule = state
        .vultr_client
        .create_load_balancer_forwarding_rule(&load_balancer_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(forwarding_rule))
}

#[instrument(skip(state))]
pub async fn get_load_balancer_forwarding_rule(
    State(state): State<Arc<AppState>>,
    Path((load_balancer_id, forwarding_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrForwardingRuleDetailed>>> {
    let forwarding_rule = state
        .vultr_client
        .get_load_balancer_forwarding_rule(&load_balancer_id, &forwarding_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(forwarding_rule))
}

#[instrument(skip(state))]
pub async fn delete_load_balancer_forwarding_rule(
    State(state): State<Arc<AppState>>,
    Path((load_balancer_id, forwarding_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_load_balancer_forwarding_rule(&load_balancer_id, &forwarding_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_load_balancer_firewall_rules(
    State(state): State<Arc<AppState>>,
    Path(load_balancer_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrLoadBalancerFirewallRule>>>> {
    let firewall_rules = state
        .vultr_client
        .list_load_balancer_firewall_rules(&load_balancer_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_rules))
}

#[instrument(skip(state))]
pub async fn get_load_balancer_firewall_rule(
    State(state): State<Arc<AppState>>,
    Path((load_balancer_id, firewall_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrLoadBalancerFirewallRule>>> {
    let firewall_rule = state
        .vultr_client
        .get_load_balancer_firewall_rule(&load_balancer_id, &firewall_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(firewall_rule))
}

// Managed Database endpoints
#[instrument(skip(state))]
pub async fn list_database_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrDatabasePlan>>>> {
    let plans = state
        .vultr_client
        .list_database_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(plans))
}

#[instrument(skip(state))]
pub async fn list_managed_databases(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrManagedDatabase>>>> {
    let databases = state
        .vultr_client
        .list_managed_databases()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(databases))
}

#[instrument(skip(state, request))]
pub async fn create_managed_database(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateManagedDatabaseRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrManagedDatabase>>> {
    let database = state
        .vultr_client
        .create_managed_database(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(database))
}

#[instrument(skip(state))]
pub async fn get_managed_database(
    State(state): State<Arc<AppState>>,
    Path(database_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrManagedDatabase>>> {
    let database = state
        .vultr_client
        .get_managed_database(&database_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(database))
}

#[instrument(skip(state, request))]
pub async fn update_managed_database(
    State(state): State<Arc<AppState>>,
    Path(database_id): Path<String>,
    Json(request): Json<UpdateManagedDatabaseRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrManagedDatabase>>> {
    let database = state
        .vultr_client
        .update_managed_database(&database_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(database))
}

#[instrument(skip(state))]
pub async fn delete_managed_database(
    State(state): State<Arc<AppState>>,
    Path(database_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_managed_database(&database_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_database_usage(
    State(state): State<Arc<AppState>>,
    Path(database_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDatabaseUsage>>> {
    let usage = state
        .vultr_client
        .get_database_usage(&database_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(usage))
}

// Database User endpoints
#[instrument(skip(state))]
pub async fn list_database_users(
    State(state): State<Arc<AppState>>,
    Path(database_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrDatabaseUser>>>> {
    let users = state
        .vultr_client
        .list_database_users(&database_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(users))
}

#[instrument(skip(state, request))]
pub async fn create_database_user(
    State(state): State<Arc<AppState>>,
    Path(database_id): Path<String>,
    Json(request): Json<CreateDatabaseUserRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDatabaseUser>>> {
    let user = state
        .vultr_client
        .create_database_user(&database_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(user))
}

#[instrument(skip(state))]
pub async fn get_database_user(
    State(state): State<Arc<AppState>>,
    Path((database_id, username)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDatabaseUser>>> {
    let user = state
        .vultr_client
        .get_database_user(&database_id, &username)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(user))
}

#[instrument(skip(state, request))]
pub async fn update_database_user(
    State(state): State<Arc<AppState>>,
    Path((database_id, username)): Path<(String, String)>,
    Json(request): Json<UpdateDatabaseUserRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDatabaseUser>>> {
    let user = state
        .vultr_client
        .update_database_user(&database_id, &username, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(user))
}

#[instrument(skip(state))]
pub async fn delete_database_user(
    State(state): State<Arc<AppState>>,
    Path((database_id, username)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_database_user(&database_id, &username)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn set_database_user_acl(
    State(state): State<Arc<AppState>>,
    Path((database_id, username)): Path<(String, String)>,
    Json(request): Json<SetDatabaseUserACLRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .set_database_user_acl(&database_id, &username, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Marketplace endpoints
#[instrument(skip(state))]
pub async fn list_marketplace_app_variables(
    State(state): State<Arc<AppState>>,
    Path(image_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrMarketplaceAppVariable>>>> {
    let variables = state
        .vultr_client
        .list_marketplace_app_variables(&image_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(variables))
}

// Object Storage (S3) endpoints
#[instrument(skip(state))]
pub async fn list_object_storages(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrObjectStorage>>>> {
    let storages = state
        .vultr_client
        .list_object_storages()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(storages))
}

#[instrument(skip(state, request))]
pub async fn create_object_storage(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateObjectStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrObjectStorage>>> {
    let storage = state
        .vultr_client
        .create_object_storage(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(storage))
}

#[instrument(skip(state))]
pub async fn get_object_storage(
    State(state): State<Arc<AppState>>,
    Path(object_storage_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrObjectStorage>>> {
    let storage = state
        .vultr_client
        .get_object_storage(&object_storage_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(storage))
}

#[instrument(skip(state))]
pub async fn delete_object_storage(
    State(state): State<Arc<AppState>>,
    Path(object_storage_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_object_storage(&object_storage_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn update_object_storage(
    State(state): State<Arc<AppState>>,
    Path(object_storage_id): Path<String>,
    Json(request): Json<UpdateObjectStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrObjectStorage>>> {
    let storage = state
        .vultr_client
        .update_object_storage(&object_storage_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(storage))
}

#[instrument(skip(state))]
pub async fn regenerate_object_storage_keys(
    State(state): State<Arc<AppState>>,
    Path(object_storage_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrObjectStorage>>> {
    let storage = state
        .vultr_client
        .regenerate_object_storage_keys(&object_storage_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(storage))
}

#[instrument(skip(state))]
pub async fn list_object_storage_clusters(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrObjectStorageCluster>>>> {
    let clusters = state
        .vultr_client
        .list_object_storage_clusters()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(clusters))
}

#[instrument(skip(state))]
pub async fn list_object_storage_tiers(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrObjectStorageTier>>>> {
    let tiers = state
        .vultr_client
        .list_object_storage_tiers()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(tiers))
}

#[instrument(skip(state))]
pub async fn list_object_storage_cluster_tiers(
    State(state): State<Arc<AppState>>,
    Path(cluster_id): Path<u32>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrObjectStorageTier>>>> {
    let tiers = state
        .vultr_client
        .list_object_storage_cluster_tiers(cluster_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(tiers))
}





#[instrument(skip(state))]
pub async fn list_metal_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrMetalPlan>>>> {
    let plans = state
        .vultr_client
        .list_metal_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(plans))
}

// Serverless Inference endpoints
#[instrument(skip(state))]
pub async fn list_inference(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrInference>>>> {
    let inferences = state
        .vultr_client
        .list_inference()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(inferences))
}

#[instrument(skip(state, request))]
pub async fn create_inference(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateInferenceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInference>>> {
    let inference = state
        .vultr_client
        .create_inference(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(inference))
}

#[instrument(skip(state))]
pub async fn get_inference(
    State(state): State<Arc<AppState>>,
    Path(inference_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInference>>> {
    let inference = state
        .vultr_client
        .get_inference(&inference_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(inference))
}

#[instrument(skip(state, request))]
pub async fn update_inference(
    State(state): State<Arc<AppState>>,
    Path(inference_id): Path<String>,
    Json(request): Json<UpdateInferenceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInference>>> {
    let inference = state
        .vultr_client
        .update_inference(&inference_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(inference))
}

#[instrument(skip(state))]
pub async fn delete_inference(
    State(state): State<Arc<AppState>>,
    Path(inference_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_inference(&inference_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_inference_usage(
    State(state): State<Arc<AppState>>,
    Path(inference_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrInferenceUsage>>> {
    let usage = state
        .vultr_client
        .get_inference_usage(&inference_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(usage))
}

// VPC endpoints
#[instrument(skip(state))]
pub async fn list_vpcs(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrVPCDetailed>>>> {
    let vpcs = state
        .vultr_client
        .list_vpcs()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vpcs))
}

#[instrument(skip(state, request))]
pub async fn create_vpc(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateVPCRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrVPCDetailed>>> {
    let vpc = state
        .vultr_client
        .create_vpc(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vpc))
}

#[instrument(skip(state))]
pub async fn get_vpc(
    State(state): State<Arc<AppState>>,
    Path(vpc_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrVPCDetailed>>> {
    let vpc = state
        .vultr_client
        .get_vpc(&vpc_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vpc))
}

#[instrument(skip(state, request))]
pub async fn update_vpc(
    State(state): State<Arc<AppState>>,
    Path(vpc_id): Path<String>,
    Json(request): Json<UpdateVPCRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_vpc(&vpc_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_vpc(
    State(state): State<Arc<AppState>>,
    Path(vpc_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_vpc(&vpc_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Reserved IP endpoints
#[instrument(skip(state))]
pub async fn list_reserved_ips(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrReservedIPDetailed>>>> {
    let reserved_ips = state
        .vultr_client
        .list_reserved_ips()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(reserved_ips))
}

#[instrument(skip(state, request))]
pub async fn create_reserved_ip(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateReservedIPRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrReservedIPDetailed>>> {
    let reserved_ip = state
        .vultr_client
        .create_reserved_ip(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(reserved_ip))
}

#[instrument(skip(state))]
pub async fn get_reserved_ip(
    State(state): State<Arc<AppState>>,
    Path(reserved_ip): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrReservedIPDetailed>>> {
    let reserved_ip_info = state
        .vultr_client
        .get_reserved_ip(&reserved_ip)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(reserved_ip_info))
}

#[instrument(skip(state, request))]
pub async fn update_reserved_ip(
    State(state): State<Arc<AppState>>,
    Path(reserved_ip): Path<String>,
    Json(request): Json<UpdateReservedIPRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrReservedIPDetailed>>> {
    let reserved_ip_info = state
        .vultr_client
        .update_reserved_ip(&reserved_ip, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(reserved_ip_info))
}

#[instrument(skip(state))]
pub async fn delete_reserved_ip(
    State(state): State<Arc<AppState>>,
    Path(reserved_ip): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_reserved_ip(&reserved_ip)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn attach_reserved_ip(
    State(state): State<Arc<AppState>>,
    Path(reserved_ip): Path<String>,
    Json(request): Json<AttachReservedIPRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_reserved_ip(&reserved_ip, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn detach_reserved_ip(
    State(state): State<Arc<AppState>>,
    Path(reserved_ip): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_reserved_ip(&reserved_ip)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn convert_reserved_ip(
    State(state): State<Arc<AppState>>,
    Json(request): Json<ConvertReservedIPRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrReservedIPDetailed>>> {
    let reserved_ip = state
        .vultr_client
        .convert_reserved_ip(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(reserved_ip))
}



#[instrument(skip(state))]
pub async fn list_available_plans_region(
    State(state): State<Arc<AppState>>,
    Path(region_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegionAvailablePlans>>> {
    let plans = state
        .vultr_client
        .list_available_plans_region(&region_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(plans))
}

// Snapshot endpoints
#[instrument(skip(state))]
pub async fn list_snapshots(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrSnapshot>>>> {
    let snapshots = state
        .vultr_client
        .list_snapshots()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(snapshots))
}

#[instrument(skip(state, request))]
pub async fn create_snapshot(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateSnapshotRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSnapshot>>> {
    let snapshot = state
        .vultr_client
        .create_snapshot(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(snapshot))
}

#[instrument(skip(state, request))]
pub async fn create_snapshot_from_url(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateSnapshotFromUrlRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSnapshot>>> {
    let snapshot = state
        .vultr_client
        .create_snapshot_from_url(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(snapshot))
}

#[instrument(skip(state))]
pub async fn get_snapshot(
    State(state): State<Arc<AppState>>,
    Path(snapshot_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSnapshot>>> {
    let snapshot = state
        .vultr_client
        .get_snapshot(&snapshot_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(snapshot))
}

#[instrument(skip(state, request))]
pub async fn update_snapshot(
    State(state): State<Arc<AppState>>,
    Path(snapshot_id): Path<String>,
    Json(request): Json<UpdateSnapshotRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSnapshot>>> {
    let snapshot = state
        .vultr_client
        .update_snapshot(&snapshot_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(snapshot))
}

#[instrument(skip(state))]
pub async fn delete_snapshot(
    State(state): State<Arc<AppState>>,
    Path(snapshot_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_snapshot(&snapshot_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Subaccount endpoints
#[instrument(skip(state))]
pub async fn list_subaccounts(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrSubaccount>>>> {
    let subaccounts = state
        .vultr_client
        .list_subaccounts()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(subaccounts))
}

#[instrument(skip(state, request))]
pub async fn create_subaccount(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateSubaccountRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrSubaccount>>> {
    let subaccount = state
        .vultr_client
        .create_subaccount(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(subaccount))
}

// Startup Script endpoints
#[instrument(skip(state))]
pub async fn list_startup_scripts(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrStartupScript>>>> {
    let scripts = state
        .vultr_client
        .list_startup_scripts()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(scripts))
}

#[instrument(skip(state, request))]
pub async fn create_startup_script(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateStartupScriptRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrStartupScript>>> {
    let script = state
        .vultr_client
        .create_startup_script(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(script))
}

#[instrument(skip(state))]
pub async fn get_startup_script(
    State(state): State<Arc<AppState>>,
    Path(script_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrStartupScript>>> {
    let script = state
        .vultr_client
        .get_startup_script(&script_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(script))
}

#[instrument(skip(state, request))]
pub async fn update_startup_script(
    State(state): State<Arc<AppState>>,
    Path(script_id): Path<String>,
    Json(request): Json<UpdateStartupScriptRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_startup_script(&script_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_startup_script(
    State(state): State<Arc<AppState>>,
    Path(script_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_startup_script(&script_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Storage Gateway endpoints
#[instrument(skip(state))]
pub async fn list_storage_gateways(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrStorageGateway>>>> {
    let gateways = state
        .vultr_client
        .list_storage_gateways()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(gateways))
}

#[instrument(skip(state, request))]
pub async fn create_storage_gateway(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateStorageGatewayRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrStorageGateway>>> {
    let gateway = state
        .vultr_client
        .create_storage_gateway(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(gateway))
}

#[instrument(skip(state))]
pub async fn get_storage_gateway(
    State(state): State<Arc<AppState>>,
    Path(gateway_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrStorageGateway>>> {
    let gateway = state
        .vultr_client
        .get_storage_gateway(&gateway_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(gateway))
}

#[instrument(skip(state, request))]
pub async fn update_storage_gateway(
    State(state): State<Arc<AppState>>,
    Path(gateway_id): Path<String>,
    Json(request): Json<UpdateStorageGatewayRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_storage_gateway(&gateway_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_storage_gateway(
    State(state): State<Arc<AppState>>,
    Path(gateway_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_storage_gateway(&gateway_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// User Management endpoints
#[instrument(skip(state))]
pub async fn list_users(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrUser>>>> {
    let users = state
        .vultr_client
        .list_users()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(users))
}

#[instrument(skip(state, request))]
pub async fn create_user(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateUserRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrUser>>> {
    let user = state
        .vultr_client
        .create_user(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(user))
}

#[instrument(skip(state))]
pub async fn get_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrUser>>> {
    let user = state
        .vultr_client
        .get_user(&user_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(user))
}

#[instrument(skip(state, request))]
pub async fn update_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
    Json(request): Json<UpdateUserRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_user(&user_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_user(&user_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// VFS endpoints
#[instrument(skip(state))]
pub async fn list_vfs_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrVFSRegion>>>> {
    let regions = state
        .vultr_client
        .list_vfs_regions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(regions))
}

#[instrument(skip(state))]
pub async fn list_vfs(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrVFS>>>> {
    let vfs = state
        .vultr_client
        .list_vfs()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vfs))
}

#[instrument(skip(state, request))]
pub async fn create_vfs(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateVFSRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrVFS>>> {
    let vfs = state
        .vultr_client
        .create_vfs(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vfs))
}

#[instrument(skip(state))]
pub async fn get_vfs(
    State(state): State<Arc<AppState>>,
    Path(vfs_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrVFS>>> {
    let vfs = state
        .vultr_client
        .get_vfs(&vfs_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vfs))
}

#[instrument(skip(state, request))]
pub async fn update_vfs(
    State(state): State<Arc<AppState>>,
    Path(vfs_id): Path<String>,
    Json(request): Json<UpdateVFSRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrVFS>>> {
    let vfs = state
        .vultr_client
        .update_vfs(&vfs_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(vfs))
}

#[instrument(skip(state))]
pub async fn delete_vfs(
    State(state): State<Arc<AppState>>,
    Path(vfs_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_vfs(&vfs_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

// Bare Metal Operation endpoints
#[instrument(skip(state))]
pub async fn start_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reboot_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn halt_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reinstall_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<crate::vultr::models::VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .reinstall_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state, request))]
pub async fn start_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(request): Json<BareMetalActionRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_bare_metals(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn reboot_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(request): Json<BareMetalActionRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_bare_metals(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn halt_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(request): Json<BareMetalActionRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_bare_metals(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn attach_bare_metal_vpc(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
    Json(request): Json<AttachBareMetalVPCRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_bare_metal_vpc(&baremetal_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn detach_bare_metal_vpc(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
    Json(request): Json<DetachBareMetalVPCRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_bare_metal_vpc(&baremetal_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("API error: {}", e)))?;

    Ok(success_response(()))
}
