use crate::{
    database::Database,
    models::{User, UserProfile},
    services::{ServiceError, ServiceResult},
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use mongodb::Collection;
use tracing::instrument;

pub struct UserService {
    users: Collection<User>,
}

impl UserService {
    pub fn new(database: &Database) -> Self {
        Self {
            users: database.collection("users"),
        }
    }

    #[instrument(skip(self))]
    pub async fn get_user_profile(&self, user_id: &str) -> ServiceResult<UserProfile> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let user = self
            .users
            .find_one(doc! { "_id": user_object_id }, None)
            .await?
            .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))?;

        Ok(user.into())
    }

    #[instrument(skip(self))]
    pub async fn get_user_servers_count(&self, user_id: &str) -> ServiceResult<u32> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Count user's servers from instances collection
        let instances_collection: mongodb::Collection<bson::Document> = self.users.database().collection("instances");
        let count = instances_collection
            .count_documents(doc! { "user_id": user_object_id }, None)
            .await?;

        Ok(count as u32)
    }

    #[instrument(skip(self))]
    pub async fn get_user_applications_count(&self, user_id: &str) -> ServiceResult<u32> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Count user's applications from applications collection
        let applications_collection: mongodb::Collection<bson::Document> = self.users.database().collection("applications");
        let count = applications_collection
            .count_documents(doc! { "user_id": user_object_id }, None)
            .await?;

        Ok(count as u32)
    }

    #[instrument(skip(self, update_data))]
    pub async fn update_user_profile(
        &self,
        user_id: &str,
        update_data: serde_json::Value,
    ) -> ServiceResult<UserProfile> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Build update document with allowed fields
        let mut update_doc = doc! { "updated_at": Utc::now() };

        if let Some(first_name) = update_data.get("first_name").and_then(|v| v.as_str()) {
            if !first_name.trim().is_empty() {
                update_doc.insert("first_name", first_name);
            }
        }

        if let Some(last_name) = update_data.get("last_name").and_then(|v| v.as_str()) {
            if !last_name.trim().is_empty() {
                update_doc.insert("last_name", last_name);
            }
        }

        if let Some(company) = update_data.get("company") {
            if company.is_null() {
                update_doc.insert("company", bson::Bson::Null);
            } else if let Some(company_str) = company.as_str() {
                update_doc.insert("company", company_str);
            }
        }

        // Update user in database
        self.users
            .update_one(
                doc! { "_id": user_object_id },
                doc! { "$set": update_doc },
                None,
            )
            .await?;

        // Return updated profile
        self.get_user_profile(user_id).await
    }
}
