{"rustc": 2830703817519440116, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 1116374142960516676, "deps": [[5466618496199522463, "crc32fast", false, 10495063486559070578], [7636735136738807108, "miniz_oxide", false, 10700016771638044818]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-ac21ad2e46875deb\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}