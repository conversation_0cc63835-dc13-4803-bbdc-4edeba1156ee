use crate::{
    controllers::{<PERSON><PERSON><PERSON><PERSON>, ControllerResult},
    models::{
        ApiResponse, CreateApplicationRequest, ApplicationResponse, TriggerDeploymentRequest,
        DeploymentResponse, PaginationQuery
    },
    services::{deployment::DeploymentService},
    models::<PERSON><PERSON><PERSON>,
    AppState,
};
use axum::{
    extract::{Path, Query, State},
    response::Json,
};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, claims, request))]
pub async fn create_application(
    State(state): State<Arc<AppState>>,
    claims: <PERSON><PERSON><PERSON>,
    J<PERSON>(request): Json<CreateApplicationRequest>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Inline validation checks
    request.validate().map_err(|e| {
        ControllerError::Validation(format!("application data is invalid: {}", e))
    })?;

    let name = request.name.as_str();

    if name.is_empty() {
        return Err(ControllerError::Validation("name is required".to_string()));
    }

    if name.len() > 100 {
        return Err(ControllerError::Validation("name must be less than 100 characters".to_string()));
    }

    // Validate repository URL
    let repository = &request.repository;
    if repository.url.is_empty() {
        return Err(ControllerError::Validation("repository URL is required".to_string()));
    }

    if repository.branch.is_empty() {
        return Err(ControllerError::Validation("repository branch is required".to_string()));
    }

    // Validate environment
    let environment = &request.environment;
    if environment.name.is_empty() {
        return Err(ControllerError::Validation("environment name is required".to_string()));
    }

    if environment.start_command.is_empty() {
        return Err(ControllerError::Validation("start command is required".to_string()));
    }

    // Validate runtime config
    let runtime_config = &request.runtime_config;
    if runtime_config.instance_type.is_empty() {
        return Err(ControllerError::Validation("instance type is required".to_string()));
    }

    if runtime_config.region.is_empty() {
        return Err(ControllerError::Validation("region is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let application = deployment_service
        .create_application(&claims.sub, request)
        .await
        .map_err(|e| ControllerError::Service(e))?;

    Ok(Json(ApiResponse::success(application)))
}

#[instrument(skip(state, claims))]
pub async fn list_applications(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Query(pagination): Query<PaginationQuery>,
) -> ControllerResult<Json<ApiResponse<Vec<ApplicationResponse>>>> {
    pagination.validate().map_err(|e| {
        ControllerError::Validation(format!("pagination parameters are invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let applications = deployment_service
        .list_applications(&claims.sub, pagination)
        .await
        .map_err(|e| ControllerError::Service(e))?;

    Ok(Json(ApiResponse::success(applications)))
}

#[instrument(skip(state, claims, app_id))]
pub async fn get_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let application = deployment_service
        .get_application(&claims.sub, &app_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(application)))
}

#[instrument(skip(state, claims, app_id, request))]
pub async fn update_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
    Json(request): Json<CreateApplicationRequest>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("application data is invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let application = deployment_service
        .update_application(&claims.sub, &app_id, request)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(application)))
}

#[instrument(skip(state, claims, app_id))]
pub async fn delete_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    deployment_service
        .delete_application(&claims.sub, &app_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(())))
}

#[instrument(skip(state, claims, app_id, request))]
pub async fn trigger_deployment(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
    Json(request): Json<TriggerDeploymentRequest>,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("deployment request is invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployment = deployment_service
        .trigger_deployment(&claims.sub, &app_id, request)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(deployment)))
}

#[instrument(skip(state, claims, app_id))]
pub async fn list_deployments(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
    Query(pagination): Query<PaginationQuery>,
) -> ControllerResult<Json<ApiResponse<Vec<DeploymentResponse>>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    pagination.validate().map_err(|e| {
        ControllerError::Validation(format!("pagination parameters are invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployments = deployment_service
        .list_deployments(&claims.sub, &app_id, pagination)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(deployments)))
}

#[instrument(skip(state, claims, app_id, deployment_id))]
pub async fn get_deployment(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path((app_id, deployment_id)): Path<(String, String)>,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    if deployment_id.is_empty() {
        return Err(ControllerError::Validation("deployment_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployment = deployment_service
        .get_deployment(&claims.sub, &app_id, &deployment_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Deployment not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(deployment)))
}

#[instrument(skip(state, claims, app_id, deployment_id))]
pub async fn rollback_deployment(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path((app_id, deployment_id)): Path<(String, String)>,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    if deployment_id.is_empty() {
        return Err(ControllerError::Validation("deployment_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployment = deployment_service
        .rollback_deployment(&claims.sub, &app_id, &deployment_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Deployment not found".to_string()),
            _ => ControllerError::Service(e),
        })?;

    Ok(Json(ApiResponse::success(deployment)))
}
