fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::http::HeaderMap) -> impl futures::Future<Output = Result<Sse<impl futures::Stream<Item = Result<axum::response::sse::Event, Infallible>>>, ControllerError>> {stream_build_logs}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::http::HeaderMap) -> impl futures::Future<Output = Result<Sse<impl futures::Stream<Item = Result<axum::response::sse::Event, Infallible>>>, ControllerError>> {stream_build_logs}: Handler<_, _>
