{"rustc": 2830703817519440116, "features": "[\"chrono\", \"chrono-0_4\", \"default\"]", "declared_features": "[\"chrono\", \"chrono-0_4\", \"default\", \"hashable\", \"serde_path_to_error\", \"serde_with\", \"serde_with-3\", \"time-0_3\", \"uncapped_max_size\", \"uuid-0_8\", \"uuid-1\"]", "target": 5945215384148664543, "profile": 15657897354478470176, "path": 16943626853339797895, "deps": [[530211389790465181, "hex", false, 12173332956928825589], [966925859616469517, "ahash", false, 8732512724034190146], [2098583196738611028, "rand", false, 6346965316161359901], [3722963349756955755, "once_cell", false, 15293267452495451070], [8319709847752024821, "uuid", false, 7741643729679656481], [9689903380558560274, "serde", false, 11120990563308116688], [9897246384292347999, "chrono", false, 7994279097577605412], [10889494155287625682, "serde_bytes", false, 15839712490695431116], [12409575957772518135, "time", false, 9269897659778293610], [13077212702700853852, "base64", false, 14442517164307021658], [14483812548788871374, "indexmap", false, 11578274163333973057], [15367738274754116744, "serde_json", false, 8622871967636998780], [15977791033689874879, "bitvec", false, 11711905055008431444]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bson-4d3d91fd097bafeb\\dep-lib-bson", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}