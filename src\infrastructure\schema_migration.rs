use semver::Version;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use mongodb::{Collection, Database, IndexModel};
use bson::{doc, Document};
use tracing::{info, warn, error};
use crate::services::{ServiceError, ServiceResult};
use futures::TryStreamExt;

#[derive(Debug, Clone)]
pub enum MigrationOperation {
    CreateCollection {
        name: String,
        options: Option<mongodb::options::CreateCollectionOptions>,
    },
    CreateIndex {
        collection: String,
        index: IndexModel,
    },
    UpdateDocuments {
        collection: String,
        filter: Document,
        update: Document,
    },
    DropCollection {
        name: String,
    },
    RenameField {
        collection: String,
        old_field: String,
        new_field: String,
    },
    AddField {
        collection: String,
        field: String,
        default_value: bson::Bson,
    },
    RemoveField {
        collection: String,
        field: String,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Migration {
    pub version: Version,
    pub name: String,
    pub description: String,
    pub up_script: String,
    pub down_script: Option<String>,
    pub checksum: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationRecord {
    #[serde(rename = "_id")]
    pub id: String, // version string
    pub version: String,
    pub name: String,
    pub checksum: String,
    pub applied_at: DateTime<Utc>,
    pub execution_time_ms: u64,
    pub success: bool,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchemaVersion {
    #[serde(rename = "_id")]
    pub id: String,
    pub current_version: String,
    pub last_migration_at: DateTime<Utc>,
    pub migrations_applied: u32,
}

pub struct MigrationManager {
    database: Database,
    migrations: HashMap<Version, Migration>,
    migration_collection: Collection<MigrationRecord>,
    schema_collection: Collection<SchemaVersion>,
}

impl MigrationManager {
    pub fn new(database: Database) -> Self {
        let migration_collection = database.collection("_migrations");
        let schema_collection = database.collection("_schema_version");
        
        Self {
            database,
            migrations: HashMap::new(),
            migration_collection,
            schema_collection,
        }
    }

    pub fn register_migration(&mut self, migration: Migration) {
        info!("Registering migration: {} ({})", migration.version, migration.name);
        self.migrations.insert(migration.version.clone(), migration);
    }

    pub async fn get_current_version(&self) -> ServiceResult<Option<Version>> {
        let schema_doc = self.schema_collection
            .find_one(doc! { "_id": "current" }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        if let Some(schema) = schema_doc {
            let version_str = schema.current_version;
            let version = Version::parse(&version_str)
                .map_err(|e| ServiceError::Internal(format!("Invalid version format: {}", e)))?;
            Ok(Some(version))
        } else {
            Ok(None)
        }
    }

    pub async fn get_pending_migrations(&self) -> ServiceResult<Vec<Migration>> {
        let current_version = self.get_current_version().await?;
        
        let mut pending = Vec::new();
        let mut sorted_versions: Vec<_> = self.migrations.keys().collect();
        sorted_versions.sort();

        for version in sorted_versions {
            if let Some(current) = &current_version {
                if version > current {
                    pending.push(self.migrations[version].clone());
                }
            } else {
                // No current version, all migrations are pending
                pending.push(self.migrations[version].clone());
            }
        }

        Ok(pending)
    }

    pub async fn migrate_to_latest(&self) -> ServiceResult<Vec<MigrationRecord>> {
        let pending_migrations = self.get_pending_migrations().await?;
        
        if pending_migrations.is_empty() {
            info!("No pending migrations");
            return Ok(Vec::new());
        }

        info!("Applying {} pending migrations", pending_migrations.len());
        
        let mut applied_migrations = Vec::new();
        
        for migration in pending_migrations {
            let record = self.apply_migration(&migration).await?;
            applied_migrations.push(record);
        }

        Ok(applied_migrations)
    }

    pub async fn migrate_to_version(&self, target_version: Version) -> ServiceResult<Vec<MigrationRecord>> {
        let current_version = self.get_current_version().await?;
        
        if let Some(ref current) = current_version {
            if *current == target_version {
                info!("Already at target version: {}", target_version);
                return Ok(Vec::new());
            }

            if *current > target_version {
                return self.rollback_to_version(target_version).await;
            }
        }

        // Forward migration
        let mut migrations_to_apply = Vec::new();
        let mut sorted_versions: Vec<_> = self.migrations.keys().collect();
        sorted_versions.sort();

        for version in sorted_versions {
            if let Some(ref current) = current_version {
                if version > current && *version <= target_version {
                    migrations_to_apply.push(self.migrations[version].clone());
                }
            } else if *version <= target_version {
                migrations_to_apply.push(self.migrations[version].clone());
            }
        }

        let mut applied_migrations = Vec::new();
        
        for migration in migrations_to_apply {
            let record = self.apply_migration(&migration).await?;
            applied_migrations.push(record);
        }

        Ok(applied_migrations)
    }

    async fn apply_migration(&self, migration: &Migration) -> ServiceResult<MigrationRecord> {
        info!("Applying migration: {} ({})", migration.version, migration.name);
        
        let start_time = std::time::Instant::now();
        let mut success = false;
        let mut error_message = None;

        // Check if migration was already applied
        let existing = self.migration_collection
            .find_one(doc! { "_id": migration.version.to_string() }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        if existing.is_some() {
            warn!("Migration {} already applied, skipping", migration.version);
            return Err(ServiceError::Conflict(format!(
                "Migration {} already applied",
                migration.version
            )));
        }

        // Execute migration script
        match self.execute_migration_script(&migration.up_script).await {
            Ok(_) => {
                success = true;
                info!("Migration {} applied successfully", migration.version);
            }
            Err(e) => {
                error_message = Some(e.to_string());
                error!("Migration {} failed: {}", migration.version, e);
            }
        }

        let execution_time = start_time.elapsed().as_millis() as u64;

        // Record migration
        let record = MigrationRecord {
            id: migration.version.to_string(),
            version: migration.version.to_string(),
            name: migration.name.clone(),
            checksum: migration.checksum.clone(),
            applied_at: Utc::now(),
            execution_time_ms: execution_time,
            success,
            error_message: error_message.clone(),
        };

        self.migration_collection
            .insert_one(&record, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        if success {
            // Update schema version
            self.update_schema_version(&migration.version).await?;
        } else {
            return Err(ServiceError::Internal(format!(
                "Migration {} failed: {}",
                migration.version,
                error_message.unwrap_or_else(|| "Unknown error".to_string())
            )));
        }

        Ok(record)
    }

    async fn rollback_to_version(&self, target_version: Version) -> ServiceResult<Vec<MigrationRecord>> {
        let current_version = self.get_current_version().await?
            .ok_or_else(|| ServiceError::Internal("No current version found".to_string()))?;

        if current_version <= target_version {
            return Ok(Vec::new());
        }

        // Get migrations to rollback (in reverse order)
        let mut migrations_to_rollback = Vec::new();
        let mut sorted_versions: Vec<_> = self.migrations.keys().collect();
        sorted_versions.sort();
        sorted_versions.reverse();

        for version in sorted_versions {
            if *version > target_version && *version <= current_version {
                if let Some(migration) = self.migrations.get(version) {
                    if migration.down_script.is_some() {
                        migrations_to_rollback.push(migration.clone());
                    } else {
                        return Err(ServiceError::Internal(format!(
                            "Migration {} has no rollback script",
                            version
                        )));
                    }
                }
            }
        }

        let mut rollback_records = Vec::new();

        for migration in migrations_to_rollback {
            let record = self.rollback_migration(&migration).await?;
            rollback_records.push(record);
        }

        // Update schema version to target
        self.update_schema_version(&target_version).await?;

        Ok(rollback_records)
    }

    async fn rollback_migration(&self, migration: &Migration) -> ServiceResult<MigrationRecord> {
        info!("Rolling back migration: {} ({})", migration.version, migration.name);
        
        let down_script = migration.down_script.as_ref()
            .ok_or_else(|| ServiceError::Internal(format!(
                "Migration {} has no rollback script",
                migration.version
            )))?;

        let start_time = std::time::Instant::now();
        let mut success = false;
        let mut error_message = None;

        // Execute rollback script
        match self.execute_migration_script(down_script).await {
            Ok(_) => {
                success = true;
                info!("Migration {} rolled back successfully", migration.version);
            }
            Err(e) => {
                error_message = Some(e.to_string());
                error!("Migration {} rollback failed: {}", migration.version, e);
            }
        }

        let execution_time = start_time.elapsed().as_millis() as u64;

        if success {
            // Remove migration record
            self.migration_collection
                .delete_one(doc! { "_id": migration.version.to_string() }, None)
                .await
                .map_err(|e| ServiceError::Database(e))?;
        }

        let record = MigrationRecord {
            id: format!("rollback_{}", migration.version),
            version: migration.version.to_string(),
            name: format!("Rollback: {}", migration.name),
            checksum: migration.checksum.clone(),
            applied_at: Utc::now(),
            execution_time_ms: execution_time,
            success,
            error_message,
        };

        if !success {
            return Err(ServiceError::Internal(format!(
                "Migration {} rollback failed",
                migration.version
            )));
        }

        Ok(record)
    }

    async fn execute_migration_script(&self, script: &str) -> ServiceResult<()> {
        // Production-ready MongoDB migration script parser and executor
        let operations = self.parse_migration_script(script)?;

        for operation in operations {
            match operation {
                MigrationOperation::CreateCollection { name, options } => {
                    self.database.create_collection(&name, options).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Created collection: {}", name);
                }
                MigrationOperation::CreateIndex { collection, index } => {
                    let coll = self.database.collection::<Document>(&collection);
                    coll.create_index(index, None).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Created index on collection: {}", collection);
                }
                MigrationOperation::UpdateDocuments { collection, filter, update } => {
                    let coll = self.database.collection::<Document>(&collection);
                    let result = coll.update_many(filter, update, None).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Updated {} documents in collection: {}", result.modified_count, collection);
                }
                MigrationOperation::DropCollection { name } => {
                    let coll = self.database.collection::<Document>(&name);
                    coll.drop(None).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Dropped collection: {}", name);
                }
                MigrationOperation::RenameField { collection, old_field, new_field } => {
                    let coll = self.database.collection::<Document>(&collection);
                    let update = doc! { "$rename": { &old_field: &new_field } };
                    let result = coll.update_many(doc! {}, update, None).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Renamed field {} to {} in {} documents", old_field, new_field, result.modified_count);
                }
                MigrationOperation::AddField { collection, field, default_value } => {
                    let coll = self.database.collection::<Document>(&collection);
                    let filter = doc! { &field: { "$exists": false } };
                    let update = doc! { "$set": { &field: default_value } };
                    let result = coll.update_many(filter, update, None).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Added field {} to {} documents", field, result.modified_count);
                }
                MigrationOperation::RemoveField { collection, field } => {
                    let coll = self.database.collection::<Document>(&collection);
                    let update = doc! { "$unset": { &field: "" } };
                    let result = coll.update_many(doc! {}, update, None).await
                        .map_err(|e| ServiceError::Database(e))?;
                    info!("Removed field {} from {} documents", field, result.modified_count);
                }
            }
        }

        Ok(())
    }

    fn parse_migration_script(&self, script: &str) -> ServiceResult<Vec<MigrationOperation>> {
        let mut operations = Vec::new();

        for line in script.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with("//") {
                continue;
            }

            let operation = self.parse_migration_line(line)?;
            operations.push(operation);
        }

        Ok(operations)
    }

    fn parse_migration_line(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse different types of migration operations
        if line.starts_with("CREATE_COLLECTION") {
            self.parse_create_collection(line)
        } else if line.starts_with("CREATE_INDEX") {
            self.parse_create_index(line)
        } else if line.starts_with("UPDATE_DOCUMENTS") {
            self.parse_update_documents(line)
        } else if line.starts_with("DROP_COLLECTION") {
            self.parse_drop_collection(line)
        } else if line.starts_with("RENAME_FIELD") {
            self.parse_rename_field(line)
        } else if line.starts_with("ADD_FIELD") {
            self.parse_add_field(line)
        } else if line.starts_with("REMOVE_FIELD") {
            self.parse_remove_field(line)
        } else {
            Err(ServiceError::Internal(format!("Unknown migration operation: {}", line)))
        }
    }

    fn parse_create_collection(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse: CREATE_COLLECTION users {}
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() < 2 {
            return Err(ServiceError::Internal("Invalid CREATE_COLLECTION syntax".to_string()));
        }

        let name = parts[1].to_string();
        let options = if parts.len() > 2 {
            let options_str = parts[2..].join(" ");
            Some(serde_json::from_str(&options_str)
                .map_err(|e| ServiceError::Internal(format!("Invalid collection options: {}", e)))?)
        } else {
            None
        };

        Ok(MigrationOperation::CreateCollection { name, options })
    }

    fn parse_create_index(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse: CREATE_INDEX users {"email": 1} {"unique": true}
        let parts: Vec<&str> = line.splitn(4, ' ').collect();
        if parts.len() < 3 {
            return Err(ServiceError::Internal("Invalid CREATE_INDEX syntax".to_string()));
        }

        let collection = parts[1].to_string();
        let keys: Document = serde_json::from_str(parts[2])
            .map_err(|e| ServiceError::Internal(format!("Invalid index keys: {}", e)))?;

        let options = if parts.len() > 3 {
            let options_json: serde_json::Value = serde_json::from_str(parts[3])
                .map_err(|e| ServiceError::Internal(format!("Invalid index options: {}", e)))?;

            let mut index_options = mongodb::options::IndexOptions::default();
            if let Some(unique) = options_json.get("unique").and_then(|v| v.as_bool()) {
                index_options.unique = Some(unique);
            }
            if let Some(name) = options_json.get("name").and_then(|v| v.as_str()) {
                index_options.name = Some(name.to_string());
            }
            index_options
        } else {
            mongodb::options::IndexOptions::default()
        };

        let index = mongodb::IndexModel::builder()
            .keys(keys)
            .options(options)
            .build();

        Ok(MigrationOperation::CreateIndex { collection, index })
    }

    fn parse_update_documents(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse: UPDATE_DOCUMENTS users {"status": "old"} {"$set": {"status": "new"}}
        let parts: Vec<&str> = line.splitn(4, ' ').collect();
        if parts.len() < 4 {
            return Err(ServiceError::Internal("Invalid UPDATE_DOCUMENTS syntax".to_string()));
        }

        let collection = parts[1].to_string();
        let filter: Document = serde_json::from_str(parts[2])
            .map_err(|e| ServiceError::Internal(format!("Invalid filter: {}", e)))?;
        let update: Document = serde_json::from_str(parts[3])
            .map_err(|e| ServiceError::Internal(format!("Invalid update: {}", e)))?;

        Ok(MigrationOperation::UpdateDocuments { collection, filter, update })
    }

    fn parse_drop_collection(&self, line: &str) -> ServiceResult<MigrationOperation> {
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() < 2 {
            return Err(ServiceError::Internal("Invalid DROP_COLLECTION syntax".to_string()));
        }

        Ok(MigrationOperation::DropCollection { name: parts[1].to_string() })
    }

    fn parse_rename_field(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse: RENAME_FIELD users old_field new_field
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() < 4 {
            return Err(ServiceError::Internal("Invalid RENAME_FIELD syntax".to_string()));
        }

        Ok(MigrationOperation::RenameField {
            collection: parts[1].to_string(),
            old_field: parts[2].to_string(),
            new_field: parts[3].to_string(),
        })
    }

    fn parse_add_field(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse: ADD_FIELD users new_field "default_value"
        let parts: Vec<&str> = line.splitn(4, ' ').collect();
        if parts.len() < 4 {
            return Err(ServiceError::Internal("Invalid ADD_FIELD syntax".to_string()));
        }

        let default_value: bson::Bson = serde_json::from_str(parts[3])
            .map_err(|e| ServiceError::Internal(format!("Invalid default value: {}", e)))?;

        Ok(MigrationOperation::AddField {
            collection: parts[1].to_string(),
            field: parts[2].to_string(),
            default_value,
        })
    }

    fn parse_remove_field(&self, line: &str) -> ServiceResult<MigrationOperation> {
        // Parse: REMOVE_FIELD users old_field
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() < 3 {
            return Err(ServiceError::Internal("Invalid REMOVE_FIELD syntax".to_string()));
        }

        Ok(MigrationOperation::RemoveField {
            collection: parts[1].to_string(),
            field: parts[2].to_string(),
        })
    }

    async fn update_schema_version(&self, version: &Version) -> ServiceResult<()> {
        let schema_version = SchemaVersion {
            id: "current".to_string(),
            current_version: version.to_string(),
            last_migration_at: Utc::now(),
            migrations_applied: self.migration_collection
                .count_documents(doc! { "success": true }, None)
                .await
                .map_err(|e| ServiceError::Database(e))? as u32,
        };

        self.schema_collection
            .replace_one(
                doc! { "_id": "current" },
                &schema_version,
                mongodb::options::ReplaceOptions::builder().upsert(true).build(),
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Updated schema version to: {}", version);
        Ok(())
    }

    pub async fn get_migration_history(&self) -> ServiceResult<Vec<MigrationRecord>> {
        let mut cursor = self.migration_collection
            .find(doc! {}, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let mut records = Vec::new();
        while let Some(record) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
            records.push(record);
        }

        // Sort by applied_at
        records.sort_by(|a, b| a.applied_at.cmp(&b.applied_at));
        Ok(records)
    }
}
