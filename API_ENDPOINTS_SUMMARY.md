# Achidas Platform API Endpoints Summary

## Build Status: ✅ SUCCESSFUL
The platform now builds successfully with the new multi-tier hosting architecture.

## Key Features Implemented
- **Multi-tier hosting**: Shared, Dedicated, Enterprise
- **Intelligent hosting plans** with dynamic pricing
- **Simplified application creation** for easy sharing
- **Comprehensive Vultr API integration**
- **Production-ready infrastructure** with circuit breakers, rate limiting, and monitoring

## API Structure

### Public Routes (No Authentication)
```
GET  /api/v1/public/health                           - Health check
POST /api/v1/public/auth/register                    - User registration
POST /api/v1/public/auth/login                       - User login
POST /api/v1/public/auth/refresh                     - Refresh token

GET  /api/v1/public/african-regions                  - List African regions
GET  /api/v1/public/african-regions/{region_code}    - Get specific region
GET  /api/v1/public/african-regions/{region_code}/pricing - Get region pricing
GET  /api/v1/public/countries                        - List countries
GET  /api/v1/public/countries/{country_code}/regions - Get country regions

GET  /api/v1/public/hosting/plans                    - List hosting plans
GET  /api/v1/public/hosting/plans/recommend          - Get recommended plan
GET  /api/v1/public/hosting/plans/{plan_name}        - Get specific plan

POST /api/v1/public/webhooks/github                  - GitHub webhook
POST /api/v1/public/webhooks/gitlab                  - GitLab webhook
POST /api/v1/public/webhooks/git/{app_id}           - Application webhook
GET  /api/v1/public/webhooks/health                  - Webhook health check
POST /api/v1/public/webhooks                         - Generic webhook
```

### User Routes (Authentication Required)
```
# User Profile
GET  /api/v1/users/profile                           - Get user profile

# Applications (Render-like Platform)
GET  /api/v1/users/applications                      - List applications
POST /api/v1/users/applications                      - Create application (full)
POST /api/v1/users/applications/simple               - Create application (simplified)
GET  /api/v1/users/applications/{app_id}            - Get application
PUT  /api/v1/users/applications/{app_id}            - Update application
DELETE /api/v1/users/applications/{app_id}          - Delete application

# Deployments
POST /api/v1/users/applications/{app_id}/deploy     - Trigger deployment
GET  /api/v1/users/applications/{app_id}/deployments - List deployments
GET  /api/v1/users/applications/{app_id}/deployments/{deployment_id} - Get deployment
POST /api/v1/users/applications/{app_id}/deployments/{deployment_id}/rollback - Rollback

# Environment & Secrets
GET  /api/v1/users/environment-groups               - List environment groups
POST /api/v1/users/environment-groups               - Create environment group
GET  /api/v1/users/environment-groups/{group_id}   - Get environment group
POST /api/v1/users/environment-groups/{group_id}/variables - Set variable
DELETE /api/v1/users/environment-groups/{group_id}/variables/{key} - Delete variable
POST /api/v1/users/environment-groups/{group_id}/secrets - Set secret
DELETE /api/v1/users/environment-groups/{group_id}/secrets/{key} - Delete secret
POST /api/v1/users/environment-groups/{group_id}/link - Link to application

# Logs & Monitoring
GET  /api/v1/users/applications/{app_id}/logs       - Get application logs
GET  /api/v1/users/applications/{app_id}/logs/stream - Stream application logs
GET  /api/v1/users/applications/{app_id}/deployments/{deployment_id}/logs - Get deployment logs
GET  /api/v1/users/builds/{job_id}/logs             - Get build logs
GET  /api/v1/users/builds/{job_id}/logs/stream      - Stream build logs

# Account & Billing
GET  /api/v1/users/account                          - Get account info
GET  /api/v1/users/account/usage                    - Get account usage
GET  /api/v1/users/billing                          - Get billing info
GET  /api/v1/users/billing/invoices                 - List invoices
GET  /api/v1/users/billing/invoices/{id}           - Get invoice
GET  /api/v1/users/billing/usage                    - Get usage
GET  /api/v1/users/billing/calculate/{plan_name}   - Calculate pricing

# Infrastructure Management
GET  /api/v1/users/instances                        - List instances
GET  /api/v1/users/instances/{id}                   - Get instance
DELETE /api/v1/users/instances/{id}                 - Delete instance
POST /api/v1/users/instances/{id}/start             - Start instance
POST /api/v1/users/instances/{id}/stop              - Stop instance
POST /api/v1/users/instances/{id}/restart           - Restart instance

# SSH Keys
GET  /api/v1/users/ssh-keys                         - List SSH keys
POST /api/v1/users/ssh-keys                         - Create SSH key
GET  /api/v1/users/ssh-keys/{id}                    - Get SSH key
PATCH /api/v1/users/ssh-keys/{id}                   - Update SSH key
DELETE /api/v1/users/ssh-keys/{id}                  - Delete SSH key

# Hosting Services
GET  /api/v1/users/hosting/servers/vultr            - List Vultr servers
GET  /api/v1/users/hosting/servers/vultr/{instance_id} - Get server details
POST /api/v1/users/hosting/servers/import/{instance_id} - Import server
GET  /api/v1/users/hosting/servers/setup/{instance_id} - Get server setup
POST /api/v1/users/hosting/deploy/user/{user_id}    - Deploy user
GET  /api/v1/users/hosting/infrastructure/status    - Get infrastructure status
GET  /api/v1/users/hosting/scaling/recommendations  - Get scaling recommendations
POST /api/v1/users/hosting/scaling/execute          - Execute scaling action
```

### Admin Routes (Admin Authentication Required)
```
# User Management
GET  /api/v1/admin/users                            - List all users
GET  /api/v1/admin/users/{id}                       - Get user
POST /api/v1/admin/users/{id}/suspend               - Suspend user
POST /api/v1/admin/users/{id}/activate              - Activate user

# System Management
GET  /api/v1/admin/instances                        - List all instances
GET  /api/v1/admin/billing/overview                 - Billing overview
GET  /api/v1/admin/metrics                          - Get metrics
POST /api/v1/admin/analytics/profit                 - Calculate profit analysis

# Vultr Infrastructure (200+ endpoints)
# Plans & Regions
GET  /api/v1/admin/vultr/plans                      - List plans
GET  /api/v1/admin/vultr/plans-metal                - List metal plans
GET  /api/v1/admin/vultr/regions                    - List regions
GET  /api/v1/admin/vultr/regions/{region_id}/availability - List available plans
GET  /api/v1/admin/vultr/os                         - List OS
GET  /api/v1/admin/vultr/backups                    - List backups

# Cloud Instances (Full CRUD + Operations)
GET  /api/v1/admin/vultr/cloud/instances            - List instances
POST /api/v1/admin/vultr/cloud/instances            - Create instance
GET  /api/v1/admin/vultr/cloud/instances/{id}       - Get instance
PATCH /api/v1/admin/vultr/cloud/instances/{id}      - Update instance
DELETE /api/v1/admin/vultr/cloud/instances/{id}     - Delete instance
POST /api/v1/admin/vultr/cloud/instances/{id}/start - Start instance
POST /api/v1/admin/vultr/cloud/instances/{id}/stop  - Stop instance
POST /api/v1/admin/vultr/cloud/instances/{id}/reboot - Reboot instance
# ... and 50+ more instance operations

# Bare Metal Servers (Full CRUD + Operations)
GET  /api/v1/admin/vultr/servers                    - List bare metal servers
POST /api/v1/admin/vultr/servers                    - Create bare metal server
GET  /api/v1/admin/vultr/servers/{id}               - Get bare metal server
PUT  /api/v1/admin/vultr/servers/{id}               - Update bare metal server
DELETE /api/v1/admin/vultr/servers/{id}             - Delete bare metal server
# ... and 20+ more bare metal operations

# Block Storage (Full CRUD + Operations)
GET  /api/v1/admin/vultr/storage/blocks             - List block storage
POST /api/v1/admin/vultr/storage/blocks             - Create block storage
GET  /api/v1/admin/vultr/storage/blocks/{id}        - Get block storage
PUT  /api/v1/admin/vultr/storage/blocks/{id}        - Update block storage
DELETE /api/v1/admin/vultr/storage/blocks/{id}      - Delete block storage
POST /api/v1/admin/vultr/storage/blocks/{id}/attach - Attach block storage
POST /api/v1/admin/vultr/storage/blocks/{id}/detach - Detach block storage

# Kubernetes Clusters (Full Management)
GET  /api/v1/admin/vultr/kubernetes/clusters        - List clusters
POST /api/v1/admin/vultr/kubernetes/clusters        - Create cluster
GET  /api/v1/admin/vultr/kubernetes/clusters/{id}   - Get cluster
PUT  /api/v1/admin/vultr/kubernetes/clusters/{id}   - Update cluster
DELETE /api/v1/admin/vultr/kubernetes/clusters/{id} - Delete cluster
# ... and 15+ more Kubernetes operations

# Load Balancers (Full Management)
GET  /api/v1/admin/vultr/load-balancers             - List load balancers
POST /api/v1/admin/vultr/load-balancers             - Create load balancer
GET  /api/v1/admin/vultr/load-balancers/{id}        - Get load balancer
PATCH /api/v1/admin/vultr/load-balancers/{id}       - Update load balancer
DELETE /api/v1/admin/vultr/load-balancers/{id}      - Delete load balancer
# ... and 10+ more load balancer operations

# Managed Databases (Full Management)
GET  /api/v1/admin/vultr/databases                  - List databases
POST /api/v1/admin/vultr/databases                  - Create database
GET  /api/v1/admin/vultr/databases/{id}             - Get database
PUT  /api/v1/admin/vultr/databases/{id}             - Update database
DELETE /api/v1/admin/vultr/databases/{id}           - Delete database
# ... and 20+ more database operations

# DNS Management (Full Management)
GET  /api/v1/admin/vultr/dns/domains                - List DNS domains
POST /api/v1/admin/vultr/dns/domains                - Create DNS domain
GET  /api/v1/admin/vultr/dns/domains/{domain}       - Get DNS domain
PUT  /api/v1/admin/vultr/dns/domains/{domain}       - Update DNS domain
DELETE /api/v1/admin/vultr/dns/domains/{domain}     - Delete DNS domain
# ... and 15+ more DNS operations

# And many more: CDN, Container Registry, Firewall, ISO, Object Storage, 
# VPC, Reserved IPs, Snapshots, SSH Keys, Startup Scripts, etc.
```

## Hosting Plans Available

### Shared Hosting (Pay-as-you-go)
- **starter**: $0.50/month, 10% CPU/memory, 10GB bandwidth
- **basic**: $1.00/month, 20% CPU/memory, 25GB bandwidth  
- **standard**: $1.50/month, 30% CPU/memory, 50GB bandwidth
- **pro**: $2.50/month, 50% CPU/memory, 100GB bandwidth

### Dedicated Hosting
- **dedicated-small**: $5.00/month, vc2-1c-1gb server
- **dedicated-medium**: $10.00/month, vc2-1c-2gb server
- **dedicated-large**: $15.00/month, vc2-2c-2gb server

### Enterprise Hosting  
- **enterprise-starter**: $12.00/month, vhf-1c-2gb server
- **enterprise-business**: $24.00/month, vhf-2c-4gb server
- **enterprise-premium**: $48.00/month, vhf-4c-8gb server

## Total API Endpoints: 300+
- **Public**: 15 endpoints
- **User**: 50+ endpoints  
- **Admin**: 250+ endpoints (including comprehensive Vultr API)

## Next Steps for Users
1. **Register**: `POST /api/v1/public/auth/register`
2. **Login**: `POST /api/v1/public/auth/login`
3. **Choose Plan**: `GET /api/v1/public/hosting/plans`
4. **Deploy App**: `POST /api/v1/users/applications/simple`
5. **Monitor**: Use logs and monitoring endpoints
