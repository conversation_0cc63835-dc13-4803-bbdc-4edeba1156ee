use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AfricanRegion {
    pub code: String,
    pub name: String,
    pub country: String,
    pub country_code: String,
    pub city: String,
    pub vultr_region: Option<String>, // Maps to actual Vultr region
    pub latitude: f64,
    pub longitude: f64,
    pub available: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RegionResponse {
    pub code: String,
    pub name: String,
    pub country: String,
    pub country_code: String,
    pub city: String,
    pub latitude: f64,
    pub longitude: f64,
    pub available: bool,
    pub pricing_tier: String, // "standard", "premium", "economy"
}

impl From<AfricanRegion> for RegionResponse {
    fn from(region: AfricanRegion) -> Self {
        Self {
            code: region.code.clone(),
            name: region.name,
            country: region.country,
            country_code: region.country_code,
            city: region.city,
            latitude: region.latitude,
            longitude: region.longitude,
            available: region.available,
            pricing_tier: get_pricing_tier(&region.code),
        }
    }
}

pub fn get_pricing_tier(region_code: &str) -> String {
    match region_code {
        "za-jhb" | "za-cpt" | "eg-cai" | "ng-lag" => "standard".to_string(),
        "ke-nai" | "gh-acc" | "ma-cas" => "premium".to_string(),
        _ => "economy".to_string(),
    }
}

pub fn get_african_regions() -> Vec<AfricanRegion> {
    vec![
        // South Africa
        AfricanRegion {
            code: "za-jhb".to_string(),
            name: "Johannesburg".to_string(),
            country: "South Africa".to_string(),
            country_code: "ZA".to_string(),
            city: "Johannesburg".to_string(),
            vultr_region: Some("ewr".to_string()), // Map to closest Vultr region
            latitude: -26.2041,
            longitude: 28.0473,
            available: true,
        },
        AfricanRegion {
            code: "za-cpt".to_string(),
            name: "Cape Town".to_string(),
            country: "South Africa".to_string(),
            country_code: "ZA".to_string(),
            city: "Cape Town".to_string(),
            vultr_region: Some("ewr".to_string()),
            latitude: -33.9249,
            longitude: 18.4241,
            available: true,
        },
        
        // Nigeria
        AfricanRegion {
            code: "ng-lag".to_string(),
            name: "Lagos".to_string(),
            country: "Nigeria".to_string(),
            country_code: "NG".to_string(),
            city: "Lagos".to_string(),
            vultr_region: Some("fra".to_string()), // Map to Frankfurt (closest)
            latitude: 6.5244,
            longitude: 3.3792,
            available: true,
        },
        AfricanRegion {
            code: "ng-abj".to_string(),
            name: "Abuja".to_string(),
            country: "Nigeria".to_string(),
            country_code: "NG".to_string(),
            city: "Abuja".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 9.0765,
            longitude: 7.3986,
            available: true,
        },
        
        // Kenya
        AfricanRegion {
            code: "ke-nai".to_string(),
            name: "Nairobi".to_string(),
            country: "Kenya".to_string(),
            country_code: "KE".to_string(),
            city: "Nairobi".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: -1.2921,
            longitude: 36.8219,
            available: true,
        },
        
        // Egypt
        AfricanRegion {
            code: "eg-cai".to_string(),
            name: "Cairo".to_string(),
            country: "Egypt".to_string(),
            country_code: "EG".to_string(),
            city: "Cairo".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 30.0444,
            longitude: 31.2357,
            available: true,
        },
        
        // Ghana
        AfricanRegion {
            code: "gh-acc".to_string(),
            name: "Accra".to_string(),
            country: "Ghana".to_string(),
            country_code: "GH".to_string(),
            city: "Accra".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 5.6037,
            longitude: -0.1870,
            available: true,
        },
        
        // Morocco
        AfricanRegion {
            code: "ma-cas".to_string(),
            name: "Casablanca".to_string(),
            country: "Morocco".to_string(),
            country_code: "MA".to_string(),
            city: "Casablanca".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 33.5731,
            longitude: -7.5898,
            available: true,
        },
        
        // Ethiopia
        AfricanRegion {
            code: "et-add".to_string(),
            name: "Addis Ababa".to_string(),
            country: "Ethiopia".to_string(),
            country_code: "ET".to_string(),
            city: "Addis Ababa".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 9.1450,
            longitude: 40.4897,
            available: false, // Coming soon
        },
        
        // Tanzania
        AfricanRegion {
            code: "tz-dar".to_string(),
            name: "Dar es Salaam".to_string(),
            country: "Tanzania".to_string(),
            country_code: "TZ".to_string(),
            city: "Dar es Salaam".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: -6.7924,
            longitude: 39.2083,
            available: false, // Coming soon
        },
        
        // Uganda
        AfricanRegion {
            code: "ug-kam".to_string(),
            name: "Kampala".to_string(),
            country: "Uganda".to_string(),
            country_code: "UG".to_string(),
            city: "Kampala".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 0.3476,
            longitude: 32.5825,
            available: false, // Coming soon
        },
        
        // Rwanda
        AfricanRegion {
            code: "rw-kig".to_string(),
            name: "Kigali".to_string(),
            country: "Rwanda".to_string(),
            country_code: "RW".to_string(),
            city: "Kigali".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: -1.9441,
            longitude: 30.0619,
            available: false, // Coming soon
        },
        
        // Senegal
        AfricanRegion {
            code: "sn-dkr".to_string(),
            name: "Dakar".to_string(),
            country: "Senegal".to_string(),
            country_code: "SN".to_string(),
            city: "Dakar".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 14.7167,
            longitude: -17.4677,
            available: false, // Coming soon
        },
        
        // Ivory Coast
        AfricanRegion {
            code: "ci-abj".to_string(),
            name: "Abidjan".to_string(),
            country: "Ivory Coast".to_string(),
            country_code: "CI".to_string(),
            city: "Abidjan".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 5.3600,
            longitude: -4.0083,
            available: false, // Coming soon
        },
        
        // Algeria
        AfricanRegion {
            code: "dz-alg".to_string(),
            name: "Algiers".to_string(),
            country: "Algeria".to_string(),
            country_code: "DZ".to_string(),
            city: "Algiers".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 36.7538,
            longitude: 3.0588,
            available: false, // Coming soon
        },
        
        // Tunisia
        AfricanRegion {
            code: "tn-tun".to_string(),
            name: "Tunis".to_string(),
            country: "Tunisia".to_string(),
            country_code: "TN".to_string(),
            city: "Tunis".to_string(),
            vultr_region: Some("fra".to_string()),
            latitude: 36.8065,
            longitude: 10.1815,
            available: false, // Coming soon
        },
    ]
}

pub fn get_region_by_code(code: &str) -> Option<AfricanRegion> {
    get_african_regions().into_iter().find(|r| r.code == code)
}

pub fn get_available_regions() -> Vec<AfricanRegion> {
    get_african_regions().into_iter().filter(|r| r.available).collect()
}

pub fn get_regions_by_country(country_code: &str) -> Vec<AfricanRegion> {
    get_african_regions().into_iter().filter(|r| r.country_code == country_code).collect()
}

// Map African region to actual Vultr region for server provisioning
pub fn map_to_vultr_region(african_region_code: &str) -> Option<String> {
    get_region_by_code(african_region_code)
        .and_then(|r| r.vultr_region)
}
