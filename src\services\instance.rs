use crate::{
    database::Database,
    models::{
        CreateInstanceRequest, Instance, InstanceResponse, InstanceStatus, PaginatedResponse,
        PaginationQuery,
    },
    services::{ServiceError, ServiceResult},
    utils::Pagination,
    vultr::{CreateVultrInstanceRequest, VultrClient},
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use mongodb::Collection;
use tracing::{error, info, instrument};
use futures::TryStreamExt;

pub struct InstanceService<'a> {
    instances: Collection<Instance>,
    vultr_client: &'a VultrClient,
}

impl<'a> InstanceService<'a> {
    pub fn new(database: &Database, vultr_client: &'a VultrClient) -> Self {
        Self {
            instances: database.collection("instances"),
            vultr_client,
        }
    }

    #[instrument(skip(self))]
    pub async fn list_user_instances(
        &self,
        user_id: &str,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<InstanceResponse>> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self
            .instances
            .count_documents(doc! { "user_id": user_object_id }, None)
            .await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let instances: Vec<Instance> = self
            .instances
            .find(doc! { "user_id": user_object_id }, None)
            .await?
            .try_collect()
            .await?;

        let instance_responses: Vec<InstanceResponse> = instances
            .into_iter()
            .map(|instance| instance.into())
            .collect();

        Ok(PaginatedResponse {
            data: instance_responses,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_user_instance(
        &self,
        user_id: &str,
        instance_id: &str,
    ) -> ServiceResult<InstanceResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        let instance_object_id = ObjectId::parse_str(instance_id)
            .map_err(|_| ServiceError::Validation("Invalid instance ID".to_string()))?;

        let instance = self
            .instances
            .find_one(
                doc! { 
                    "_id": instance_object_id,
                    "user_id": user_object_id 
                },
                None,
            )
            .await?
            .ok_or_else(|| ServiceError::NotFound("Instance not found".to_string()))?;

        Ok(instance.into())
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(
        &self,
        user_id: &str,
        request: CreateInstanceRequest,
    ) -> ServiceResult<InstanceResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Extract validated fields (these should always be Some due to controller validation)
        let name = request.name.as_ref().ok_or_else(|| {
            ServiceError::Validation("Name is required".to_string())
        })?;
        let region = request.region.as_ref().ok_or_else(|| {
            ServiceError::Validation("Region is required".to_string())
        })?;
        let plan = request.plan.as_ref().ok_or_else(|| {
            ServiceError::Validation("Plan is required".to_string())
        })?;
        let os = request.os.as_ref().ok_or_else(|| {
            ServiceError::Validation("OS is required".to_string())
        })?;

        // Create Vultr instance request
        let vultr_request = CreateVultrInstanceRequest {
            region: region.clone(),
            plan: plan.clone(),
            os_id: Some(os.parse().map_err(|_| {
                ServiceError::Validation("Invalid OS ID".to_string())
            })?),
            image_id: None,
            label: Some(name.clone()),
            hostname: Some(name.clone()),
            tag: None,
            user_data: None,
            ssh_keys: request.ssh_key_ids.clone(),
            startup_script_id: request.startup_script_id.clone(),
            firewall_group_id: None,
            enable_ipv6: Some(true),
            enable_private_network: Some(false),
            attach_private_network: None,
            enable_ddos_protection: Some(false),
            backups: None,
            app_id: None,
            snapshot_id: None,
            iso_id: None,
            script_id: None,
            activation_email: Some(false),
            ddos_protection: Some(false),
            enable_vpc: Some(false),
            attach_vpc: None,
            tags: request.tags.clone(),
        };

        // Create instance in Vultr
        let vultr_instance = self
            .vultr_client
            .create_instance(vultr_request)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Vultr API error: {}", e)))?;

        // Get plan details for cost calculation
        let plans = self
            .vultr_client
            .list_plans()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to get plans: {}", e)))?;

        let plan_details = plans
            .iter()
            .find(|p| p.id == *plan)
            .ok_or_else(|| ServiceError::Validation("Invalid plan ID".to_string()))?;

        // Create instance record in database
        let now = Utc::now();
        let instance = Instance {
            id: None,
            user_id: user_object_id,
            vultr_instance_id: vultr_instance.id.clone(),
            name: name.clone(),
            region: region.clone(),
            plan: plan.clone(),
            os: os.clone(),
            status: InstanceStatus::Pending,
            ip_address: vultr_instance.main_ip,
            internal_ip: vultr_instance.internal_ip,
            vcpu_count: vultr_instance.vcpu_count,
            ram: vultr_instance.ram,
            disk: vultr_instance.disk,
            bandwidth: vultr_instance.allowed_bandwidth,
            monthly_cost: plan_details.monthly_cost,
            created_at: now,
            updated_at: now,
            tags: request.tags.unwrap_or_default(),
        };

        let result = self.instances.insert_one(&instance, None).await?;
        let instance_id = result.inserted_id.as_object_id().unwrap();

        let mut created_instance = instance;
        created_instance.id = Some(instance_id);

        info!(
            "Instance created successfully: {} (Vultr ID: {})",
            created_instance.name, created_instance.vultr_instance_id
        );

        Ok(created_instance.into())
    }

    #[instrument(skip(self, update_data))]
    pub async fn update_instance(
        &self,
        user_id: &str,
        instance_id: &str,
        update_data: serde_json::Value,
    ) -> ServiceResult<InstanceResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        let instance_object_id = ObjectId::parse_str(instance_id)
            .map_err(|_| ServiceError::Validation("Invalid instance ID".to_string()))?;

        // Find the instance
        let _instance = self
            .instances
            .find_one(
                doc! { 
                    "_id": instance_object_id,
                    "user_id": user_object_id 
                },
                None,
            )
            .await?
            .ok_or_else(|| ServiceError::NotFound("Instance not found".to_string()))?;

        // Update allowed fields (name, tags)
        let now = Utc::now();
        let mut update_doc = doc! { "updated_at": now };

        if let Some(name) = update_data.get("name").and_then(|v| v.as_str()) {
            update_doc.insert("name", name);
        }

        if let Some(tags) = update_data.get("tags").and_then(|v| v.as_array()) {
            let tag_strings: Vec<String> = tags
                .iter()
                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                .collect();
            update_doc.insert("tags", tag_strings);
        }

        // Update in database
        self.instances
            .update_one(
                doc! { "_id": instance_object_id },
                doc! { "$set": update_doc },
                None,
            )
            .await?;

        // Return updated instance
        self.get_user_instance(user_id, instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, user_id: &str, instance_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        let instance_object_id = ObjectId::parse_str(instance_id)
            .map_err(|_| ServiceError::Validation("Invalid instance ID".to_string()))?;

        // Find the instance
        let instance = self
            .instances
            .find_one(
                doc! { 
                    "_id": instance_object_id,
                    "user_id": user_object_id 
                },
                None,
            )
            .await?
            .ok_or_else(|| ServiceError::NotFound("Instance not found".to_string()))?;

        // Delete from Vultr
        self.vultr_client
            .delete_instance(&instance.vultr_instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Vultr API error: {}", e)))?;

        // Update status in database
        self.instances
            .update_one(
                doc! { "_id": instance_object_id },
                doc! {
                    "$set": {
                        "status": "Destroyed",
                        "updated_at": Utc::now()
                    }
                },
                None,
            )
            .await?;

        info!("Instance deleted successfully: {}", instance.vultr_instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, user_id: &str, instance_id: &str) -> ServiceResult<()> {
        let instance = self.get_instance_for_action(user_id, instance_id).await?;

        self.vultr_client
            .start_instance(&instance.vultr_instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Vultr API error: {}", e)))?;

        info!("Instance started successfully: {}", instance.vultr_instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, user_id: &str, instance_id: &str) -> ServiceResult<()> {
        let instance = self.get_instance_for_action(user_id, instance_id).await?;

        self.vultr_client
            .stop_instance(&instance.vultr_instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Vultr API error: {}", e)))?;

        info!("Instance stopped successfully: {}", instance.vultr_instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, user_id: &str, instance_id: &str) -> ServiceResult<()> {
        let instance = self.get_instance_for_action(user_id, instance_id).await?;

        self.vultr_client
            .restart_instance(&instance.vultr_instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Vultr API error: {}", e)))?;

        info!("Instance restarted successfully: {}", instance.vultr_instance_id);
        Ok(())
    }

    async fn get_instance_for_action(
        &self,
        user_id: &str,
        instance_id: &str,
    ) -> ServiceResult<Instance> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        let instance_object_id = ObjectId::parse_str(instance_id)
            .map_err(|_| ServiceError::Validation("Invalid instance ID".to_string()))?;

        self.instances
            .find_one(
                doc! { 
                    "_id": instance_object_id,
                    "user_id": user_object_id 
                },
                None,
            )
            .await?
            .ok_or_else(|| ServiceError::NotFound("Instance not found".to_string()))
    }
}
