use crate::{
    controllers::{success_response, ControllerResult},
    models::{InstanceResponse, PaginatedResponse, PaginationQuery, UserProfile},
    services::admin::AdminService,
    AppState,
};
use axum::{
    extract::{Path, Query, State},
    Json,
};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state))]
pub async fn list_users(
    State(state): State<Arc<AppState>>,
    Query(pagination): Query<PaginationQuery>,
) -> ControllerResult<Json<crate::models::ApiResponse<PaginatedResponse<UserProfile>>>> {
    pagination.validate().map_err(|e| {
        crate::controllers::ControllerError::Validation(format!("pagination parameters are invalid: {}", e))
    })?;

    let admin_service = AdminService::new(&state.database, &state.config);
    let users = admin_service.list_all_users(pagination).await?;

    Ok(success_response(users))
}

#[instrument(skip(state))]
pub async fn get_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    let admin_service = AdminService::new(&state.database, &state.config);
    let user = admin_service.get_user_by_id(&user_id).await?;

    Ok(success_response(user))
}

#[instrument(skip(state))]
pub async fn suspend_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let admin_service = AdminService::new(&state.database, &state.config);
    admin_service.suspend_user(&user_id).await?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn activate_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let admin_service = AdminService::new(&state.database, &state.config);
    admin_service.activate_user(&user_id).await?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_all_instances(
    State(state): State<Arc<AppState>>,
    Query(pagination): Query<PaginationQuery>,
) -> ControllerResult<Json<crate::models::ApiResponse<PaginatedResponse<InstanceResponse>>>> {
    pagination.validate().map_err(|e| {
        crate::controllers::ControllerError::Validation(format!("Validation failed: {}", e))
    })?;

    let admin_service = AdminService::new(&state.database, &state.config);
    let instances = admin_service.list_all_instances(pagination).await?;

    Ok(success_response(instances))
}

#[instrument(skip(state))]
pub async fn billing_overview(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<serde_json::Value>>> {
    let admin_service = AdminService::new(&state.database, &state.config);
    let overview = admin_service.get_billing_overview().await?;

    Ok(success_response(overview))
}

#[instrument(skip(state))]
pub async fn get_metrics(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<serde_json::Value>>> {
    let admin_service = AdminService::new(&state.database, &state.config);
    let metrics = admin_service.get_system_metrics().await?;

    Ok(success_response(metrics))
}
