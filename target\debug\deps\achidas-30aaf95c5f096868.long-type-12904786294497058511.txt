fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {delete_environment_variable}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {delete_environment_variable}: Handler<_, _>
