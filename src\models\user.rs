use bson::oid::ObjectId;
use serde::{Deserialize, Serialize, Serializer};
use validator::Validate;

// Custom serializer to convert bson::DateTime to readable string
fn serialize_datetime<S>(date: &bson::DateTime, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let chrono_date = date.to_chrono();
    let formatted = chrono_date.format("%Y-%m-%dT%H:%M:%SZ").to_string();
    serializer.serialize_str(&formatted)
}

// Custom serializer for optional datetime
fn serialize_optional_datetime<S>(date: &Option<bson::DateTime>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    match date {
        Some(dt) => {
            let chrono_date = dt.to_chrono();
            let formatted = chrono_date.format("%Y-%m-%dT%H:%M:%SZ").to_string();
            serializer.serialize_str(&formatted)
        }
        None => serializer.serialize_none(),
    }
}
use axum::{
    async_trait,
    extract::FromRequestParts,
    http::{request::Parts, StatusCode},
    RequestPartsExt,
};
use axum_extra::{
    headers::{authorization::Bearer, Authorization},
    TypedHeader,
};
use jsonwebtoken::{decode, DecodingKey, Validation};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub email: String,
    pub password_hash: String,
    pub first_name: String,
    pub last_name: String,
    pub company: Option<String>,
    pub role: UserRole,
    pub status: UserStatus,
    pub vultr_subaccount_id: Option<String>,
    pub created_at: bson::DateTime,
    pub updated_at: bson::DateTime,
    #[serde(skip_serializing_if = "Option::is_none", default)]
    pub last_login: Option<bson::DateTime>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UserRole {
    Admin,
    User,
    Billing,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UserStatus {
    Active,
    Inactive,
    Suspended,
    PendingVerification,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub email: Option<String>,
    pub password: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub company: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginRequest {
    pub email: Option<String>,
    pub password: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserProfile,
    #[serde(serialize_with = "serialize_datetime")]
    pub expires_at: bson::DateTime,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserProfile {
    pub id: String,
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub company: Option<String>,
    pub role: UserRole,
    pub status: UserStatus,
    #[serde(serialize_with = "serialize_datetime")]
    pub created_at: bson::DateTime,
    #[serde(serialize_with = "serialize_optional_datetime", skip_serializing_if = "Option::is_none", default)]
    pub last_login: Option<bson::DateTime>,
}

impl From<User> for UserProfile {
    fn from(user: User) -> Self {
        Self {
            id: user.id.map(|id| id.to_hex()).unwrap_or_default(),
            email: user.email,
            first_name: user.first_name,
            last_name: user.last_name,
            company: user.company,
            role: user.role,
            status: user.status,
            created_at: user.created_at,
            last_login: user.last_login,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // User ID
    pub email: String,
    pub role: UserRole,
    pub exp: usize, // Expiration time
    pub iat: usize, // Issued at
}

#[async_trait]
impl<S> FromRequestParts<S> for Claims
where
    S: Send + Sync,
{
    type Rejection = StatusCode;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        // Extract the authorization header
        let TypedHeader(Authorization(bearer)) = parts
            .extract::<TypedHeader<Authorization<Bearer>>>()
            .await
            .map_err(|_| StatusCode::UNAUTHORIZED)?;

        // Decode the JWT token
        // In a real application, you'd get the secret from configuration
        let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret".to_string());
        let decoding_key = DecodingKey::from_secret(secret.as_ref());

        let token_data = decode::<Claims>(
            bearer.token(),
            &decoding_key,
            &Validation::default(),
        )
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

        Ok(token_data.claims)
    }
}
