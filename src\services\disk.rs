use crate::{
    config::Config,
    database::Database,
    infrastructure::{
        CircuitBreakerService, CircuitBreakerAware, MetricsService, RateLimiterService,
        ChunkProcessor, ChunkProcessorConfig
    },
    models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},
    services::{ServiceError, ServiceResult},
    vultr::VultrClient,
    with_circuit_breaker,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::Collection;
use serde::{Deserialize, Serialize};
use tracing::{error, info, instrument, warn};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateDiskRequest {
    pub name: String,
    pub size_gb: i32,
    pub mount_path: String,
    pub service_id: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateDiskRequest {
    pub name: Option<String>,
    pub size_gb: Option<i32>,
    pub mount_path: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DiskResponse {
    pub id: String,
    pub name: String,
    pub size_gb: i32,
    pub mount_path: String,
    pub service_id: String,
    pub status: DiskStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RestoreSnapshotRequest {
    pub snapshot_key: String,
}

pub struct DiskService<'a> {
    disks: Collection<Disk>,
    disk_snapshots: Collection<DiskSnapshot>,
    vultr_client: &'a VultrClient,
    config: &'a Config,
    circuit_breaker: CircuitBreakerService,
    metrics: MetricsService,
    rate_limiter: RateLimiterService,
    chunk_processor: ChunkProcessor,
}

impl<'a> DiskService<'a> {
    pub fn new(database: &Database, vultr_client: &'a VultrClient, config: &'a Config) -> Self {
        let circuit_breaker = CircuitBreakerService::new();
        let metrics = MetricsService::new();
        let rate_limiter = RateLimiterService::new();
        let chunk_processor = ChunkProcessor::new(ChunkProcessorConfig::default());

        Self {
            disks: database.collection("disks"),
            disk_snapshots: database.collection("disk_snapshots"),
            vultr_client,
            config,
            circuit_breaker,
            metrics,
            rate_limiter,
            chunk_processor,
        }
    }

    /// Create a new persistent disk
    #[instrument(skip(self))]
    pub async fn create_disk(&self, user_id: &str, request: CreateDiskRequest) -> ServiceResult<DiskResponse> {
        // Rate limit disk creation
        self.rate_limiter.check_api_rate_limit(user_id).await?;

        // Validate disk size (minimum 1GB, maximum 1TB)
        if request.size_gb < 1 || request.size_gb > 1024 {
            return Err(ServiceError::Validation("Disk size must be between 1GB and 1TB".to_string()));
        }

        // Validate mount path
        if !request.mount_path.starts_with('/') {
            return Err(ServiceError::Validation("Mount path must be absolute".to_string()));
        }

        // Validate service exists and user has access
        self.validate_service_access(user_id, &request.service_id).await?;

        // Create disk in infrastructure
        let infrastructure_disk_id = self.create_infrastructure_disk(&request).await?;

        let disk = Disk {
            id: None,
            name: request.name.clone(),
            size_gb: request.size_gb,
            mount_path: request.mount_path.clone(),
            service_id: ObjectId::parse_str(&request.service_id)
                .map_err(|_| ServiceError::Validation("Invalid service ID".to_string()))?,
            user_id: ObjectId::parse_str(user_id)
                .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?,
            infrastructure_id: infrastructure_disk_id,
            status: DiskStatus::Creating,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = self.disks.insert_one(&disk, None).await
            .map_err(|e| ServiceError::Database(e))?;

        let disk_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get disk ID".to_string()))?;

        // Start async disk provisioning
        self.provision_disk(disk_id).await?;

        self.metrics.record_custom_metric("disks_created", 1.0, vec![
            ("size_gb".to_string(), request.size_gb.to_string())
        ]);

        Ok(DiskResponse {
            id: disk_id.to_hex(),
            name: disk.name,
            size_gb: disk.size_gb,
            mount_path: disk.mount_path,
            service_id: request.service_id,
            status: disk.status,
            created_at: disk.created_at,
            updated_at: disk.updated_at,
        })
    }

    /// Update disk configuration
    #[instrument(skip(self))]
    pub async fn update_disk(
        &self,
        user_id: &str,
        disk_id: &str,
        request: UpdateDiskRequest,
    ) -> ServiceResult<DiskResponse> {
        let disk_object_id = ObjectId::parse_str(disk_id)
            .map_err(|_| ServiceError::Validation("Invalid disk ID".to_string()))?;

        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Get existing disk
        let existing_disk = self.disks
            .find_one(doc! { "_id": disk_object_id, "user_id": user_object_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound("Disk not found".to_string()))?;

        let mut update_doc = doc! { "$set": { "updated_at": Utc::now() } };
        let mut needs_infrastructure_update = false;

        if let Some(name) = &request.name {
            update_doc.get_document_mut("$set").unwrap().insert("name", name);
        }

        if let Some(size_gb) = request.size_gb {
            // Validate size increase only
            if size_gb <= existing_disk.size_gb {
                return Err(ServiceError::Validation("Disk size can only be increased".to_string()));
            }
            if size_gb > 1024 {
                return Err(ServiceError::Validation("Maximum disk size is 1TB".to_string()));
            }
            
            update_doc.get_document_mut("$set").unwrap().insert("size_gb", size_gb);
            needs_infrastructure_update = true;
        }

        if let Some(mount_path) = &request.mount_path {
            if !mount_path.starts_with('/') {
                return Err(ServiceError::Validation("Mount path must be absolute".to_string()));
            }
            update_doc.get_document_mut("$set").unwrap().insert("mount_path", mount_path);
            needs_infrastructure_update = true;
        }

        // Update database
        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.disks
                    .update_one(doc! { "_id": disk_object_id }, update_doc, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok::<(), ServiceError>(())
            }
        )?;

        // Update infrastructure if needed
        if needs_infrastructure_update {
            self.update_infrastructure_disk(&existing_disk.infrastructure_id, &request).await?;
        }

        self.get_disk(user_id, disk_id).await
    }

    /// Get disk by ID
    #[instrument(skip(self))]
    pub async fn get_disk(&self, user_id: &str, disk_id: &str) -> ServiceResult<DiskResponse> {
        let disk_object_id = ObjectId::parse_str(disk_id)
            .map_err(|_| ServiceError::Validation("Invalid disk ID".to_string()))?;

        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let disk = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.disks
                    .find_one(doc! { "_id": disk_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Disk not found".to_string()))
            }
        )?;

        Ok(DiskResponse {
            id: disk.id.unwrap().to_hex(),
            name: disk.name,
            size_gb: disk.size_gb,
            mount_path: disk.mount_path,
            service_id: disk.service_id.to_hex(),
            status: disk.status,
            created_at: disk.created_at,
            updated_at: disk.updated_at,
        })
    }

    /// Delete disk
    #[instrument(skip(self))]
    pub async fn delete_disk(&self, user_id: &str, disk_id: &str) -> ServiceResult<()> {
        let disk_object_id = ObjectId::parse_str(disk_id)
            .map_err(|_| ServiceError::Validation("Invalid disk ID".to_string()))?;

        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Get disk
        let disk = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.disks
                    .find_one(doc! { "_id": disk_object_id, "user_id": user_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?
                    .ok_or_else(|| ServiceError::NotFound("Disk not found".to_string()))
            }
        )?;

        // Delete from infrastructure
        self.delete_infrastructure_disk(&disk.infrastructure_id).await?;

        // Delete from database
        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.disks
                    .delete_one(doc! { "_id": disk_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok::<(), ServiceError>(())
            }
        )?;

        self.metrics.record_custom_metric("disks_deleted", 1.0, vec![]);
        info!("Disk {} deleted successfully", disk_id);
        Ok(())
    }

    /// List snapshots for a disk
    #[instrument(skip(self))]
    pub async fn list_snapshots(&self, user_id: &str, disk_id: &str) -> ServiceResult<Vec<DiskSnapshot>> {
        let disk_object_id = ObjectId::parse_str(disk_id)
            .map_err(|_| ServiceError::Validation("Invalid disk ID".to_string()))?;

        // Verify user owns the disk
        self.get_disk(user_id, disk_id).await?;

        let snapshots = with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                let mut cursor = self.disk_snapshots
                    .find(doc! { "disk_id": disk_object_id }, None)
                    .await
                    .map_err(|e| ServiceError::Database(e))?;

                let mut snapshots = Vec::new();
                while let Some(snapshot) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
                    snapshots.push(snapshot);
                }
                Ok::<Vec<DiskSnapshot>, ServiceError>(snapshots)
            }
        )?;

        Ok(snapshots)
    }

    /// Restore disk from snapshot
    #[instrument(skip(self))]
    pub async fn restore_snapshot(
        &self,
        user_id: &str,
        disk_id: &str,
        request: RestoreSnapshotRequest,
    ) -> ServiceResult<DiskResponse> {
        // Verify user owns the disk
        let disk = self.get_disk(user_id, disk_id).await?;

        // Validate snapshot key (in production, would verify it exists and hasn't expired)
        if request.snapshot_key.is_empty() {
            return Err(ServiceError::Validation("Snapshot key is required".to_string()));
        }

        // Restore in infrastructure
        self.restore_infrastructure_snapshot(&disk.id, &request.snapshot_key).await?;

        // Update disk status
        let disk_object_id = ObjectId::parse_str(disk_id)
            .map_err(|_| ServiceError::Validation("Invalid disk ID".to_string()))?;

        with_circuit_breaker!(
            self.circuit_breaker,
            "database",
            {
                self.disks
                    .update_one(
                        doc! { "_id": disk_object_id },
                        doc! { "$set": { "status": "restoring", "updated_at": Utc::now() } },
                        None,
                    )
                    .await
                    .map_err(|e| ServiceError::Database(e))?;
                Ok::<(), ServiceError>(())
            }
        )?;

        self.metrics.record_custom_metric("disk_snapshots_restored", 1.0, vec![]);
        info!("Disk {} restore initiated from snapshot", disk_id);

        self.get_disk(user_id, disk_id).await
    }

    // Private helper methods
    async fn validate_service_access(&self, user_id: &str, service_id: &str) -> ServiceResult<()> {
        // In production, would verify the service exists and user has access
        info!("Validating service access for user {} to service {}", user_id, service_id);
        Ok(())
    }

    async fn create_infrastructure_disk(&self, request: &CreateDiskRequest) -> ServiceResult<String> {
        // Create disk in Vultr infrastructure
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                // In production, would create actual Vultr block storage
                info!("Creating infrastructure disk: {} ({}GB)", request.name, request.size_gb);
                Ok(Uuid::new_v4().to_string())
            }
        )
    }

    async fn provision_disk(&self, disk_id: ObjectId) -> ServiceResult<()> {
        // Start async provisioning process
        tokio::spawn(async move {
            // Simulate provisioning time
            tokio::time::sleep(std::time::Duration::from_secs(30)).await;
            info!("Disk {} provisioning completed", disk_id);
        });

        Ok(())
    }

    async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> ServiceResult<()> {
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                info!("Updating infrastructure disk: {}", infrastructure_id);
                // In production, would update actual Vultr block storage
                Ok(())
            }
        )
    }

    async fn delete_infrastructure_disk(&self, infrastructure_id: &str) -> ServiceResult<()> {
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                info!("Deleting infrastructure disk: {}", infrastructure_id);
                // In production, would delete actual Vultr block storage
                Ok(())
            }
        )
    }

    async fn restore_infrastructure_snapshot(&self, disk_id: &str, snapshot_key: &str) -> ServiceResult<()> {
        with_circuit_breaker!(
            self.circuit_breaker,
            "vultr_api",
            {
                info!("Restoring disk {} from snapshot: {}", disk_id, snapshot_key);
                // In production, would restore actual Vultr block storage snapshot
                Ok(())
            }
        )
    }
}

impl<'a> CircuitBreakerAware for DiskService<'a> {
    fn circuit_breaker(&self) -> &CircuitBreakerService {
        &self.circuit_breaker
    }
}
