fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::<PERSON><PERSON><SetEnvironmentVariableRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {set_environment_variable}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::extract::Path<std::string::String>, axum::<PERSON><PERSON><SetEnvironmentVariableRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {set_environment_variable}: Handler<_, _>
