fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, axum::<PERSON><PERSON><{type error}>) -> impl futures::Future<Output = Result<axum::<PERSON>son<ApiResponse<{type error}>>, ControllerError>> {trigger_deployment}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<{type error}>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<{type error}>>, ControllerError>> {trigger_deployment}: Handler<_, _>
