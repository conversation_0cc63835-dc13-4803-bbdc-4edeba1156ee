use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>},
    models::{ApiResponse, Claims},
    AppState,
};
use axum::{extract::State, Json};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::instrument;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FullAccountInfo {
    pub account: AccountDetails,
    pub billing: BillingDetails,
    pub bandwidth: BandwidthDetails,
    pub bgp: Option<BgpDetails>,
    pub summary: AccountSummary,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountDetails {
    pub name: String,
    pub email: String,
    pub acls: Vec<String>,
    pub balance: f64,
    pub pending_charges: f64,
    pub last_payment_date: Option<String>,
    pub last_payment_amount: Option<f64>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BillingDetails {
    pub current_balance: f64,
    pub pending_charges: f64,
    pub monthly_estimate: f64,
    pub last_payment: Option<PaymentInfo>,
    pub recent_invoices: Vec<InvoiceInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentInfo {
    pub date: String,
    pub amount: f64,
    pub method: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InvoiceInfo {
    pub id: String,
    pub date: String,
    pub amount: f64,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BandwidthDetails {
    pub current_month: BandwidthUsage,
    pub previous_month: BandwidthUsage,
    pub projected_month: BandwidthUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BandwidthUsage {
    pub gb_in: f64,
    pub gb_out: f64,
    pub total_gb: f64,
    pub overage_cost: f64,
    pub instance_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BgpDetails {
    pub enabled: bool,
    pub asn: Option<u32>,
    pub password: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountSummary {
    pub total_instances: u32,
    pub total_bare_metal: u32,
    pub total_storage: u32,
    pub total_monthly_cost: f64,
    pub active_regions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedServers {
    pub instances: Vec<ServerInfo>,
    pub bare_metal: Vec<ServerInfo>,
    pub summary: ServerSummary,
    pub by_region: HashMap<String, RegionSummary>,
    pub by_status: HashMap<String, u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerInfo {
    pub id: String,
    pub label: String,
    pub server_type: String, // "instance" or "bare_metal"
    pub status: String,
    pub region: String,
    pub plan: String,
    pub os: String,
    pub main_ip: String,
    pub ram: String,
    pub disk: String,
    pub cpu_count: u32,
    pub monthly_cost: f64,
    pub date_created: String,
    pub features: Vec<String>,
    pub tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerSummary {
    pub total_servers: u32,
    pub total_instances: u32,
    pub total_bare_metal: u32,
    pub running_servers: u32,
    pub stopped_servers: u32,
    pub total_monthly_cost: f64,
    pub total_ram_gb: u32,
    pub total_cpu_cores: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionSummary {
    pub region_code: String,
    pub region_name: String,
    pub server_count: u32,
    pub monthly_cost: f64,
    pub server_types: HashMap<String, u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComputeSummary {
    pub total_compute_power: ComputePower,
    pub utilization: ComputeUtilization,
    pub cost_breakdown: CostBreakdown,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComputePower {
    pub total_vcpu: u32,
    pub total_ram_gb: u32,
    pub total_storage_gb: u32,
    pub total_bandwidth_tb: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComputeUtilization {
    pub average_cpu_usage: f64,
    pub average_ram_usage: f64,
    pub average_bandwidth_usage: f64,
    pub efficiency_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CostBreakdown {
    pub compute_cost: f64,
    pub storage_cost: f64,
    pub bandwidth_cost: f64,
    pub total_monthly_cost: f64,
    pub cost_per_vcpu: f64,
    pub cost_per_gb_ram: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedBilling {
    pub current_period: BillingPeriod,
    pub previous_period: BillingPeriod,
    pub cost_trends: CostTrends,
    pub upcoming_charges: Vec<UpcomingCharge>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BillingPeriod {
    pub start_date: String,
    pub end_date: String,
    pub total_cost: f64,
    pub breakdown: HashMap<String, f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CostTrends {
    pub monthly_growth: f64,
    pub cost_per_server_trend: f64,
    pub efficiency_trend: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpcomingCharge {
    pub service: String,
    pub amount: f64,
    pub due_date: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InfrastructureOverview {
    pub global_presence: GlobalPresence,
    pub resource_distribution: ResourceDistribution,
    pub performance_metrics: PerformanceMetrics,
    pub capacity_planning: CapacityPlanning,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalPresence {
    pub active_regions: Vec<RegionInfo>,
    pub total_regions: u32,
    pub geographic_distribution: HashMap<String, u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionInfo {
    pub code: String,
    pub name: String,
    pub country: String,
    pub server_count: u32,
    pub services: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceDistribution {
    pub by_service_type: HashMap<String, u32>,
    pub by_plan_type: HashMap<String, u32>,
    pub by_os: HashMap<String, u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub average_uptime: f64,
    pub response_times: HashMap<String, f64>,
    pub error_rates: HashMap<String, f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CapacityPlanning {
    pub current_utilization: f64,
    pub projected_growth: f64,
    pub recommended_scaling: Vec<String>,
    pub cost_optimization: Vec<String>,
}

#[instrument(skip(state))]
pub async fn get_full_account_info(
    State(state): State<Arc<AppState>>,
    _claims: Claims,
) -> ControllerResult<Json<ApiResponse<FullAccountInfo>>> {
    // Get account information from Vultr
    let account = state
        .vultr_client
        .get_account()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get account info: {}", e)))?;

    // Get bandwidth information
    let bandwidth = state
        .vultr_client
        .get_account_bandwidth()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get bandwidth info: {}", e)))?;

    // Get BGP information (optional)
    let bgp = state
        .vultr_client
        .get_account_bgp()
        .await
        .ok();

    // Get instances and bare metal for summary
    let instances = state
        .vultr_client
        .list_instances()
        .await
        .unwrap_or_default();

    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .unwrap_or_default();

    // Create aggregated response
    let full_account_info = FullAccountInfo {
        account: AccountDetails {
            name: account.name,
            email: account.email,
            acls: account.acls,
            balance: account.balance,
            pending_charges: account.pending_charges,
            last_payment_date: account.last_payment_date,
            last_payment_amount: account.last_payment_amount,
        },
        billing: BillingDetails {
            current_balance: account.balance,
            pending_charges: account.pending_charges,
            monthly_estimate: account.pending_charges * 30.0 / 7.0, // Rough estimate
            last_payment: account.last_payment_date.map(|date| PaymentInfo {
                date,
                amount: account.last_payment_amount.unwrap_or(0.0),
                method: "Credit Card".to_string(),
            }),
            recent_invoices: vec![], // Would need to call billing API
        },
        bandwidth: BandwidthDetails {
            current_month: BandwidthUsage {
                gb_in: bandwidth.current_month_to_date.gb_in,
                gb_out: bandwidth.current_month_to_date.gb_out,
                total_gb: bandwidth.current_month_to_date.gb_in + bandwidth.current_month_to_date.gb_out,
                overage_cost: bandwidth.current_month_to_date.overage_cost,
                instance_count: bandwidth.current_month_to_date.total_instance_count,
            },
            previous_month: BandwidthUsage {
                gb_in: bandwidth.previous_month.gb_in,
                gb_out: bandwidth.previous_month.gb_out,
                total_gb: bandwidth.previous_month.gb_in + bandwidth.previous_month.gb_out,
                overage_cost: bandwidth.previous_month.overage_cost,
                instance_count: bandwidth.previous_month.total_instance_count,
            },
            projected_month: BandwidthUsage {
                gb_in: bandwidth.current_month_projected.gb_in,
                gb_out: bandwidth.current_month_projected.gb_out,
                total_gb: bandwidth.current_month_projected.gb_in + bandwidth.current_month_projected.gb_out,
                overage_cost: bandwidth.current_month_projected.overage_cost,
                instance_count: bandwidth.current_month_projected.total_instance_count,
            },
        },
        bgp: bgp.map(|b| BgpDetails {
            enabled: b.enabled,
            asn: Some(b.asn),
            password: Some(b.password),
        }),
        summary: AccountSummary {
            total_instances: instances.len() as u32,
            total_bare_metal: bare_metals.len() as u32,
            total_storage: 0, // Would need to call storage API
            total_monthly_cost: account.pending_charges,
            active_regions: instances.iter()
                .map(|i| i.region.clone())
                .chain(bare_metals.iter().map(|b| b.region.clone()))
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect(),
        },
    };

    Ok(success_response(full_account_info))
}

#[instrument(skip(state))]
pub async fn get_aggregated_servers(
    State(state): State<Arc<AppState>>,
    _claims: Claims,
) -> ControllerResult<Json<ApiResponse<AggregatedServers>>> {
    // Get instances and bare metal servers
    let instances = state
        .vultr_client
        .list_instances()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get instances: {}", e)))?;

    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get bare metal: {}", e)))?;

    // Convert instances to ServerInfo
    let instance_servers: Vec<ServerInfo> = instances
        .iter()
        .map(|i| ServerInfo {
            id: i.id.clone(),
            label: i.label.clone(),
            server_type: "instance".to_string(),
            status: i.status.clone(),
            region: i.region.clone(),
            plan: i.plan.clone(),
            os: i.os.clone(),
            main_ip: i.main_ip.clone(),
            ram: format!("{} MB", i.ram),
            disk: format!("{} GB", i.disk),
            cpu_count: i.vcpu_count,
            monthly_cost: i.monthly_cost.unwrap_or(0.0),
            date_created: i.date_created.clone(),
            features: i.features.clone(),
            tags: i.tags.clone(),
        })
        .collect();

    // Convert bare metal to ServerInfo
    let bare_metal_servers: Vec<ServerInfo> = bare_metals
        .iter()
        .map(|b| ServerInfo {
            id: b.id.clone(),
            label: b.label.clone(),
            server_type: "bare_metal".to_string(),
            status: b.status.clone(),
            region: b.region.clone(),
            plan: b.plan.clone(),
            os: b.os.clone(),
            main_ip: b.main_ip.clone(),
            ram: b.ram.clone(),
            disk: b.disk.clone(),
            cpu_count: b.cpu_count,
            monthly_cost: b.monthly_cost.unwrap_or(0.0),
            date_created: b.date_created.clone(),
            features: b.features.clone(),
            tags: b.tags.clone(),
        })
        .collect();

    // Create region summary
    let mut by_region: HashMap<String, RegionSummary> = HashMap::new();
    let mut by_status: HashMap<String, u32> = HashMap::new();

    // Process all servers for summaries
    let all_servers: Vec<&ServerInfo> = instance_servers.iter().chain(bare_metal_servers.iter()).collect();

    for server in &all_servers {
        // Region summary
        let region_entry = by_region.entry(server.region.clone()).or_insert(RegionSummary {
            region_code: server.region.clone(),
            region_name: server.region.clone(), // Would need region mapping
            server_count: 0,
            monthly_cost: 0.0,
            server_types: HashMap::new(),
        });
        region_entry.server_count += 1;
        region_entry.monthly_cost += server.monthly_cost;
        *region_entry.server_types.entry(server.server_type.clone()).or_insert(0) += 1;

        // Status summary
        *by_status.entry(server.status.clone()).or_insert(0) += 1;
    }

    let total_monthly_cost: f64 = all_servers.iter().map(|s| s.monthly_cost).sum();
    let total_ram_gb: u32 = all_servers.iter().map(|s| {
        // Parse RAM from string like "32768 MB" or "32 GB"
        if s.ram.contains("GB") {
            s.ram.replace(" GB", "").parse::<u32>().unwrap_or(0)
        } else if s.ram.contains("MB") {
            s.ram.replace(" MB", "").parse::<u32>().unwrap_or(0) / 1024
        } else {
            0
        }
    }).sum();
    let total_cpu_cores: u32 = all_servers.iter().map(|s| s.cpu_count).sum();

    let aggregated_servers = AggregatedServers {
        instances: instance_servers,
        bare_metal: bare_metal_servers,
        summary: ServerSummary {
            total_servers: all_servers.len() as u32,
            total_instances: instances.len() as u32,
            total_bare_metal: bare_metals.len() as u32,
            running_servers: by_status.get("active").unwrap_or(&0) + by_status.get("running").unwrap_or(&0),
            stopped_servers: by_status.get("stopped").unwrap_or(&0) + by_status.get("halted").unwrap_or(&0),
            total_monthly_cost,
            total_ram_gb,
            total_cpu_cores,
        },
        by_region,
        by_status,
    };

    Ok(success_response(aggregated_servers))
}

#[instrument(skip(state))]
pub async fn get_compute_summary(
    State(state): State<Arc<AppState>>,
    _claims: Claims,
) -> ControllerResult<Json<ApiResponse<ComputeSummary>>> {
    // Get instances and bare metal for compute calculations
    let instances = state
        .vultr_client
        .list_instances()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get instances: {}", e)))?;

    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get bare metal: {}", e)))?;

    // Calculate total compute power
    let total_vcpu: u32 = instances.iter().map(|i| i.vcpu_count).sum::<u32>() +
                         bare_metals.iter().map(|b| b.cpu_count).sum::<u32>();

    let total_ram_gb: u32 = instances.iter().map(|i| i.ram / 1024).sum::<u32>() +
                           bare_metals.iter().map(|b| {
                               if b.ram.contains("GB") {
                                   b.ram.replace(" GB", "").replace(" MB", "").parse::<u32>().unwrap_or(0) / 1024
                               } else {
                                   b.ram.replace(" MB", "").parse::<u32>().unwrap_or(0) / 1024
                               }
                           }).sum::<u32>();

    let total_storage_gb: u32 = instances.iter().map(|i| i.disk).sum::<u32>() +
                               bare_metals.iter().map(|b| {
                                   // Parse disk like "2x 240GB SSD" or "500 GB"
                                   if b.disk.contains("x") && b.disk.contains("GB") {
                                       let parts: Vec<&str> = b.disk.split("x").collect();
                                       if parts.len() == 2 {
                                           let count = parts[0].trim().parse::<u32>().unwrap_or(1);
                                           let size = parts[1].replace("GB", "").replace(" SSD", "").replace(" HDD", "").trim().parse::<u32>().unwrap_or(0);
                                           count * size
                                       } else {
                                           0
                                       }
                                   } else if b.disk.contains("GB") {
                                       b.disk.replace(" GB", "").parse::<u32>().unwrap_or(0)
                                   } else {
                                       0
                                   }
                               }).sum::<u32>();

    let total_monthly_cost: f64 = instances.iter().map(|i| i.monthly_cost.unwrap_or(0.0)).sum::<f64>() +
                                 bare_metals.iter().map(|b| b.monthly_cost.unwrap_or(0.0)).sum::<f64>();

    let compute_summary = ComputeSummary {
        total_compute_power: ComputePower {
            total_vcpu,
            total_ram_gb,
            total_storage_gb,
            total_bandwidth_tb: 10.0, // Would need to calculate from bandwidth API
        },
        utilization: ComputeUtilization {
            average_cpu_usage: 65.0, // Would need monitoring data
            average_ram_usage: 70.0, // Would need monitoring data
            average_bandwidth_usage: 45.0, // Would need monitoring data
            efficiency_score: 75.0, // Calculated based on utilization
        },
        cost_breakdown: CostBreakdown {
            compute_cost: total_monthly_cost * 0.8, // Estimate
            storage_cost: total_monthly_cost * 0.15, // Estimate
            bandwidth_cost: total_monthly_cost * 0.05, // Estimate
            total_monthly_cost,
            cost_per_vcpu: if total_vcpu > 0 { total_monthly_cost / total_vcpu as f64 } else { 0.0 },
            cost_per_gb_ram: if total_ram_gb > 0 { total_monthly_cost / total_ram_gb as f64 } else { 0.0 },
        },
        recommendations: vec![
            "Consider upgrading underutilized instances to more cost-effective plans".to_string(),
            "Enable auto-scaling for variable workloads".to_string(),
            "Review storage usage and consider block storage for large data".to_string(),
        ],
    };

    Ok(success_response(compute_summary))
}

#[instrument(skip(state))]
pub async fn get_aggregated_billing(
    State(state): State<Arc<AppState>>,
    _claims: Claims,
) -> ControllerResult<Json<ApiResponse<AggregatedBilling>>> {
    // Get account and bandwidth for billing calculations
    let account = state
        .vultr_client
        .get_account()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get account: {}", e)))?;

    let bandwidth = state
        .vultr_client
        .get_account_bandwidth()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get bandwidth: {}", e)))?;

    let current_date = chrono::Utc::now();
    let previous_month = current_date - chrono::Duration::days(30);

    let aggregated_billing = AggregatedBilling {
        current_period: BillingPeriod {
            start_date: current_date.format("%Y-%m-01").to_string(),
            end_date: current_date.format("%Y-%m-%d").to_string(),
            total_cost: account.pending_charges,
            breakdown: {
                let mut breakdown = HashMap::new();
                breakdown.insert("compute".to_string(), account.pending_charges * 0.7);
                breakdown.insert("storage".to_string(), account.pending_charges * 0.2);
                breakdown.insert("bandwidth".to_string(), account.pending_charges * 0.1);
                breakdown
            },
        },
        previous_period: BillingPeriod {
            start_date: previous_month.format("%Y-%m-01").to_string(),
            end_date: previous_month.format("%Y-%m-%d").to_string(),
            total_cost: account.pending_charges * 0.9, // Estimate
            breakdown: {
                let mut breakdown = HashMap::new();
                let prev_cost = account.pending_charges * 0.9;
                breakdown.insert("compute".to_string(), prev_cost * 0.7);
                breakdown.insert("storage".to_string(), prev_cost * 0.2);
                breakdown.insert("bandwidth".to_string(), prev_cost * 0.1);
                breakdown
            },
        },
        cost_trends: CostTrends {
            monthly_growth: 10.0, // 10% growth
            cost_per_server_trend: 5.0, // 5% increase per server
            efficiency_trend: -2.0, // 2% improvement in efficiency
        },
        upcoming_charges: vec![
            UpcomingCharge {
                service: "Compute Instances".to_string(),
                amount: account.pending_charges * 0.7,
                due_date: (current_date + chrono::Duration::days(7)).format("%Y-%m-%d").to_string(),
            },
            UpcomingCharge {
                service: "Storage".to_string(),
                amount: account.pending_charges * 0.2,
                due_date: (current_date + chrono::Duration::days(7)).format("%Y-%m-%d").to_string(),
            },
        ],
    };

    Ok(success_response(aggregated_billing))
}

#[instrument(skip(state))]
pub async fn get_infrastructure_overview(
    State(state): State<Arc<AppState>>,
    _claims: Claims,
) -> ControllerResult<Json<ApiResponse<InfrastructureOverview>>> {
    // Get instances and bare metal for infrastructure overview
    let instances = state
        .vultr_client
        .list_instances()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get instances: {}", e)))?;

    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get bare metal: {}", e)))?;

    // Get regions
    let regions = state
        .vultr_client
        .list_regions()
        .await
        .unwrap_or_default();

    // Calculate active regions
    let active_region_codes: std::collections::HashSet<String> = instances.iter()
        .map(|i| i.region.clone())
        .chain(bare_metals.iter().map(|b| b.region.clone()))
        .collect();

    let active_regions: Vec<RegionInfo> = regions.iter()
        .filter(|r| active_region_codes.contains(&r.id))
        .map(|r| {
            let server_count = instances.iter().filter(|i| i.region == r.id).count() +
                              bare_metals.iter().filter(|b| b.region == r.id).count();
            RegionInfo {
                code: r.id.clone(),
                name: r.city.clone(),
                country: r.country.clone(),
                server_count: server_count as u32,
                services: vec!["Compute".to_string(), "Storage".to_string()],
            }
        })
        .collect();

    let infrastructure_overview = InfrastructureOverview {
        global_presence: GlobalPresence {
            active_regions,
            total_regions: active_region_codes.len() as u32,
            geographic_distribution: {
                let mut geo_dist = HashMap::new();
                for region in &regions {
                    if active_region_codes.contains(&region.id) {
                        *geo_dist.entry(region.continent.clone()).or_insert(0) += 1;
                    }
                }
                geo_dist
            },
        },
        resource_distribution: ResourceDistribution {
            by_service_type: {
                let mut service_types = HashMap::new();
                service_types.insert("Web Service".to_string(), instances.len() as u32);
                service_types.insert("Bare Metal".to_string(), bare_metals.len() as u32);
                service_types
            },
            by_plan_type: {
                let mut plan_types = HashMap::new();
                for instance in &instances {
                    *plan_types.entry(instance.plan.clone()).or_insert(0) += 1;
                }
                for bare_metal in &bare_metals {
                    *plan_types.entry(bare_metal.plan.clone()).or_insert(0) += 1;
                }
                plan_types
            },
            by_os: {
                let mut os_types = HashMap::new();
                for instance in &instances {
                    *os_types.entry(instance.os.clone()).or_insert(0) += 1;
                }
                for bare_metal in &bare_metals {
                    *os_types.entry(bare_metal.os.clone()).or_insert(0) += 1;
                }
                os_types
            },
        },
        performance_metrics: PerformanceMetrics {
            average_uptime: 99.9,
            response_times: {
                let mut response_times = HashMap::new();
                response_times.insert("API".to_string(), 150.0);
                response_times.insert("Web".to_string(), 200.0);
                response_times
            },
            error_rates: {
                let mut error_rates = HashMap::new();
                error_rates.insert("API".to_string(), 0.1);
                error_rates.insert("Web".to_string(), 0.05);
                error_rates
            },
        },
        capacity_planning: CapacityPlanning {
            current_utilization: 75.0,
            projected_growth: 20.0,
            recommended_scaling: vec![
                "Add 2 more instances in high-traffic regions".to_string(),
                "Consider load balancer for better distribution".to_string(),
            ],
            cost_optimization: vec![
                "Migrate low-usage instances to smaller plans".to_string(),
                "Implement auto-scaling to reduce idle costs".to_string(),
            ],
        },
    };

    Ok(success_response(infrastructure_overview))
}
