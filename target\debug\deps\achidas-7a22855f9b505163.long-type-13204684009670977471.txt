fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {delete_application}
fn(axum::extract::State<Arc<AppState>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<()>>, ControllerError>> {delete_application}: Handler<_, _>
