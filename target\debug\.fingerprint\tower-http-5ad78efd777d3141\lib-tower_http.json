{"rustc": 2830703817519440116, "features": "[\"cors\", \"default\", \"timeout\", \"tokio\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 2241668132362809309, "path": 3526564220616807389, "deps": [[784494742817713399, "tower_service", false, 13980336227272009489], [1906322745568073236, "pin_project_lite", false, 13886474930693114932], [7712452662827335977, "tower_layer", false, 12375612145001944731], [7896293946984509699, "bitflags", false, 4035715955649555567], [8606274917505247608, "tracing", false, 13822962143594336692], [9010263965687315507, "http", false, 15833340190285319200], [9538054652646069845, "tokio", false, 4325870631136706026], [14084095096285906100, "http_body", false, 17926065028124312500], [16066129441945555748, "bytes", false, 2617022058346697638], [16900715236047033623, "http_body_util", false, 6146872258357789516]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-5ad78efd777d3141\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}