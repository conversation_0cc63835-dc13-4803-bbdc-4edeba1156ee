fn(axum::extract::State<Arc<AppState>>, Claims, axum::Json<CreateEnvironmentGroupRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {create_environment_group}
fn(axum::extract::State<Arc<AppState>>, Claims, axum::Json<CreateEnvironmentGroupRequest>) -> impl futures::Future<Output = Result<axum::Json<models::ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {create_environment_group}: Handler<_, _>
