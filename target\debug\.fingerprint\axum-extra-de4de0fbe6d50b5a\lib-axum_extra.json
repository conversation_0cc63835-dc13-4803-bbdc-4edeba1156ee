{"rustc": 2830703817519440116, "features": "[\"default\", \"multipart\", \"tracing\", \"typed-header\"]", "declared_features": "[\"async-read-body\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 15657897354478470176, "path": 9230839002199561847, "deps": [[784494742817713399, "tower_service", false, 10334702719707753232], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [4359148418957042248, "axum_core", false, 12941729643191358677], [4891297352905791595, "axum", false, 3869037488119064553], [5695049318159433696, "tower", false, 2034677835280452154], [7435852374066785895, "headers", false, 5496224469549732572], [7712452662827335977, "tower_layer", false, 9331745602861664882], [9010263965687315507, "http", false, 14954775231676038152], [9689903380558560274, "serde", false, 17239657888703164653], [10229185211513642314, "mime", false, 18301127011126638926], [10629569228670356391, "futures_util", false, 5228039834430210376], [12285238697122577036, "fastrand", false, 4507832822799423476], [12757619235593077227, "multer", false, 15864322740133384498], [14084095096285906100, "http_body", false, **************06369], [16066129441945555748, "bytes", false, 14215611657469732184], [16900715236047033623, "http_body_util", false, 2880669227747316702]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-extra-de4de0fbe6d50b5a\\dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}