{"rustc": 2830703817519440116, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 10592228219403531104, "deps": [[1009387600818341822, "matchers", false, 17133563490838698432], [1017461770342116999, "sharded_slab", false, 5834279682477701439], [1359731229228270592, "thread_local", false, 16152357962446084441], [3424551429995674438, "tracing_core", false, 15848792717994607859], [3666196340704888985, "smallvec", false, 11749763988601564173], [3722963349756955755, "once_cell", false, 10983383885145764756], [6981130804689348050, "tracing_serde", false, 1947017490287001800], [8606274917505247608, "tracing", false, 13822962143594336692], [8614575489689151157, "nu_ansi_term", false, 6512137399571373449], [9451456094439810778, "regex", false, 3307808613053810460], [9689903380558560274, "serde", false, 17640726418188598545], [10806489435541507125, "tracing_log", false, 8636458597425319330], [15367738274754116744, "serde_json", false, 1229581184379083770]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-df91d980eab7d3d1\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}